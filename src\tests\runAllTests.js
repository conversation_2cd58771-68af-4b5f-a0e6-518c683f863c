#!/usr/bin/env node

/**
 * Comprehensive test runner for Donation Receipt Frontend Application
 * This script runs all test suites and provides detailed reporting
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test configuration
const testConfig = {
  testDirs: [
    'src/tests/unit/pages/donation-head',
    'src/tests/unit/pages/donation-admin',
    'src/tests/unit/pages/donation-tenant',
    'src/tests/integration/pages/donation-head',
    'src/tests/integration/pages/donation-admin',
    'src/tests/integration/pages/donation-tenant',
    'src/tests/api/donation-head',
    'src/tests/api/donation-admin',
    'src/tests/api/donation-tenant',
  ],
  coverageThreshold: {
    statements: 80,
    branches: 75,
    functions: 80,
    lines: 80,
  },
};

// Utility functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  const border = '='.repeat(60);
  log(border, 'cyan');
  log(`  ${message}`, 'cyan');
  log(border, 'cyan');
}

function logSubHeader(message) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`  ${message}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

function checkTestFiles() {
  log('\n📁 Checking test file structure...', 'yellow');
  
  const missingDirs = [];
  const testFiles = [];
  
  testConfig.testDirs.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    
    if (!fs.existsSync(fullPath)) {
      missingDirs.push(dir);
      return;
    }
    
    const files = fs.readdirSync(fullPath)
      .filter(file => file.endsWith('.test.js'))
      .map(file => path.join(dir, file));
    
    testFiles.push(...files);
  });
  
  if (missingDirs.length > 0) {
    log('❌ Missing test directories:', 'red');
    missingDirs.forEach(dir => log(`   - ${dir}`, 'red'));
  }
  
  log(`✅ Found ${testFiles.length} test files`, 'green');
  testFiles.forEach(file => log(`   - ${file}`, 'green'));
  
  return { testFiles, missingDirs };
}

function runJestTests(pattern = '') {
  log('\n🧪 Running Jest tests...', 'yellow');
  
  try {
    const jestCommand = [
      'npx jest',
      pattern ? `--testPathPattern="${pattern}"` : '',
      '--coverage',
      '--verbose',
      '--detectOpenHandles',
      '--forceExit',
    ].filter(Boolean).join(' ');
    
    log(`Command: ${jestCommand}`, 'blue');
    
    const output = execSync(jestCommand, {
      encoding: 'utf8',
      stdio: 'pipe',
    });
    
    log('✅ All tests passed!', 'green');
    return { success: true, output };
    
  } catch (error) {
    log('❌ Some tests failed:', 'red');
    log(error.stdout || error.message, 'red');
    return { success: false, output: error.stdout || error.message };
  }
}

function runSpecificTestSuite(suiteName) {
  logSubHeader(`Running ${suiteName} tests`);
  
  const patterns = {
    'unit': 'src/tests/unit',
    'integration': 'src/tests/integration',
    'api': 'src/tests/api',
    'donation-head': 'donation-head',
    'donation-admin': 'donation-admin',
    'donation-tenant': 'donation-tenant',
  };
  
  const pattern = patterns[suiteName];
  if (!pattern) {
    log(`❌ Unknown test suite: ${suiteName}`, 'red');
    return { success: false };
  }
  
  return runJestTests(pattern);
}

function generateTestReport(results) {
  logSubHeader('Test Report Summary');
  
  const totalSuites = Object.keys(results).length;
  const passedSuites = Object.values(results).filter(r => r.success).length;
  const failedSuites = totalSuites - passedSuites;
  
  log(`📊 Test Suites: ${totalSuites}`, 'bright');
  log(`✅ Passed: ${passedSuites}`, 'green');
  log(`❌ Failed: ${failedSuites}`, failedSuites > 0 ? 'red' : 'green');
  
  // Detailed results
  Object.entries(results).forEach(([suite, result]) => {
    const status = result.success ? '✅' : '❌';
    const color = result.success ? 'green' : 'red';
    log(`${status} ${suite}`, color);
  });
  
  return { totalSuites, passedSuites, failedSuites };
}

function checkCoverage() {
  logSubHeader('Coverage Analysis');
  
  const coverageFile = path.join(process.cwd(), 'coverage/coverage-summary.json');
  
  if (!fs.existsSync(coverageFile)) {
    log('⚠️  Coverage report not found', 'yellow');
    return false;
  }
  
  try {
    const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    const total = coverage.total;
    
    log('📈 Coverage Summary:', 'bright');
    
    Object.entries(testConfig.coverageThreshold).forEach(([metric, threshold]) => {
      const actual = total[metric].pct;
      const status = actual >= threshold ? '✅' : '❌';
      const color = actual >= threshold ? 'green' : 'red';
      
      log(`${status} ${metric}: ${actual}% (threshold: ${threshold}%)`, color);
    });
    
    return true;
  } catch (error) {
    log('❌ Error reading coverage report:', 'red');
    log(error.message, 'red');
    return false;
  }
}

function validateTestPatterns() {
  logSubHeader('Validating Test Patterns');
  
  const patterns = [
    {
      name: 'Test file naming',
      check: () => {
        const testFiles = [];
        testConfig.testDirs.forEach(dir => {
          const fullPath = path.join(process.cwd(), dir);
          if (fs.existsSync(fullPath)) {
            const files = fs.readdirSync(fullPath);
            testFiles.push(...files.map(f => path.join(dir, f)));
          }
        });
        
        const invalidFiles = testFiles.filter(file => !file.endsWith('.test.js'));
        return {
          success: invalidFiles.length === 0,
          message: invalidFiles.length > 0 ? `Invalid test files: ${invalidFiles.join(', ')}` : 'All test files follow naming convention',
        };
      },
    },
    {
      name: 'Test structure',
      check: () => {
        const requiredDirs = [
          'src/tests/unit',
          'src/tests/integration',
          'src/tests/api',
          'src/tests/utils',
          'src/tests/mocks',
        ];
        
        const missingDirs = requiredDirs.filter(dir => !fs.existsSync(path.join(process.cwd(), dir)));
        return {
          success: missingDirs.length === 0,
          message: missingDirs.length > 0 ? `Missing directories: ${missingDirs.join(', ')}` : 'All required directories exist',
        };
      },
    },
    {
      name: 'Utility files',
      check: () => {
        const requiredFiles = [
          'src/tests/utils/donationTestUtils.js',
          'src/tests/mocks/donationMocks.js',
        ];
        
        const missingFiles = requiredFiles.filter(file => !fs.existsSync(path.join(process.cwd(), file)));
        return {
          success: missingFiles.length === 0,
          message: missingFiles.length > 0 ? `Missing files: ${missingFiles.join(', ')}` : 'All utility files exist',
        };
      },
    },
  ];
  
  patterns.forEach(pattern => {
    const result = pattern.check();
    const status = result.success ? '✅' : '❌';
    const color = result.success ? 'green' : 'red';
    log(`${status} ${pattern.name}: ${result.message}`, color);
  });
}

// Main execution
async function main() {
  logHeader('Donation Receipt Frontend - Test Suite Runner');
  
  // Check test file structure
  const { testFiles, missingDirs } = checkTestFiles();
  
  if (missingDirs.length > 0) {
    log('\n❌ Cannot proceed with missing test directories', 'red');
    process.exit(1);
  }
  
  // Validate test patterns
  validateTestPatterns();
  
  // Run test suites
  const testSuites = ['unit', 'integration', 'api'];
  const results = {};
  
  for (const suite of testSuites) {
    results[suite] = runSpecificTestSuite(suite);
  }
  
  // Generate report
  const summary = generateTestReport(results);
  
  // Check coverage
  checkCoverage();
  
  // Final status
  logHeader('Final Results');
  
  if (summary.failedSuites === 0) {
    log('🎉 All tests passed successfully!', 'green');
    log('✅ Test suite implementation is complete and follows best practices', 'green');
  } else {
    log('❌ Some test suites failed', 'red');
    log('🔧 Please review and fix the failing tests', 'yellow');
  }
  
  // Exit with appropriate code
  process.exit(summary.failedSuites === 0 ? 0 : 1);
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  log('Donation Receipt Frontend Test Runner', 'bright');
  log('\nUsage:', 'yellow');
  log('  npm run test:all                 # Run all tests');
  log('  node src/tests/runAllTests.js    # Run this script directly');
  log('\nOptions:', 'yellow');
  log('  --help, -h                       # Show this help message');
  log('  --unit                           # Run only unit tests');
  log('  --integration                    # Run only integration tests');
  log('  --api                            # Run only API tests');
  process.exit(0);
}

if (args.includes('--unit')) {
  runSpecificTestSuite('unit');
} else if (args.includes('--integration')) {
  runSpecificTestSuite('integration');
} else if (args.includes('--api')) {
  runSpecificTestSuite('api');
} else {
  main().catch(error => {
    log('❌ Unexpected error:', 'red');
    log(error.message, 'red');
    process.exit(1);
  });
}
