import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { renderWithProviders, mockStatsData, mockApiResponse } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the complete donation admin dashboard
const DonationAdminDashboard = () => {
  const [stats, setStats] = React.useState(null);
  const [loadingStats, setLoadingStats] = React.useState(true);
  const [ngoSignupData, setNgoSignupData] = React.useState(null);
  const [donationData, setDonationData] = React.useState(null);
  const [distributionData, setDistributionData] = React.useState(null);
  const [selectedNgoTab, setSelectedNgoTab] = React.useState(0);
  const [selectedDonationTab, setSelectedDonationTab] = React.useState(0);
  const [error, setError] = React.useState(null);

  const tabLabels = ['Yearly', 'Monthly', 'Weekly', 'Daily'];
  const tabKeys = ['yearly', 'monthly', 'weekly', 'daily'];

  // Mock data
  const mockNgoData = {
    yearly: { series: [{ name: 'NGO Signups', data: [15, 22, 18, 25, 30, 28, 35] }], categories: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'] },
    monthly: { series: [{ name: 'NGO Signups', data: [2, 1, 3, 2, 3, 1] }], categories: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
    weekly: { series: [{ name: 'NGO Signups', data: [0, 1, 2, 1, 1, 0, 1] }], categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    daily: { series: [{ name: 'NGO Signups', data: [0, 0, 1, 0, 0, 0] }], categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'] },
  };

  const mockDonationTrends = {
    yearly: { series: [{ name: 'Total Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }], categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
    monthly: { series: [{ name: 'Total Donations', data: [200, 150, 300, 250, 350, 100] }], categories: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'] },
    weekly: { series: [{ name: 'Total Donations', data: [50, 30, 80, 60, 90, 70, 100] }], categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    daily: { series: [{ name: 'Total Donations', data: [10, 15, 5, 20, 12, 25, 8] }], categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'] },
  };

  const mockDistribution = {
    series: [4400, 5500, 3500, 4300, 2400],
    labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
  };

  // API calls
  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/dashboard/stats');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch stats');
    }
  };

  const fetchNgoSignupData = async () => {
    try {
      const response = await axios.get('/api/dashboard/ngo-signups');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch NGO signup data');
    }
  };

  const fetchDonationTrends = async () => {
    try {
      const response = await axios.get('/api/dashboard/donation-trends');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch donation trends');
    }
  };

  const fetchDistributionData = async () => {
    try {
      const response = await axios.get('/api/dashboard/donation-distribution');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch distribution data');
    }
  };

  // Load all data
  React.useEffect(() => {
    const loadAllData = async () => {
      setLoadingStats(true);
      setError(null);
      
      try {
        const [statsData, ngoData, donationTrendsData, distributionData] = await Promise.all([
          fetchStats(),
          fetchNgoSignupData(),
          fetchDonationTrends(),
          fetchDistributionData(),
        ]);

        setStats(statsData);
        setNgoSignupData(ngoData);
        setDonationData(donationTrendsData);
        setDistributionData(distributionData);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        setError(error.message);
        // Set mock data as fallback
        setStats(mockStatsData());
        setNgoSignupData(mockNgoData);
        setDonationData(mockDonationTrends);
        setDistributionData(mockDistribution);
      } finally {
        setLoadingStats(false);
      }
    };

    loadAllData();
  }, []);

  const refreshData = async () => {
    setLoadingStats(true);
    try {
      const statsData = await fetchStats();
      setStats(statsData);
    } catch (error) {
      setError('Failed to refresh data');
    } finally {
      setLoadingStats(false);
    }
  };

  const currencySymbol = '₹';

  if (error && !stats) {
    return (
      <div data-testid="error-state">
        <h2>Error Loading Dashboard</h2>
        <p data-testid="error-message">{error}</p>
        <button data-testid="retry-button" onClick={refreshData}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div data-testid="donation-admin-dashboard">
      <div data-testid="dashboard-header">
        <h1>Admin Dashboard</h1>
        <button data-testid="refresh-button" onClick={refreshData} disabled={loadingStats}>
          {loadingStats ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {error && (
        <div data-testid="error-banner" role="alert">
          <p>Warning: {error}</p>
        </div>
      )}

      {/* Stats Overview */}
      <div data-testid="stats-overview">
        <h2>Statistics Overview</h2>
        <div data-testid="stats-grid">
          <div data-testid="stat-total-donations">
            <h3>Total Donations</h3>
            {loadingStats ? (
              <div data-testid="loading-total">Loading...</div>
            ) : (
              <div data-testid="total-value">
                {currencySymbol}{stats?.totalDonations?.toLocaleString() || '0'}
              </div>
            )}
          </div>

          <div data-testid="stat-unique-donors">
            <h3>Unique Donors</h3>
            {loadingStats ? (
              <div data-testid="loading-donors">Loading...</div>
            ) : (
              <div data-testid="donors-value">{stats?.uniqueDonors || '0'}</div>
            )}
          </div>

          <div data-testid="stat-recent-donations">
            <h3>Last 30 Days</h3>
            {loadingStats ? (
              <div data-testid="loading-recent">Loading...</div>
            ) : (
              <div data-testid="recent-value">
                {stats?.last30DaysDonations ?? '0'} donations
              </div>
            )}
          </div>

          <div data-testid="stat-average-donation">
            <h3>Average Donation</h3>
            {loadingStats ? (
              <div data-testid="loading-average">Loading...</div>
            ) : (
              <div data-testid="average-value">
                {currencySymbol}{stats?.averageDonation?.toLocaleString() || '0'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div data-testid="charts-section">
        <h2>Analytics</h2>
        
        {/* NGO Signup Analytics */}
        <div data-testid="ngo-analytics">
          <h3>NGO Registration Trends</h3>
          <div data-testid="ngo-tabs">
            {tabLabels.map((label, index) => (
              <button
                key={index}
                data-testid={`ngo-tab-${label.toLowerCase()}`}
                onClick={() => setSelectedNgoTab(index)}
                className={selectedNgoTab === index ? 'active' : ''}
                aria-pressed={selectedNgoTab === index}
              >
                {label}
              </button>
            ))}
          </div>
          <div data-testid="ngo-chart">
            {ngoSignupData ? (
              <div data-testid="mock-chart" data-type="bar" data-period={tabKeys[selectedNgoTab]}>
                NGO Signup Chart - {tabLabels[selectedNgoTab]}
              </div>
            ) : (
              <div data-testid="chart-loading">Loading chart...</div>
            )}
          </div>
        </div>

        {/* Donation Trends Analytics */}
        <div data-testid="donation-analytics">
          <h3>Donation Growth Trends</h3>
          <div data-testid="donation-tabs">
            {tabLabels.map((label, index) => (
              <button
                key={index}
                data-testid={`donation-tab-${label.toLowerCase()}`}
                onClick={() => setSelectedDonationTab(index)}
                className={selectedDonationTab === index ? 'active' : ''}
                aria-pressed={selectedDonationTab === index}
              >
                {label}
              </button>
            ))}
          </div>
          <div data-testid="donation-chart">
            {donationData ? (
              <div data-testid="mock-chart" data-type="line" data-period={tabKeys[selectedDonationTab]}>
                Donation Trends Chart - {tabLabels[selectedDonationTab]}
              </div>
            ) : (
              <div data-testid="chart-loading">Loading chart...</div>
            )}
          </div>
        </div>

        {/* Distribution Analytics */}
        <div data-testid="distribution-analytics">
          <h3>Donation Distribution by Category</h3>
          <div data-testid="distribution-chart">
            {distributionData ? (
              <div data-testid="mock-chart" data-type="pie">
                Distribution Pie Chart
              </div>
            ) : (
              <div data-testid="chart-loading">Loading chart...</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

describe('Donation Admin Dashboard Integration', () => {
  const mockStatsResponse = {
    totalDonations: 512986,
    uniqueDonors: 20,
    last30DaysDonations: 0,
    averageDonation: 25649,
  };

  const mockUser = {
    id: '1',
    name: 'Admin User',
    organisationCategory: 'SUPER_ADMIN',
    orgId: 'admin-org',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockAxios.get.mockImplementation((url) => {
      switch (url) {
        case '/api/dashboard/stats':
          return Promise.resolve(mockApiResponse(mockStatsResponse));
        case '/api/dashboard/ngo-signups':
          return Promise.resolve(mockApiResponse({}));
        case '/api/dashboard/donation-trends':
          return Promise.resolve(mockApiResponse({}));
        case '/api/dashboard/donation-distribution':
          return Promise.resolve(mockApiResponse({}));
        default:
          return Promise.reject(new Error('Unknown endpoint'));
      }
    });
  });

  describe('Initial Load and Data Fetching', () => {
    it('loads all dashboard data on mount', async () => {
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      expect(screen.getByTestId('loading-total')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('total-value')).toBeInTheDocument();
      });

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/stats');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/ngo-signups');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/donation-trends');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/donation-distribution');
    });

    it('displays loaded stats data correctly', async () => {
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByText('₹512,986')).toBeInTheDocument();
        expect(screen.getByText('20')).toBeInTheDocument();
        expect(screen.getByText('0 donations')).toBeInTheDocument();
        expect(screen.getByText('₹25,649')).toBeInTheDocument();
      });
    });

    it('loads charts after data is fetched', async () => {
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('ngo-chart')).toBeInTheDocument();
        expect(screen.getByTestId('donation-chart')).toBeInTheDocument();
        expect(screen.getByTestId('distribution-chart')).toBeInTheDocument();
      });
    });
  });

  describe('Interactive Chart Navigation', () => {
    it('switches between NGO signup chart periods', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('ngo-analytics')).toBeInTheDocument();
      });

      // Initially shows yearly
      expect(screen.getByText('NGO Signup Chart - Yearly')).toBeInTheDocument();

      // Switch to monthly
      await user.click(screen.getByTestId('ngo-tab-monthly'));
      expect(screen.getByText('NGO Signup Chart - Monthly')).toBeInTheDocument();

      // Switch to weekly
      await user.click(screen.getByTestId('ngo-tab-weekly'));
      expect(screen.getByText('NGO Signup Chart - Weekly')).toBeInTheDocument();
    });

    it('switches between donation trend chart periods', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('donation-analytics')).toBeInTheDocument();
      });

      // Initially shows yearly
      expect(screen.getByText('Donation Trends Chart - Yearly')).toBeInTheDocument();

      // Switch to daily
      await user.click(screen.getByTestId('donation-tab-daily'));
      expect(screen.getByText('Donation Trends Chart - Daily')).toBeInTheDocument();
    });

    it('maintains independent tab states for different charts', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('ngo-analytics')).toBeInTheDocument();
      });

      // Set NGO chart to monthly
      await user.click(screen.getByTestId('ngo-tab-monthly'));
      expect(screen.getByText('NGO Signup Chart - Monthly')).toBeInTheDocument();

      // Set donation chart to weekly
      await user.click(screen.getByTestId('donation-tab-weekly'));
      expect(screen.getByText('Donation Trends Chart - Weekly')).toBeInTheDocument();

      // NGO chart should still be monthly
      expect(screen.getByText('NGO Signup Chart - Monthly')).toBeInTheDocument();
    });
  });

  describe('Data Refresh Functionality', () => {
    it('refreshes stats data when refresh button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      // Clear previous calls
      mockAxios.get.mockClear();

      await user.click(screen.getByTestId('refresh-button'));

      expect(screen.getByText('Refreshing...')).toBeInTheDocument();
      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/stats');
    });

    it('disables refresh button during refresh', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('refresh-button'));

      const refreshButton = screen.getByTestId('refresh-button');
      expect(refreshButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('displays error state when all APIs fail', async () => {
      mockAxios.get.mockRejectedValue(new Error('Network error'));

      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('error-state')).toBeInTheDocument();
      });

      expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('error-message')).toHaveTextContent('Failed to fetch stats');
    });

    it('shows fallback data when APIs fail', async () => {
      mockAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        // Should show fallback data instead of error state
        expect(screen.getByTestId('donation-admin-dashboard')).toBeInTheDocument();
      });
    });

    it('allows retry when in error state', async () => {
      const user = userEvent.setup();
      mockAxios.get.mockRejectedValueOnce(new Error('Network error'))
                  .mockResolvedValue(mockApiResponse(mockStatsResponse));

      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('error-state')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('retry-button'));

      await waitFor(() => {
        expect(screen.getByTestId('donation-admin-dashboard')).toBeInTheDocument();
      });
    });

    it('shows error banner for partial failures', async () => {
      mockAxios.get.mockImplementation((url) => {
        if (url === '/api/dashboard/stats') {
          return Promise.resolve(mockApiResponse(mockStatsResponse));
        }
        return Promise.reject(new Error('Chart data failed'));
      });

      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('error-banner')).toBeInTheDocument();
      });

      expect(screen.getByText(/Warning:/)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes for tab navigation', async () => {
      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('ngo-tab-yearly')).toBeInTheDocument();
      });

      const yearlyTab = screen.getByTestId('ngo-tab-yearly');
      expect(yearlyTab).toHaveAttribute('aria-pressed', 'true');

      const monthlyTab = screen.getByTestId('ngo-tab-monthly');
      expect(monthlyTab).toHaveAttribute('aria-pressed', 'false');
    });

    it('has proper alert role for error messages', async () => {
      mockAxios.get.mockImplementation((url) => {
        if (url === '/api/dashboard/stats') {
          return Promise.resolve(mockApiResponse(mockStatsResponse));
        }
        return Promise.reject(new Error('Chart data failed'));
      });

      renderWithProviders(<DonationAdminDashboard />, { user: mockUser });

      await waitFor(() => {
        const errorBanner = screen.getByTestId('error-banner');
        expect(errorBanner).toHaveAttribute('role', 'alert');
      });
    });
  });
});
