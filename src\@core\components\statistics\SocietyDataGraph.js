import React from "react";
import dynamic from "next/dynamic";
import { useTheme, useMediaQuery } from "@mui/material";

// Dynamically import ApexChart to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const SocietyDataGraph = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm")); // Check if the screen size is mobile

  const zones = [
    "Western",
    "Central",
    "Navi Mumbai",
    "SOBO",
    "Thane",
    "Harbour",
    "Mumbai",
    "Panvel",
    "Palghar",
    "Pune",
    "Blank",
  ];

  // Data for Red and Pro societies
  const redSocieties = [87, 56, 12, 11, 5, 4, 3, 0, 0, 0, 0]; // Red Location totals
  const proSocieties = [600, 295, 31, 115, 100, 61, 68, 11, 1, 1, 3]; // Pro Location totals

  // Calculate the differences
  const differences = proSocieties.map((pro, index) => pro - redSocieties[index]);

  const options = {
    chart: {
      type: "bar",
      stacked: false,
      toolbar: {
        show: false, // Hide toolbar for mobile
      },
    },
    title: {
      text: "Societies by Zone",
      align: "center",
      style: {
        fontSize: isMobile ? "10px" : "16px", // Adjust font size for mobile
      },
    },
    xaxis: {
      categories: zones,
      labels: {
        style: {
          fontSize: isMobile ? "10px" : "12px", // Adjust x-axis label size for mobile
        },
      },
    },
    yaxis: {
      title: {
        text: "Number of Societies",
        style: {
          fontSize: isMobile ? "10px" : "12px", // Adjust y-axis title font size
        },
      },
      labels: {
        style: {
          fontSize: isMobile ? "10px" : "12px", // Adjust y-axis label font size
        },
      },
    },
    legend: {
      position: "top",
      labels: {
        style: {
          fontSize: isMobile ? "10px" : "12px", // Adjust legend font size
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
    plotOptions: {
      bar: {
        columnWidth: isMobile ? "70%" : "50%", // Adjust bar width for mobile
      },
    },
    colors: ["rgb(60, 252, 34)", "#3788d8", "rgba(254, 176, 25, 0.85)"],
  };

  const series = [
    {
      name: "RED",
      data: redSocieties,
    },
    {
      name: "PRO",
      data: proSocieties,
    },
    {
      name: "Difference (PRO - RED)",
      data: differences,
    },
  ];

  return (
    <div
      style={{
        width: isMobile ? "90%" : "600px", // Adjust chart width for mobile
        margin: "0 auto", // Center the chart
        overflowX: "auto", // Allow horizontal scrolling if needed
      }}
    >
      <ApexChart
        options={options}
        series={series}
        type="bar"
        height={isMobile ? 300 : 400} // Adjust height for mobile
      />
    </div>
  );
};

export default SocietyDataGraph;
