import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const FunnelChart = () => {
  const state = {
    series: [
      {
        name: "SR ",
        data: [1500, 1200, 1000, 850, 700, 550, 400, 250,170], // Example data
      },
    ],
    options: {
      chart: {
        type: 'bar',
        height: 350,
      },
      plotOptions: {
        bar: {
          borderRadius: 0,
          horizontal: true,
          barHeight: '80%',
          isFunnel: true,
        },
      },
      dataLabels: {
        enabled: true,
        formatter: function (val, opt) {
          return val; // Display only the value in the bar
        },
        style: {
          fontSize: '12px',
        },
        offsetX: 0, // Adjust the position of the value label inside the bar
      },
      title: {
        text: 'Service Requisition Stages Funnel',
        align: 'center',
      },
      xaxis: {
        categories: [
          'Stage 1: Service Requisition Raised',
          'Stage 2: Inquiry and Initial Engagement',
          'Stage 3: Quotation Submission',
          'Stage 4: Vendor Selection',
          'Stage 5: Work Order Creation',
          'Stage 6: Payment Processing',
          'Stage 7: Implementation',
          'Stage 8: Payment Completion',
          'Stage 9: Closure'
        ],
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '8px',
          },
        },
      },
      legend: {
        show: false,
      },
      tooltip: {
        y: {
          formatter: function(val, { series, seriesIndex, dataPointIndex, w }) {
            return `${w.globals.labels[dataPointIndex]}: ${val}`;
          },
        },
      },
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="bar" height={350} />
      </div>
    </div>
  );
};

export default FunnelChart;
