import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import Grid from "@mui/material/Grid";

const ApexChart = dynamic(() => import('react-apexcharts'), {
  ssr: false
});

const SparkLinesChart = () => {
  // Function to randomize data
  const randomizeArray = (arg) => {
    var array = arg.slice();
    var currentIndex = array.length, temporaryValue, randomIndex;

    while (0 !== currentIndex) {
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex -= 1;
      temporaryValue = array[currentIndex];
      array[currentIndex] = array[randomIndex];
      array[randomIndex] = temporaryValue;
    }
    return array;
  };

  // Placeholder for sparkline data
  const sparklineData = [23, 44, 56, 78, 13, 43, 10, 34, 25, 19];

  // Initial state setup using useState hook
  const [chartData, setChartData] = useState({
    series: [{ data: randomizeArray(sparklineData) }],
    options: {
      chart: { type: 'area', height: 140, sparkline: { enabled: true } },
      stroke: { curve: 'straight' },
      fill: { opacity: 0.3 },
      xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        tooltip: { enabled: false }
      },
      yaxis: { min: 0 },
      colors: ['#DCE6EC'],
      title: { text: '$424,652', offsetX: 0, style: { fontSize: '24px' } },
      subtitle: { text: 'Sales', offsetX: 0, style: { fontSize: '14px' } }
    },
    seriesSpark2: [{ data: randomizeArray(sparklineData) }],
    optionsSpark2: {
      chart: { type: 'area', height: 140, sparkline: { enabled: true } },
      stroke: { curve: 'straight' },
      fill: { opacity: 0.3 },
      yaxis: { min: 0 },
      colors: ['#DCE6EC'],
      title: { text: '$235,312', offsetX: 0, style: { fontSize: '24px' } },
      subtitle: { text: 'Expenses', offsetX: 0, style: { fontSize: '14px' } }
    },
    seriesSpark3: [{ data: randomizeArray(sparklineData) }],
    optionsSpark3: {
      chart: { type: 'area', height: 140, sparkline: { enabled: true } },
      stroke: { curve: 'straight' },
      fill: { opacity: 0.3 },
      xaxis: { crosshairs: { width: 1 } },
      yaxis: { min: 0 },
      title: { text: '$135,965', offsetX: 0, style: { fontSize: '24px' } },
      subtitle: { text: 'Profits', offsetX: 0, style: { fontSize: '14px' } }
    },
    series1: [{ data: [25, 66, 41, 89, 63, 25, 44, 12, 36, 9, 54] }],
    options1: {
      chart: { type: 'line', width: 100, height: 35, sparkline: { enabled: true } },
      tooltip: {
        fixed: { enabled: false },
        x: { show: false },
        y: { title: { formatter: () => '' } },
        marker: { show: false }
      }
    },
    series2: [{ data: [12, 14, 2, 47, 42, 15, 47, 75, 65, 19, 14] }],
    options2: {
      chart: { type: 'line', width: 100, height: 35, sparkline: { enabled: true } },
      tooltip: {
        fixed: { enabled: false },
        x: { show: false },
        ///y: { title: { formatter: () should use the 'formatter' function without any specific name, },
        marker: { show: false }
      }
    },
    series3: [{ data: [43, 32, 12, 9] }],
    options3: {
      chart: { type: 'pie', width: 40, height: 40, sparkline: { enabled: true } },
      stroke: { width: 1 },
      tooltip: { fixed: { enabled: false } }
    },
    series4: [{ data: [43, 32, 12, 9] }],
    options4: {
      chart: { type: 'donut', width: 40, height: 40, sparkline: { enabled: true } },
      stroke: { width: 1 },
      tooltip: { fixed: { enabled: false } }
    },
    series5: [{ data: [25, 66, 41, 89, 63, 25, 44, 12, 36, 9, 54] }],
    options5: {
      chart: { type: 'bar', width: 100, height: 35, sparkline: { enabled: true } },
      plotOptions: { bar: { columnWidth: '80%' } },
      labels: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
      xaxis: { crosshairs: { width: 1 } },
      tooltip: {
        fixed: { enabled: false },
        x: { show: false },
        y: { title: { formatter: () => '' } },
        marker: { show: false }
      }
    },
    series6: [{ data: [12, 14, 2, 47, 42, 15, 47, 75, 65, 19, 14] }],
    options6: {
      chart: { type: 'bar', width: 100, height: 35, sparkline: { enabled: true } },
      plotOptions: { bar: { columnWidth: '80%' } },
      labels: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
      xaxis: { crosshairs: { width: 1 } },
      tooltip: {
        fixed: { enabled: false },
        x: { show: false },
        y: { title: { formatter: () => '' } },
        marker: { show: false }
      }
    },
    series7: [{ data: [45] }],
    options7: {
      chart: { type: 'radialBar', width: 50, height: 50, sparkline: { enabled: true } },
      dataLabels: { enabled: false },
      plotOptions: {
        radialBar: {
          hollow: { margin: 0, size: '50%' },
          track: { margin: 0 },
          dataLabels: { show: false }
        }
      }
    },
    series8: [{ data: [53, 67] }],
    options8: {
      chart: { type: 'radialBar', width: 40, height: 40, sparkline: { enabled: true } },
      dataLabels: { enabled: false },
      plotOptions: {
        radialBar: {
          hollow: { margin: 0, size: '50%' },
          track: { margin: 1 },
          dataLabels: { show: false }
        }
      }
    },
  });

  return (
    <Grid container spacing={3}>
    <Grid item xs={12} md={4}>
      <div id="chart-spark1">
        <ApexChart options={chartData.options} series={chartData.series} type="area" height={160} />
      </div>
    </Grid>
    <Grid item xs={12} md={4}>
      <div id="chart-spark2">
        <ApexChart options={chartData.optionsSpark2} series={chartData.seriesSpark2} type="area" height={160} />
      </div>
    </Grid>
    <Grid item xs={12} md={4}>
      <div id="chart-spark3">
        <ApexChart options={chartData.optionsSpark3} series={chartData.seriesSpark3} type="area" height={160} />
      </div>
    </Grid>
  </Grid>
  );
};

export default SparkLinesChart;
