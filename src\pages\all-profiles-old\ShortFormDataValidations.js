import * as yup from "yup";

export const ShortFormDataValidations = (fields) => {
  const fieldsArray = Array.isArray(fields) ? fields : [];
  return yup.object().shape({
    firstName: yup.string().when("$fields", {
      is: () => fieldsArray.includes("firstName"),
      then: yup
        .string()
        .required("First Name is required")
        .nullable()
        .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "Multiple spaces are not allowed")
        .max(30, "First Name must not exceed 30 characters")
        .min(3, "First Name must have at least 3 characters"),
      otherwise: yup.string().notRequired().nullable(),
    }),
    lastName: yup
      .string()

      .when("$fields", {
        is: () => fieldsArray.includes("lastName"),
        then: yup
          .string()
          .required("LastName is required")
          .nullable()
          .matches(
            /^([A-Za-z]+ ?)*[A-Za-z]+$/,
            "Multiple spaces are not allowed"
          )
          .max(30, "LastName must not exceed 30 characters")
          .min(3, "LastName must have at least 3 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
    mobileNumber: yup
      .string()
      .when("$fields", {
        is: () => fieldsArray.includes("mobileNumber"),
        then: yup
          .string()
          .required("Mobile number is required")
          .nullable()
          .matches(
            /^(?:\+91\s?)?[6-9]\d{9}$/,
            "Please enter a valid contact number"
          )
          .max(13, "Contact number must not exceed 13 characters"),
        otherwise: yup.string().notRequired().nullable(),
      }),
  });
};

export default ShortFormDataValidations;

export { yup };
