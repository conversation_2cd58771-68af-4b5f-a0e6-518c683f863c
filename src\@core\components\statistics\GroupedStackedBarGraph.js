import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const GroupedStackedBarGraph = () => {
  const state = {
    series: [
      {
        name: 'Q1 Budget',
        group: 'budget',
        data: [44000, 55000, 41000, 67000, 22000]
      },
      {
        name: 'Q1 Actual',
        group: 'actual',
        data: [48000, 50000, 40000, 65000, 25000]
      },
      {
        name: 'Q2 Budget',
        group: 'budget',
        data: [13000, 36000, 20000, 8000, 13000]
      },
      {
        name: 'Q2 Actual',
        group: 'actual',
        data: [20000, 40000, 25000, 10000, 12000]
      }
    ],
    options: {
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,
      },
      stroke: {
        width: 1,
        colors: ['#fff']
      },
      dataLabels: {
        formatter: (val) => {
          return val / 1000 + 'K'; // Format values to display in thousands
        }
      },
      plotOptions: {
        bar: {
          horizontal: true
        }
      },
      xaxis: {
        categories: [
          'Accounts',
          'Administration',
          'Social Media & Marketing',
          'Business Development',
          'Sales'
        ],
        labels: {
          formatter: (val) => {
            return val / 1000 + 'K'; // Format labels to display in thousands
          }
        }
      },
      fill: {
        opacity: 1,
      },
      colors: ['#80c7fd', '#008FFB', '#80f1cb', '#00E396'], // Custom colors for groups
      legend: {
        position: 'top',
        horizontalAlign: 'left'
      }
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="bar" height={350} />
      </div>
    </div>
  );
};

export default GroupedStackedBarGraph;
