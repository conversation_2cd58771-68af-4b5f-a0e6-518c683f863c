// ** Custom Navigation Components
import LandingPageHorizontalNavGroup from './LandingPageHorizontalNavGroup'
import LandingPageHorizontalNavLink from './LandingPageHorizontalNavLink'

const resolveComponent = item => {
  if (item.children) return LandingPageHorizontalNavGroup

  return LandingPageHorizontalNavLink
}

const LandingPageHorizontalNavItems = props => {
  const RenderMenuItems = props.horizontalNavItems?.map((item, index) => {
    const TagName = resolveComponent(item)

    return <TagName {...props} key={index} item={item} />
  })

  return <>{RenderMenuItems}</>
}

export default LandingPageHorizontalNavItems
