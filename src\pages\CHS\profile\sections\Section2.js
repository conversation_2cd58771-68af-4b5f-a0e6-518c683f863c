// ** React Imports
import { forwardRef, useContext, useState } from "react";

// ** MUI Imports
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import CardHeader from "@mui/material/CardHeader";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import Typography from "@mui/material/Typography";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { useAuth } from "src/hooks/useAuth";
import { yupResolver } from "@hookform/resolvers/yup";
import SocietyValidationsSection1 from "./SocietyValidationsSection1";
import { AuthContext } from "src/context/AuthContext";


// ** Icon Imports

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section2 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();

  const fields=["builtUpAreaResidential","builtUpAreaCommercial","noOfResidence","noOfCommercial"]

  const {user} = useContext(AuthContext)
  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(SocietyValidationsSection1(fields)),
    mode: "onChange",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  async function submit(data) {
    setIsSubmitting(true)
    try {
      const trimmedData = Object.fromEntries(
        Object.entries(data).map(([key, value]) => [
          key,
          typeof value === "string" ? value.trim() : value,
        ])
      );
      const hasWhiteSpace = Object.values(trimmedData).some(
        (value) => typeof value === "string" && value === ""
      );
      if (hasWhiteSpace) {
        toast.error("Fields cannot contain only white spaces");
        return;
      }
  
      const userUniqueId =
        formData && formData.userId !== undefined ? formData?.userId : user?.id;
  
      const response = await auth.updateEntity(trimmedData, userUniqueId)
      onCancel();
    } catch {
      console.error("Submission failed:", error);
      toast.error("An error occurred while submitting the form.")
    } finally {
      setIsSubmitting(false);
    }
    
  }

  return (
    <Card>
      <CardHeader
        title={
          <Typography variant="body1" sx={{ mt: 6, mb: 1 }}>
            <strong>Total Plot Area:</strong>
          </Typography>
        }
      />
      <CardContent>
        <Grid container spacing={6}>
         
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="builtUpAreaResidential"
                control={control}
                defaultValue={formData?.builtUpAreaResidential}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Built-up area Residential (Sq Mtrs)"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.builtUpAreaResidential)}
                    helperText={errors.builtUpAreaResidential?.message}
                    size='small'
                    aria-describedby="validation-basic-built-up-area-residential"
                    onBlur={(event) => {
                      if (field.value && isNaN(field.value)) {
                        errors.fsi_PermissibleFsi = { message: "Only numbers are allowed" };
                      }
                    }}
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      pattern: '[0-9]*',
                      maxLength: 6, // allow up to 6 digits
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="builtUpAreaCommercial"
                control={control}
                defaultValue={formData?.builtUpAreaCommercial}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Built-up area Commercial (Sq Mtrs)"
                    size='small'
                    placeholder="Sq Mtrs"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.builtUpAreaCommercial)}
                    helperText={errors.builtUpAreaCommercial?.message}
                    aria-describedby="validation-basic-built-up-area-commercial"
                    onBlur={(event) => {
                      if (field.value && isNaN(field.value)) {
                        errors.fsi_PermissibleFsi = { message: "Only numbers are allowed" };
                      }
                    }}
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      pattern: '[0-9]*',
                      maxLength: 6, // allow up to 6 digits
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        </Grid>
      </CardContent>

      <CardHeader
        title={
          <Typography variant="body1" sx={{ mt: 6, mb: 1 }}>
            <strong>No.of Residents/Commercial:</strong>
          </Typography>
        }
      />
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="noOfResidence"
                control={control}
                defaultValue={formData?.noOfResidence}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="number"
                    label="No. of Residents"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.noOfResidence)}
                    helperText={errors.noOfResidence?.message}
                    size='small'
                    aria-describedby="validation-basic-no-of-residence"
                    onBlur={(event) => {
                      if (field.value && isNaN(field.value)) {
                        errors.fsi_PermissibleFsi = { message: "Only numbers are allowed" };
                      }
                    }}
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      pattern: '[0-9]*',
                      maxLength: 6, // allow up to 6 digits
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="noOfCommercial"
                control={control}
                defaultValue={formData?.noOfCommercial}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="number"
                    label="No. of Commercial"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.noOfCommercial)}
                    helperText={errors.noOfCommercial?.message}
                    size='small'
                    aria-describedby="validation-basic-no-of-commercial"
                    onBlur={(event) => {
                      if (field.value && isNaN(field.value)) {
                        errors.fsi_PermissibleFsi = { message: "Only numbers are allowed" };
                      }
                    }}
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      pattern: '[0-9]*',
                      maxLength: 6, // allow up to 6 digits
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSubmitting}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default Section2;