import { useContext, useState } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TextField,
  IconButton,
  Checkbox,
  Autocomplete,
  DialogContentText,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import {
  Upload,
  Edit as EditIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { useDropzone } from "react-dropzone";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "../permission/RBACContext";
import * as XLSX from "xlsx";
import { getFileUploadHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";

const SYSTEM_FIELDS = [
  { label: "S.No", value: "s.no" },
  { label: "Name", value: "name", required: true },
  { label: "Email", value: "email", required: true },
  { label: "Mobile Number", value: "mobile", required: true },
  { label: "PAN Number", value: "panNumber", required: true },
  { label: "State", value: "state" },
  { label: "Pin Code", value: "pinCode" },
  { label: "Address", value: "address" },
];

const steps = ["Upload File", "Map Columns", "Review & Import Data"];

const ImportExportDonors = ({ open, onClose }) => {
  const { user } = useContext(AuthContext);
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [importFile, setImportFile] = useState(null);
  const [importStep, setImportStep] = useState(0);
  const [fileHeaders, setFileHeaders] = useState([]);
  const [originalRows, setOriginalRows] = useState([]);
  const [reviewData, setReviewData] = useState([]);
  const [columnMapping, setColumnMapping] = useState({});
  const [editingRowIndex, setEditingRowIndex] = useState(null);
  const [uploadEnabled, setUploadEnabled] = useState(true);
  const [duplicateEntries, setDuplicateEntries] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [duplicateEmails, setDuplicateEmails] = useState([]);
  const [duplicateMobiles, setDuplicateMobiles] = useState([]);

  const onDrop = (acceptedFiles) => {
    if (!acceptedFiles || acceptedFiles.length === 0) return;
    setImportFile(acceptedFiles[0]);
    setError(null);
    setFileHeaders([]);
    setOriginalRows([]);
    setReviewData([]);
    setColumnMapping({});

    const file = acceptedFiles[0];
    const reader = new FileReader();

    reader.onload = (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: "array" });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: "" });

      if (jsonData.length > 0) {
        const headers = Object.keys(jsonData[0]);
        setFileHeaders(headers);
        setOriginalRows(jsonData);

        const mapping = {};
        headers.forEach((h) => {
          const headerLower = h.toLowerCase();
          if (headerLower.includes("name")) mapping[h] = "name";
          else if (headerLower.includes("email")) mapping[h] = "email";
          else if (
            headerLower.includes("mobile") ||
            headerLower.includes("phone")
          )
            mapping[h] = "mobile";
          else if (headerLower.includes("address")) mapping[h] = "address";
          else if (headerLower.includes("state")) mapping[h] = "state";
          else if (
            headerLower.includes("pin") ||
            headerLower.includes("zipcode") ||
            headerLower.includes("postal")
          )
            mapping[h] = "pinCode";
          else if (headerLower.includes("pan")) mapping[h] = "panNumber";
          else mapping[h] = "";
        });

        setColumnMapping(mapping);
      }
    };

    reader.readAsArrayBuffer(file);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
    },
    multiple: false,
    noClick: !uploadEnabled || importStep !== 0,
    noDrag: !uploadEnabled || importStep !== 0,
    disabled: !uploadEnabled,
  });

  const handleMappingChange = (fileCol, sysField) => {
    setColumnMapping({ ...columnMapping, [fileCol]: sysField });
  };

  const prepareReviewData = () => {
    if (originalRows.length === 0 || Object.keys(columnMapping).length === 0) {
      setReviewData([]);
      return;
    }

    const mappedData = originalRows.map((row) => {
      const newRow = {};
      SYSTEM_FIELDS.forEach((sysField) => {
        const fileHeader = Object.keys(columnMapping).find(
          (fh) => columnMapping[fh] === sysField.value
        );
        newRow[sysField.value] = fileHeader ? row[fileHeader] || "" : "";
      });
      return newRow;
    });
    setReviewData(mappedData);
  };

  const checkDuplicates = (data, rowIndex, fieldKey, value) => {
    const duplicates = [];

    // Normalize value for comparison
    const normalizedValue = String(value).toLowerCase().trim();

    data.forEach((row, idx) => {
      if (idx !== rowIndex) {
        const compareValue = String(row[fieldKey] || "")
          .toLowerCase()
          .trim();

        if (
          (fieldKey === "email" && compareValue === normalizedValue) ||
          (fieldKey === "mobile" && compareValue === normalizedValue)
        ) {
          duplicates.push(idx);
          if (!duplicates.includes(rowIndex)) duplicates.push(rowIndex);
        }
      }
    });

    if (fieldKey === "email") {
      setDuplicateMobiles((prev) =>
        prev.filter((idx) => !duplicates.includes(idx))
      );
    } else if (fieldKey === "mobile") {
      setDuplicateMobiles(duplicates);
      setDuplicateEmails((prev) =>
        prev.filter((idx) => !duplicates.includes(idx))
      );
    }

    return duplicates;
  };

  const hasDuplicates = (data) => {
    const emails = new Map();
    const mobiles = new Map();
    let duplicatesFound = false;

    data.forEach((row, idx) => {
      const email = (row.email || "").toLowerCase().trim();
      const mobile = row.mobile || "";

      if (email) {
        if (emails.has(email)) {
          duplicatesFound = true;
          const prevIndex = emails.get(email);
          setDuplicateEntries((prev) => [
            ...new Set([...prev, prevIndex, idx]),
          ]);
        } else {
          emails.set(email, idx);
        }
      }

      if (mobile) {
        if (mobiles.has(mobile)) {
          duplicatesFound = true;
          const prevIndex = mobiles.get(mobile);
          setDuplicateEntries((prev) => [
            ...new Set([...prev, prevIndex, idx]),
          ]);
        } else {
          mobiles.set(mobile, idx);
        }
      }
    });

    return duplicatesFound;
  };

  const handleReviewDataChange = (rowIndex, fieldKey, value) => {
    const updatedData = [...reviewData];
    updatedData[rowIndex][fieldKey] = value;

    if (fieldKey === "email" || fieldKey === "mobile") {
      const newDuplicates = checkDuplicates(
        updatedData,
        rowIndex,
        fieldKey,
        value
      );
      if (newDuplicates.length > 0) {
        setDuplicateEntries(newDuplicates);
        setError(`Duplicate ${fieldKey} found. Please fix before proceeding.`);
      } else {
        setDuplicateEntries((prev) => prev.filter((idx) => idx !== rowIndex));
        setError(null);
      }
    }

    setReviewData(updatedData);
  };

  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);

  const handleSuccess = () => {
    const message = `
        <div> 
          <h3> Data imported Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    const message = `
        <div> 
          <h3> Failed to import data. Please try again later.</h3>
        </div>
      `;

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleImport = async () => {
    // Get the data to import - either selected rows or all rows
    const dataToImport =
      selectedRows.length > 0
        ? reviewData.filter((_, idx) => selectedRows.includes(idx))
        : reviewData;

    const formattedData = dataToImport.map((item) => {
      const row = {};
      SYSTEM_FIELDS.forEach((field) => {
        row[field.label] = item[field.value] || "";
      });
      return row;
    });

    const worksheet = XLSX.utils.json_to_sheet(formattedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Review Data");

    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });

    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    const fileName = `${importFile?.name}`;

    const file = new File([blob], fileName, { type: blob.type });

    const formData = new FormData();

    formData.append("file", file);

    setDuplicateEntries([]); // Reset duplicates
    if (hasDuplicates(dataToImport)) {
      setError(
        "Duplicate email or mobile numbers found. Please resolve duplicates before importing."
      );
      return;
    }

    setLoading(true);
    setError(null);

    const recordCount = dataToImport.length;
    if (recordCount === 0) {
      setError("No data to import.");
      setLoading(false);
      return;
    }

    await axios({
      method: "post",
      url: getUrl(`${authConfig.donorImportEndpoint}/upload`),
      headers: getFileUploadHeaders(),
      data: formData,
    })
      .then((res) => {
        handleSuccess();
        handleDialogClose();
        router.push("/donors-import-history");
      })

      .catch((err) => {
        handleFailure(err);
      });

    setEditingRowIndex(null);
    setLoading(false);
  };

  const downloadTemplate = async () => {
    await axios({
      method: "get",
      url:
        getUrl(authConfig.donorImportEndpoint) +
        "/import-template?category=DONOR",
      headers: getFileUploadHeaders(),
      responseType: "arraybuffer",
    })
      .then((res) => {
        const workbook = XLSX.read(res.data, { type: "array" });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: "" });

        if (jsonData.length > 0) {
          const headers = Object.keys(jsonData[0]);
          setFileHeaders(headers);
          setOriginalRows(jsonData);
          setReviewData(jsonData);
          setColumnMapping({});
        }

        const fileName = "donor_import_template.xlsx";
        XLSX.writeFile(workbook, fileName);
        setUploadEnabled(true);
      })
      .catch((err) => console.log("Error downloading template", err));
  };

  const handleNext = () => {
    setError(null);

    if (importStep === 1) {
      const requiredSystemFields = SYSTEM_FIELDS.filter((f) => f.required);
      const mappedSystemValues = Object.values(columnMapping);
      const unmappedRequiredFields = requiredSystemFields.filter(
        (sysField) => !mappedSystemValues.includes(sysField.value)
      );

      prepareReviewData();
    }
    setImportStep((prev) => Math.min(prev + 1, steps.length - 1));
    setEditingRowIndex(null);
  };
  const handleBack = () => {
    setError(null);
    setImportStep((prev) => Math.max(prev - 1, 0));
    setEditingRowIndex(null);
  };

  const handleDialogClose = () => {
    setImportStep(0);
    setImportFile(null);
    setError(null);
    setColumnMapping({});
    setFileHeaders([]);
    setOriginalRows([]);
    setReviewData([]);
    setEditingRowIndex(null);
    onClose();
  };

  const handleEditRow = (rowIndex) => {
    if (editingRowIndex === rowIndex) {
      setEditingRowIndex(null);
    } else {
      setEditingRowIndex(rowIndex);
    }
  };

  const validateRow = (row) => {
    const status = {
      isValid: true,
      errors: {},
    };


    // Required field validation
    SYSTEM_FIELDS.forEach((field) => {
      if (field.required && !row[field.value]) {
        status.isValid = false;
        status.errors[field.value] = "Required field";
      }
    });

    // Email format validation
    if (row.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
      status.isValid = false;
      status.errors.email = "Invalid email format";
    }

    // Mobile format validation
    if (row.mobile && !/^\d{10}$/.test(row.mobile)) {
      status.isValid = false;
      status.errors.mobile = "Invalid mobile number";
    }

    // PAN card validation
    if (row.panNumber) {
      // PAN format: ********** (5 letters, 4 numbers, 1 letter)
      const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
      if (!panRegex.test(row.panNumber)) {
        status.isValid = false;
        status.errors.panNumber = "Invalid PAN format (e.g., **********)";
      }
      // Additional validation for individual/entity PAN based on donor type
    }

    return status;
  };

  const handlePANChange = (rowIndex, value) => {
    // Convert to uppercase automatically
    const formattedPAN = value.toUpperCase();
    handleReviewDataChange(rowIndex, "pan", formattedPAN);
  };

  return (
    <>
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog open={open} onClose={handleDialogClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "flex-start", md: "flex-start" },
            fontSize: { xs: 20, md: 26 },
            height: "45px",
          }}
        >
          Step {importStep + 1} of {steps.length}: {steps[importStep]}
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: { xs: 5.5, sm: 5.5, md: 5.5, lg: 5.5, xl: 5.5 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleDialogClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#9a9ae5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 1 }}>
          {importStep === 0 && (
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Import donor records by uploading a XLSX file. For best results,{" "}
                <Typography
                  component="span"
                  onClick={downloadTemplate}
                  sx={{
                    color: "primary.main",
                    cursor: "pointer",
                    textDecoration: "underline",
                    "&:hover": {
                      color: "primary.dark",
                    },
                  }}
                >
                  download template
                </Typography>{" "}
                to ensure proper formatting.
              </Typography>

              <Card variant="outlined" sx={{ mb: 3 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography
                    variant="subtitle1"
                    sx={{ mb: 2, fontWeight: 500, color: "text.primary" }}
                  >
                    Upload Your File
                  </Typography>
                  <Box
                    {...getRootProps()}
                    sx={{
                      border: "2px dashed",
                      borderColor: !uploadEnabled
                        ? "grey.300"
                        : isDragActive
                        ? "primary.main"
                        : importFile
                        ? "success.main"
                        : "divider",
                      borderRadius: 1,
                      p: 3,
                      textAlign: "center",
                      cursor: uploadEnabled ? "pointer" : "not-allowed",
                      backgroundColor: !uploadEnabled
                        ? "grey.50"
                        : isDragActive
                        ? "action.hover"
                        : importFile
                        ? "success.light"
                        : "background.paper",
                      opacity: !uploadEnabled ? 0.7 : 1,
                      transition: "all 0.2s ease",
                      "&:hover": {
                        borderColor: uploadEnabled
                          ? "primary.main"
                          : "grey.300",
                      },
                    }}
                  >
                    <input {...getInputProps()} />
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                      }}
                    >
                      <Upload
                        sx={{
                          fontSize: 40,
                          color: !uploadEnabled
                            ? "text.disabled"
                            : isDragActive
                            ? "primary.main"
                            : importFile
                            ? "success.main"
                            : "text.secondary",
                          mb: 1,
                          transition: "color 0.2s ease",
                        }}
                      />
                      <Typography
                        variant="body1"
                        sx={{
                          mb: 1,
                          fontWeight: 500,
                          color: !uploadEnabled
                            ? "text.disabled"
                            : "text.primary",
                        }}
                      >
                        {!uploadEnabled
                          ? "Please download template or existing records first"
                          : importFile
                          ? `Ready to import: ${importFile.name}`
                          : isDragActive
                          ? "Drop your file here"
                          : "Drag and drop your file here"}
                      </Typography>
                      {!importFile && (
                        <Typography
                          variant="body2"
                          color="textSecondary"
                          sx={{ mb: 2 }}
                        >
                          or click to browse files
                        </Typography>
                      )}
                      <Typography variant="caption" color="textSecondary">
                        Supported formats: XLSX (Max 5MB)
                      </Typography>
                      {importFile && (
                        <Box
                          sx={{
                            mt: 2,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "success.main",
                          }}
                        >
                          <Typography
                            variant="caption"
                            sx={{ fontWeight: 500 }}
                          >
                            {(importFile.size / 1024).toFixed(2)} KB • Ready for
                            next step
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>

                  <Box>
                    <Box
                      sx={{
                        mt: 2,
                        p: 2,
                        backgroundColor: "grey.50",
                        borderRadius: 1,
                      }}
                    >
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        component="div"
                      >
                        <strong>Remember</strong>
                        <ul
                          style={{
                            marginTop: 4,
                            marginBottom: 0,
                            paddingLeft: 20,
                          }}
                        >
                          <li>
                            For existing donors, we preserve the following
                            fields :
                            <em>
                              {SYSTEM_FIELDS.filter((f) => f.required)
                                .map((f) => f.label)
                                .join(", ")}
                            </em>
                          </li>
                          <li>
                            You can update the following fields :{" "}
                            <em>
                              {SYSTEM_FIELDS.filter((f) => !f.required)
                                .map((f) => f.label)
                                .join(", ")}
                            </em>
                          </li>
                          <li>
                            For best results, please avoid commas in text
                            fields.{" "}
                          </li>
                          <li>
                            Each donor needs a unique email address - we'll
                            automatically skip if any duplicates are found.
                          </li>
                          <li>
                            If anything goes wrong, we'll send you a detailed
                            error report by email.
                          </li>
                        </ul>
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          )}

          {importStep === 1 && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Map Columns to Fields
              </Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <Icon
                        icon="vscode-icons:file-type-excel"
                        fontSize="1rem"
                      />
                      XLSX column
                    </TableCell>
                    <TableCell>Donor Field</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fileHeaders
                    .filter((header) => header !== "S.No" && header !== "Category")
                    .map((fileHeader) => (
                      <TableRow key={fileHeader}>
                        <TableCell>{fileHeader}</TableCell>
                        <TableCell>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              size="small"
                              fullWidth
                              options={SYSTEM_FIELDS.filter(
                                (field) =>
                                  field.label !== "S.No" &&
                                  field.label !== "Category"
                              ).map((field) => field.value)}
                              getOptionLabel={(option) => {
                                const field = SYSTEM_FIELDS.find(
                                  (f) => f.value === option
                                );
                                return field ? field.label : option;
                              }}
                              value={columnMapping[fileHeader] || null}
                              onChange={(e, newValue) =>
                                handleMappingChange(fileHeader, newValue)
                              }
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Select donor field"
                                  variant="outlined"
                                />
                              )}
                            />
                          </FormControl>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </Box>
          )}

          {importStep === 2 && (
            <Box>
              <Box
                sx={{
                  mb: 3,
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 600,
                    color: "text.primary",
                    letterSpacing: "-0.5px",
                  }}
                >
                  Review & Import Data
                </Typography>
                <Box sx={{ display: "flex", gap: 2 }}>
                  {selectedRows?.length > 0 && (
                    <Button
                      variant="outlined"
                      color="error"
                      size="small"
                      startIcon={<DeleteIcon />}
                      onClick={() => {
                        const newData = reviewData.filter(
                          (_, idx) => !selectedRows.includes(idx)
                        );
                        setReviewData(newData);
                        setSelectedRows([]);
                      }}
                    >
                      Delete ({selectedRows.length})
                    </Button>
                  )}
                </Box>
              </Box>

              {reviewData.length > 0 && (
                <Table
                  size="small"
                  stickyHeader
                  sx={{
                    mb: 2,
                    border: 1,
                    borderColor: "divider",
                    borderRadius: 1,
                    overflow: "hidden",
                    backgroundColor: "#fff",
                    minWidth: "100%",
                    "& .MuiTableCell-root": {
                      padding: "8px",
                      fontSize: "0.75rem",
                      maxWidth: {
                        xs: "120px", // Mobile
                        sm: "150px", // Tablet
                        md: "200px", // Desktop
                      },
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                    },
                    "& .MuiTableCell-head": {
                      backgroundColor: "#ffffff",
                      "&:hover": {
                        backgroundColor: "rgba(0, 0, 0, 0.02)",
                        "& .row-actions": { opacity: 1 },
                      },
                      "& .MuiTableCell-root": {
                        borderBottom: "1px solid",
                        borderBottomColor: "divider",
                      },
                    },
                    "& .MuiTextField-root": {
                      minWidth: "auto",
                      "& .MuiOutlinedInput-root": {
                        "& input": {
                          padding: "6px 8px",
                          fontSize: "0.75rem",
                        },
                      },
                    },
                  }}
                >
                  <TableHead>
                    <TableRow>
                      <TableCell
                        padding="checkbox"
                        sx={{
                          borderBottom: "2px solid",
                          borderBottomColor: "divider",
                        }}
                      >
                        <Checkbox
                          size="small"
                          checked={
                            selectedRows.length > 0 &&
                            selectedRows.length === reviewData.length
                          }
                          indeterminate={
                            selectedRows.length > 0 &&
                            selectedRows.length < reviewData.length
                          }
                          onChange={(e) => {
                            if (e.target.checked) {
                              // Select all rows
                              const allRowIndexes = reviewData.map(
                                (_, idx) => idx
                              );
                              setSelectedRows(allRowIndexes);
                            } else {
                              setSelectedRows([]);
                            }
                          }}
                        />
                      </TableCell>
                      {SYSTEM_FIELDS.filter(
                        (field) =>
                          field.label !== "S.No" && field.label !== "Category"
                      ).map((field) => (
                        <TableCell
                          key={field.value}
                          sx={{
                            whiteSpace: "nowrap",
                            letterSpacing: "0.01em",
                            fontSize: "0.8125rem",
                            textTransform: "uppercase",
                          }}
                        >
                          {field.label}
                          {field.required && (
                            <Box
                              component="span"
                              sx={{ color: "error.main", ml: 0.5 }}
                            >
                              *
                            </Box>
                          )}
                        </TableCell>
                      ))}
                      <TableCell
                        sx={{
                          textAlign: "center",
                          fontSize: "0.8125rem",
                          textTransform: "uppercase",
                          letterSpacing: "0.01em",
                        }}
                      >
                        Status
                      </TableCell>
                      <TableCell
                        sx={{
                          textAlign: "right",
                          fontSize: "0.8125rem",
                          textTransform: "uppercase",
                          letterSpacing: "0.01em",
                        }}
                      >
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {reviewData.map((row, rowIndex) => {
                      const rowValidation = validateRow(row);
                      const isDuplicate = duplicateEntries.includes(rowIndex);

                      return (
                        <TableRow
                          key={rowIndex}
                          selected={selectedRows.includes(rowIndex)}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={selectedRows.includes(rowIndex)}
                              onChange={(e) => {
                                setSelectedRows((prev) =>
                                  e.target.checked
                                    ? [...prev, rowIndex]
                                    : prev.filter((idx) => idx !== rowIndex)
                                );
                              }}
                            />
                          </TableCell>
                          {SYSTEM_FIELDS.filter(
                            (field) =>
                              field.label !== "S.No" &&
                              field.label !== "Category"
                          ).map((field) => (
                            <TableCell key={`${rowIndex}-${field.value}`}>
                              <TextField
                                fullWidth
                                variant="outlined"
                                size="small"
                                value={row[field.value] || ""}
                                onChange={(e) =>
                                  field.value === "pan"
                                    ? handlePANChange(rowIndex, e.target.value)
                                    : handleReviewDataChange(
                                        rowIndex,
                                        field.value,
                                        e.target.value
                                      )
                                }
                                disabled={editingRowIndex !== rowIndex}
                                error={
                                  !!rowValidation.errors[field.value] ||
                                  (duplicateEmails.includes(rowIndex) &&
                                    field.value === "email") ||
                                  (duplicateMobiles.includes(rowIndex) &&
                                    field.value === "mobile")
                                }
                                helperText={
                                  duplicateEmails.includes(rowIndex) &&
                                  field.value === "email"
                                    ? "Duplicate email"
                                    : duplicateMobiles.includes(rowIndex) &&
                                      field.value === "mobile"
                                    ? "Duplicate mobile"
                                    : rowValidation.errors[field.value]
                                }
                                inputProps={{
                                  maxLength:
                                    field.value === "pan" ? 10 : undefined,
                                  style: {
                                    textTransform:
                                      field.value === "pan"
                                        ? "uppercase"
                                        : "none",
                                    backgroundColor:
                                      (duplicateEmails.includes(rowIndex) &&
                                        field.value === "email") ||
                                      (duplicateMobiles.includes(rowIndex) &&
                                        field.value === "mobile")
                                        ? "rgba(211, 47, 47, 0.05)"
                                        : "transparent",
                                  },
                                }}
                                sx={{
                                  "& .MuiOutlinedInput-root": {
                                    minHeight: "32px",
                                    "& input": {
                                      padding: "6px 8px",
                                      fontSize: "0.75rem",
                                    },
                                    ...(duplicateEntries.includes(rowIndex) &&
                                      ["email", "mobile"].includes(
                                        field.value
                                      ) && {
                                        "& fieldset": {
                                          borderColor: "error.main",
                                        },
                                        "&:hover fieldset": {
                                          borderColor: "error.dark",
                                        },
                                      }),
                                  },
                                }}
                              />
                            </TableCell>
                          ))}
                          <TableCell>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                                color: duplicateEntries.includes(rowIndex)
                                  ? "error.main"
                                  : rowValidation.isValid
                                  ? "success.main"
                                  : "error.main",
                              }}
                            >
                              {duplicateEntries.includes(rowIndex)
                                ? "Duplicate Entry"
                                : rowValidation.isValid
                                ? "Valid"
                                : "Invalid"}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box
                              className="row-actions"
                              sx={{ display: "flex", gap: 1 }}
                            >
                              <IconButton
                                size="small"
                                onClick={() => handleEditRow(rowIndex)}
                                color={
                                  editingRowIndex === rowIndex
                                    ? "primary"
                                    : "default"
                                }
                              >
                                {editingRowIndex === rowIndex ? (
                                  <SaveIcon fontSize="small" />
                                ) : (
                                  <EditIcon fontSize="small" />
                                )}
                              </IconButton>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => {
                                  const newData = [...reviewData];
                                  newData.splice(rowIndex, 1);
                                  setReviewData(newData);
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
              <Box
                sx={{ display: "flex", justifyContent: "flex-end", mt: 3 }}
              ></Box>
            </Box>
          )}

          {importStep !== 3 && error && (
            <Alert
              severity="error"
              sx={{
                mt: 2,
                mb: 1,
                width: "calc(100% - 32px)",
                mx: "auto",
                color: "#fff",
                backgroundColor: "#d32f2f",
              }}
            >
              {error}
            </Alert>
          )}
        </DialogContent>
        <DialogActions
          sx={{
            p: 2,
            borderTop: "1px solid #eee",
            display: "flex",
            justifyContent: "flex-end",
            "& .MuiButton-root": {
              height: 40,
              marginLeft: 1,
            },
          }}
        >
          {importStep === 0 && (
            <Button onClick={handleDialogClose} color="inherit">
              Cancel
            </Button>
          )}

          {(importStep === 1 || (importStep === 2 && !loading)) && (
            <Button
              onClick={handleBack}
              color="primary"
              variant="outlined"
              disabled={loading}
            >
              Back
            </Button>
          )}

          {importStep < 2 && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNext}
              disabled={
                (importStep === 0 && !importFile) ||
                (importStep === 1 &&
                  Object.values(columnMapping).filter((v) => v).length <
                    SYSTEM_FIELDS.filter((f) => f.required).length) ||
                loading
              }
            >
              Next
            </Button>
          )}

          {importStep === 2 && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleImport}
              disabled={
                loading ||
                reviewData.length === 0 ||
                editingRowIndex !== null ||
                duplicateEntries.length > 0
              }
            >
              {loading
                ? "Importing..."
                : selectedRows.length > 0
                ? `Import ${selectedRows.length} Selected Record(s)`
                : `Import all Record(s)`}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ImportExportDonors;