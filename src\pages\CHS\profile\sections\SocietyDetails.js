// ** MUI Imports
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component
import {
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import MUITableCell from "src/pages/SP/MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";
import Section1 from "./Section1";
import { AuthContext } from "src/context/AuthContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const SocietyDetails = ({ data, expanded, userData, employeesData }) => {
  const { can } = useRBAC();

  const { listValues } = useContext(AuthContext)

  const [assignedTo, setAssignedTo] = useState(data?.assignedTo);
  const handleAssignedToChange = (event) => {
    const selectedId = event.target.value;
    setAssignedTo(selectedId);
  };

  useEffect(() => {
    if (!!data && !!data.assignedTo) {
      setAssignedTo(data.assignedTo);
    }
  }, [data]);

  const [assignedToName, setAssignedToName] = useState("");

  useEffect(() => {
    if (!!assignedTo && employeesData && employeesData.length > 0) {
      setAssignedToName(
        employeesData?.find((item) => item.id == assignedTo)?.name
      );
    }
  }, [assignedTo, employeesData]);

  // ** Hook
  const theme = useTheme();
  const [state, setState] = useState(true);

  const handleState = () => {
    setState(!state);
  };

  const locationObject = listValues?.find(location => location.name === data?.location);

  
  return (
    <>
      {/* {can('society_societyDetails_READ') && */}
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Society Details"}
        body={
          <>
            {state && (
              <TableContainer
                sx={{ padding: "4px 6px", cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer'  }}
                className="tableBody"
                onClick={handleState}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Society Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.name}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>System Code:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.systemCode}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Plot CTS No:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.plotCTSNo}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Team Member:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.teamMember}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Society Address:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.societyAddress}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Location:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.location}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Zone:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.zone}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Type:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.type}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    {userData && userData.id !== undefined && (
                      <TableRow>
                        <MUITableCell>
                          <Typography style={field}>AssignedTo:</Typography>
                        </MUITableCell>
                        <MUITableCell>
                          <Typography className="data-field">
                            {assignedToName}
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    )}

                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Ward:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.ward}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Pin Code:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.pinCode}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>RoadWidth:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.roadWidth}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                    <MUITableCell>
                      <Tooltip
                        title="Gross plot area in square meters"
                        placement="bottom-start"
                        animate={{ duration: 300 }}
                        delay={{ enter: 500, exit: 200 }}
                      >
                        <Typography style={field}>
                          Gross Plot Area (Sq Mtrs.):
                        </Typography>
                      </Tooltip>
                    </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.grossPlotArea}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Authority:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.authority}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Reference:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.reference}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Owner:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.owner}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Google Map Location:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.googleMapLocation}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {!state && (
              <Section1
                userData={userData}
                formData={data}
                locId={locationObject?.id}
                onCancel={handleState}
                employeesData={employeesData}
                handleAssignedToChange={handleAssignedToChange}
                assignedTo={assignedTo}
              />
            )}
          </>
        }
        expanded={expanded}
      />
      {/* }   */}
    </>
  );
};
export default SocietyDetails;
