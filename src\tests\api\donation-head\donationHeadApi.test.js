import axios from 'axios';
import { mockDonationHead, mockApiResponse } from '../../utils/donationTestUtils';
import { setupAllMocks } from '../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// API service functions to test
const donationHeadApi = {
  getAll: async (page = 1, pageSize = 10, searchKeyword = '') => {
    const response = await axios.post('/api/donation-heads/search', {
      page,
      pageSize,
      searchKeyword,
    });
    return response.data;
  },

  getById: async (id) => {
    const response = await axios.get(`/api/donation-heads/${id}`);
    return response.data;
  },

  create: async (data) => {
    const response = await axios.post('/api/donation-heads', data);
    return response.data;
  },

  update: async (id, data) => {
    const response = await axios.patch(`/api/donation-heads/${id}`, data);
    return response.data;
  },

  delete: async (id) => {
    const response = await axios.delete(`/api/donation-heads/${id}`);
    return response.data;
  },

  toggleStatus: async (id) => {
    const response = await axios.patch(`/api/donation-heads/${id}/toggle-status`);
    return response.data;
  },

  search: async (filters) => {
    const response = await axios.post('/api/donation-heads/advanced-search', filters);
    return response.data;
  },
};

describe('Donation Head API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/donation-heads (getAll)', () => {
    it('fetches all donation heads successfully', async () => {
      const mockData = {
        donationHeads: [
          mockDonationHead({ id: '1', name: 'Education Fund' }),
          mockDonationHead({ id: '2', name: 'Healthcare Fund' }),
        ],
        totalCount: 2,
        page: 1,
        pageSize: 10,
      };

      mockAxios.post.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationHeadApi.getAll(1, 10, '');

      expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads/search', {
        page: 1,
        pageSize: 10,
        searchKeyword: '',
      });
      expect(result).toEqual(mockData);
    });

    it('handles search with keyword', async () => {
      const mockData = {
        donationHeads: [mockDonationHead({ id: '1', name: 'Education Fund' })],
        totalCount: 1,
        page: 1,
        pageSize: 10,
      };

      mockAxios.post.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationHeadApi.getAll(1, 10, 'Education');

      expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads/search', {
        page: 1,
        pageSize: 10,
        searchKeyword: 'Education',
      });
      expect(result).toEqual(mockData);
    });

    it('handles pagination correctly', async () => {
      const mockData = {
        donationHeads: [mockDonationHead({ id: '3', name: 'Environment Fund' })],
        totalCount: 25,
        page: 3,
        pageSize: 5,
      };

      mockAxios.post.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationHeadApi.getAll(3, 5, '');

      expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads/search', {
        page: 3,
        pageSize: 5,
        searchKeyword: '',
      });
      expect(result).toEqual(mockData);
    });

    it('handles API errors', async () => {
      const errorMessage = 'Failed to fetch donation heads';
      mockAxios.post.mockRejectedValue(new Error(errorMessage));

      await expect(donationHeadApi.getAll()).rejects.toThrow(errorMessage);
    });
  });

  describe('GET /api/donation-heads/:id (getById)', () => {
    it('fetches donation head by ID successfully', async () => {
      const mockData = mockDonationHead({ id: '1', name: 'Education Fund' });
      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationHeadApi.getById('1');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/donation-heads/1');
      expect(result).toEqual(mockData);
    });

    it('handles not found error', async () => {
      mockAxios.get.mockRejectedValue(new Error('Donation head not found'));

      await expect(donationHeadApi.getById('999')).rejects.toThrow('Donation head not found');
    });
  });

  describe('POST /api/donation-heads (create)', () => {
    it('creates donation head successfully', async () => {
      const newDonationHead = {
        name: 'New Education Fund',
        description: 'New education related donations',
        orgId: 'org-1',
      };

      const mockResponse = mockDonationHead({
        id: '3',
        ...newDonationHead,
        isActive: true,
        createdOn: '2024-01-01T10:00:00Z',
        updatedOn: '2024-01-01T10:00:00Z',
      });

      mockAxios.post.mockResolvedValue(mockApiResponse(mockResponse));

      const result = await donationHeadApi.create(newDonationHead);

      expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads', newDonationHead);
      expect(result).toEqual(mockResponse);
    });

    it('handles validation errors', async () => {
      const invalidData = { name: '' }; // Missing required fields
      
      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errors: ['Name is required', 'Organization ID is required'],
          },
        },
      });

      await expect(donationHeadApi.create(invalidData)).rejects.toMatchObject({
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
          },
        },
      });
    });

    it('handles duplicate name error', async () => {
      const duplicateData = {
        name: 'Education Fund', // Already exists
        description: 'Duplicate name',
        orgId: 'org-1',
      };

      mockAxios.post.mockRejectedValue({
        response: {
          status: 409,
          data: {
            message: 'Donation head with this name already exists',
          },
        },
      });

      await expect(donationHeadApi.create(duplicateData)).rejects.toMatchObject({
        response: {
          status: 409,
        },
      });
    });
  });

  describe('PATCH /api/donation-heads/:id (update)', () => {
    it('updates donation head successfully', async () => {
      const updateData = {
        name: 'Updated Education Fund',
        description: 'Updated description',
      };

      const mockResponse = mockDonationHead({
        id: '1',
        ...updateData,
        orgId: 'org-1',
        isActive: true,
        updatedOn: '2024-01-02T10:00:00Z',
      });

      mockAxios.patch.mockResolvedValue(mockApiResponse(mockResponse));

      const result = await donationHeadApi.update('1', updateData);

      expect(mockAxios.patch).toHaveBeenCalledWith('/api/donation-heads/1', updateData);
      expect(result).toEqual(mockResponse);
    });

    it('handles not found error during update', async () => {
      const updateData = { name: 'Updated Name' };

      mockAxios.patch.mockRejectedValue({
        response: {
          status: 404,
          data: {
            message: 'Donation head not found',
          },
        },
      });

      await expect(donationHeadApi.update('999', updateData)).rejects.toMatchObject({
        response: {
          status: 404,
        },
      });
    });
  });

  describe('DELETE /api/donation-heads/:id (delete)', () => {
    it('deletes donation head successfully', async () => {
      const mockResponse = { message: 'Donation head deleted successfully' };
      mockAxios.delete.mockResolvedValue(mockApiResponse(mockResponse));

      const result = await donationHeadApi.delete('1');

      expect(mockAxios.delete).toHaveBeenCalledWith('/api/donation-heads/1');
      expect(result).toEqual(mockResponse);
    });

    it('handles not found error during delete', async () => {
      mockAxios.delete.mockRejectedValue({
        response: {
          status: 404,
          data: {
            message: 'Donation head not found',
          },
        },
      });

      await expect(donationHeadApi.delete('999')).rejects.toMatchObject({
        response: {
          status: 404,
        },
      });
    });

    it('handles foreign key constraint error', async () => {
      mockAxios.delete.mockRejectedValue({
        response: {
          status: 409,
          data: {
            message: 'Cannot delete donation head with existing donations',
          },
        },
      });

      await expect(donationHeadApi.delete('1')).rejects.toMatchObject({
        response: {
          status: 409,
        },
      });
    });
  });

  describe('PATCH /api/donation-heads/:id/toggle-status (toggleStatus)', () => {
    it('toggles donation head status successfully', async () => {
      const mockResponse = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: false, // Toggled from true to false
        updatedOn: '2024-01-02T10:00:00Z',
      });

      mockAxios.patch.mockResolvedValue(mockApiResponse(mockResponse));

      const result = await donationHeadApi.toggleStatus('1');

      expect(mockAxios.patch).toHaveBeenCalledWith('/api/donation-heads/1/toggle-status');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('POST /api/donation-heads/advanced-search (search)', () => {
    it('performs advanced search successfully', async () => {
      const searchFilters = {
        nameFilter: 'Education',
        descriptionFilter: 'fund',
        orgIdFilter: 'org-1',
        isActiveFilter: true,
      };

      const mockData = {
        donationHeads: [mockDonationHead({ id: '1', name: 'Education Fund' })],
        totalCount: 1,
        appliedFilters: searchFilters,
      };

      mockAxios.post.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationHeadApi.search(searchFilters);

      expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads/advanced-search', searchFilters);
      expect(result).toEqual(mockData);
    });

    it('handles empty search results', async () => {
      const searchFilters = {
        nameFilter: 'NonExistent',
      };

      const mockData = {
        donationHeads: [],
        totalCount: 0,
        appliedFilters: searchFilters,
      };

      mockAxios.post.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationHeadApi.search(searchFilters);

      expect(result.donationHeads).toHaveLength(0);
      expect(result.totalCount).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('handles network errors', async () => {
      mockAxios.get.mockRejectedValue(new Error('Network Error'));

      await expect(donationHeadApi.getById('1')).rejects.toThrow('Network Error');
    });

    it('handles timeout errors', async () => {
      mockAxios.post.mockRejectedValue(new Error('timeout of 5000ms exceeded'));

      await expect(donationHeadApi.getAll()).rejects.toThrow('timeout of 5000ms exceeded');
    });

    it('handles server errors (500)', async () => {
      mockAxios.post.mockRejectedValue({
        response: {
          status: 500,
          data: {
            message: 'Internal server error',
          },
        },
      });

      await expect(donationHeadApi.create({})).rejects.toMatchObject({
        response: {
          status: 500,
        },
      });
    });

    it('handles unauthorized errors (401)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 401,
          data: {
            message: 'Unauthorized',
          },
        },
      });

      await expect(donationHeadApi.getAll()).rejects.toMatchObject({
        response: {
          status: 401,
        },
      });
    });

    it('handles forbidden errors (403)', async () => {
      mockAxios.delete.mockRejectedValue({
        response: {
          status: 403,
          data: {
            message: 'Forbidden: Insufficient permissions',
          },
        },
      });

      await expect(donationHeadApi.delete('1')).rejects.toMatchObject({
        response: {
          status: 403,
        },
      });
    });
  });

  describe('Request Headers and Configuration', () => {
    it('includes proper authorization headers', async () => {
      mockAxios.get.mockResolvedValue(mockApiResponse({}));

      await donationHeadApi.getById('1');

      // Verify that the mock was called (headers would be set by interceptors in real app)
      expect(mockAxios.get).toHaveBeenCalledWith('/api/donation-heads/1');
    });

    it('includes proper content-type for POST requests', async () => {
      mockAxios.post.mockResolvedValue(mockApiResponse({}));

      const data = { name: 'Test', orgId: 'org-1' };
      await donationHeadApi.create(data);

      expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads', data);
    });
  });
});
