
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import { useTheme } from "@emotion/react";
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";
import Section7 from "./Section7";
import MUITableCell from "src/pages/SP/MUITableCell";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const AssignmentAndStatus = ({ data, expanded, employeesData }) => {
  const { can } = useRBAC();

  // ** Hook
  const theme = useTheme();

  const [state6, setState6] = useState(true);
  const { listValues, user } = useContext(AuthContext);
  const handleState6 = () => {
    setState6(!state6);
  };

  const [assignedTo, setAssignedTo] = useState(data?.assignedTo);
  const [createdBy, setCreatedBy] = useState(data?.createdBy);

  const handleAssignedToChange = (event) => {
    const selectedId = event.target.value;
    setAssignedTo(selectedId);
  };

  useEffect(() => {
    if (!!data && !!data?.assignedTo) {
      setAssignedTo(data.assignedTo);
    }
    if (!!data && !!data?.createdBy) {
      setCreatedBy(data.createdBy);
    }
  }, [data]);

  const [assignedToName, setAssignedToName] = useState("");
  const [createdByName, setCreatedByName] = useState("");

  useEffect(() => {
    if (!!assignedTo && employeesData && employeesData.length > 0) {
      setAssignedToName(
        employeesData?.find((item) => item.id == assignedTo)?.name
      );
    }
    if (!!createdBy && employeesData && employeesData.length > 0) {
      setCreatedByName(
        employeesData?.find((item) => item.id == createdBy)?.name
      );
    }
  }, [assignedTo, createdBy, employeesData]);

  const leadStatus = data?.leadStatus
    ? listValues.find((item) => item?.id === data?.leadStatus)?.name
    : null;

    const leadPriority = data?.leadPriority
    ? listValues.find((item) => item?.id === data?.leadPriority)?.name
    : null;

  const sourceGroup = data?.sourceGroup
    ? listValues.find((item) => item?.id === data?.sourceGroup)?.name
    : null;

  const subSourceGroup = data?.subSourceGroup
    ? listValues.find((item) => item?.id === data?.subSourceGroup)?.name
    : null;

  return (
    <>
    {user.organisationCategory === "SOCIETY" ? <></> : (
      <AccordionBasic
        id={"panel-header-2"}
        ariaControls={"panel-content-2"}
        heading={"Assignment and Status"}
        body={
          <>
            {state6 && (
              <TableContainer
                sx={{ padding: "4px 6px", cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer'  }}
                className="tableBody"
                //onClick={can('society_otherDetails_UPDATE') ? handleState6 : null}>
                onClick={handleState6}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Assigned To:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {assignedToName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Lead Status:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {leadStatus}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Lead Priority:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {leadPriority}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Source Group:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {sourceGroup}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Sub Source Group:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {subSourceGroup}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Created On:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.createdOn
                            ? new Date(data?.createdOn).toLocaleDateString(
                                "en-GB"
                              )
                            : ""}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Created By:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {createdByName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Remarks:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.remarks}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {!state6 && (
              <Section7
                formData={data}
                onCancel={handleState6}
                employeesData={employeesData}
                assignedTo={assignedTo}
                createdByName={createdByName}
                handleAssignedToChange={handleAssignedToChange}
              />
            )}
          </>
        }
        expanded={expanded}
      />
      )}
    </>
  );
};
export default AssignmentAndStatus;
