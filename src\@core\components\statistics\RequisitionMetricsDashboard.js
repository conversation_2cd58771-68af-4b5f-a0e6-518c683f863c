import React, { useContext, useEffect, useState } from "react";
import { Box, Typography, Card, Grid } from "@mui/material";
import { BiTask, BiCalendar, BiCheckCircle, BiBarChart } from "react-icons/bi"; // Updated icon for Visit Success
import { FaClipboardList } from "react-icons/fa";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { MdDateRange } from "react-icons/md";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";

const RequisitionMetricsDashboard = () => {
  const [sampleData, setSampleData] = useState([]);

  const router = useRouter();
  const { user } = useContext(AuthContext);
  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs + "/service-requisitions-average-count"
      );
    } else {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          "/employee/service-requisitions-average-count"
      );
    }

    axios({
      method: "get",
      url: url,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  // Static data for statistics
  const statsData = [
    {
      label: "Daily Requisitions",
      value: sampleData?.serviceRequisitionCreatedCurrentDay || 0,
      icon: <BiTask size={40} color="#f39c12" />,
      color: "#fffde7", // Light yellow background
      frequency: "daily",
    },
    {
      label: "Monthly Requisitions",
      value: sampleData?.monthlyServiceRequisitionCount || 0,
      icon: <BiCalendar size={40} color="#3498db" />,
      color: "#e3f2fd", // Light blue background
      frequency: "monthly",
    },
    {
      label: "Yearly Requisitions",
      value: sampleData?.serviceRequisitionCurrentYearCount || 0,
      icon: <MdDateRange size={40} color="#48c9b0" />,
      color: "#e8f8f5", // Light blue background
      frequency: "yearly",
    },
    {
      label: "Completed Requisition",
      value: sampleData?.completedServiceRequisitions || 0,
      icon: <BiCheckCircle size={40} color="#108A00" />,
      color: "#e8f5e9", // Light green background
      frequency: "completed",
    },
    {
      label: "Booked Site Visits",
      value: sampleData?.bookedSiteVisitsFromTotalSR || 0,
      icon: <BiBarChart size={40} color="#e74c3c" />,
      color: "#ffebee", // Light red background
      frequency: "site-visits",
    },
  ];

  const handleCardClick = (frequency) => {
    const query = { frequency };
    if (frequency === "site-visits") {
      query.bookedSiteVisits = true
      router.push({
        pathname: "/site-visits",
        query,
      });
    } else {
      if (user?.roleId !== authConfig?.superAdminRoleId) {
        query.assignedTo = true;
      }
      router.push({
        pathname: "/service-requisitions",
        query,
      });
    }
  };

  return (
    <Card
      sx={{
        p: 4,
        borderRadius: "16px",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 2,
          display: "flex", // Align text and icon in a single line
          alignItems: "center",
          gap: 1, // Adds spacing between the icon and text
        }}
      >
        <FaClipboardList size={20} color="#108A00" />
        Requisition Metrics
      </Typography>
      <Grid container spacing={4} justifyContent="center">
        {statsData?.map((stat, index) => (
          <Grid item xs={12} sm={6} md={2.4} key={index}>
            <Card
              sx={{
                p: 2,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                borderRadius: "50%",
                height: 150,
                width: 150,
                backgroundColor: stat.color,
                transition: "transform 0.3s",
                "&:hover": {
                  transform: "rotate(5deg) scale(1.1)",
                },
                boxShadow: "0px 6px 20px rgba(0,0,0,0.1)",
                cursor: "pointer",
              }}
              onClick={() => handleCardClick(stat.frequency)}
            >
              <Box sx={{ mb: 1 }}>{stat.icon}</Box>
              <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                {stat.value}
              </Typography>
              <Typography variant="caption">{stat.label}</Typography>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Card>
  );
};

export default RequisitionMetricsDashboard;
