import React, { useContext, useEffect, useState } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { Typography, IconButton, Box } from "@mui/material";
import { Close as CloseIcon, CropSquareOutlined, MinimizeSharp } from "@mui/icons-material";
import { AuthContext } from "src/context/AuthContext";
import mammoth from "mammoth";
import { Buffer } from 'buffer';
import FallbackSpinner from "src/@core/components/spinner";

const ViewDocumentByLocation = ({ location, setSelectedLocation }) => {

  const { getFileByLocation } = useContext(AuthContext);
  const [fileContent, setFileContent] = useState(null);
  const [fileType, setFileType] = useState(null);
  const [isLarge, setIsLarge] = useState(false);
  const [docxContent, setDocxContent] = useState(null);
  const [loading, setLoading] = useState(true); // Loading state

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true); // Start loading when the data fetch begins
        const response = await getFileByLocation(location);
        const fileData = response?.data?.data;
        const fileName = location.split("/").pop();
        const fileExtension = fileName.split(".").pop().toLowerCase();

        let inferredFileType;
        switch (fileExtension) {
          case 'pdf':
            inferredFileType = 'application/pdf';
            break;
          case 'docx':
            inferredFileType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            break;
          case 'jpg':
          case 'jpeg':
          case 'png':
            inferredFileType = `image/${fileExtension}`;
            break;
          default:
            inferredFileType = 'unknown';
        }

        setFileType(inferredFileType);
        if (inferredFileType === 'application/pdf') {
          const blob = new Blob([Buffer.from(fileData, 'base64')], { type: 'application/pdf' });
          const pdfUrl = URL.createObjectURL(blob);
          setFileContent(pdfUrl);
        } else if (inferredFileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          const arrayBuffer = Buffer.from(fileData, 'base64').buffer;
          const { value } = await mammoth.convertToHtml({ arrayBuffer });
          setDocxContent(value);
        } else {
          setFileContent(fileData);
        }
        setLoading(false); // Stop loading once the data has been fetched
      } catch (error) {
        console.error("Error fetching file content:", error);
        setLoading(false); // Stop loading in case of error
      }
    };

    if (location) {
      fetchData();
    }
  }, [getFileByLocation, location]);

  const renderFileContent = () => {
    if (loading) {
      return (
      <Box 
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "100%",
      }}>
        <FallbackSpinner />; 
      </Box>
      );
    }

    if (fileType) {
      if (fileType.startsWith('image/')) {
        const imageUrl = `data:${fileType};base64,${fileContent}`;
        return <img src={imageUrl} alt="file" style={{ width: "100%", height: "100%", objectFit: "contain" }} />;
      } else if (fileType === 'application/pdf') {
        return (
          <iframe
            src={fileContent}
            style={{ width: "100%", height: "100%" }}
            title="PDF Viewer"
          /> 
        );
      } else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        return (
          <div
            dangerouslySetInnerHTML={{ __html: docxContent }}
            style={{ width: "100%", height: "100%", overflowY: "auto" }}
          />
        );
      } else {
        return <Typography>Unsupported file type: {fileType}</Typography>;
      }
    }
    return <Typography>File type not yet determined.</Typography>;
  };

  const onClose = () => {
    if (fileContent) {
      URL.revokeObjectURL(fileContent);
      setFileContent(null);
    } 
    setSelectedLocation(null);
  };

  const toggleSize = () => {
    setIsLarge((prevIsLarge) => !prevIsLarge);
  };

  return (
    <Dialog
      open={Boolean(location)}
      onClose={onClose}
      maxWidth={isLarge ? "xl" : "md"}
    >
      <DialogTitle
         sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start" },
          fontSize: { xs: 19, md: 20 },
        }}
        textAlign={"center"}
      >
        <Typography>
          {location && location?.split("/").pop()}
        </Typography>

        <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={toggleSize}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              {isLarge ? (
                <MinimizeSharp style={{ fontSize: 20 }} />
              ) : (
                <CropSquareOutlined style={{ fontSize: 20 }} />
              )}
            </IconButton>
            <IconButton
              size="small"
              onClick={onClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                ml: 2,
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <CloseIcon style={{ fontSize: 20 }} />
            </IconButton>
          </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          width: isLarge ? "800px" : "500px",
          height: isLarge ? "500px" : "300px",
          position: "relative",
        }}
      >
        {renderFileContent()}
      </DialogContent>
    </Dialog>
  );
};

export default ViewDocumentByLocation;
