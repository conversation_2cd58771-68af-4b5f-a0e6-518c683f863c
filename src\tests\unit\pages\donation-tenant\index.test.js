import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DonationTenantPage from '@/pages/donation-tenant/index.js';
import { renderWithProviders, mockStatsData, mockChartData, mockDistributionData } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the DonationTenantPage component
jest.mock('@/pages/donation-tenant/index.js', () => {
  return function MockDonationTenantPage() {
    const [stats, setStats] = React.useState(null);
    const [loadingStats, setLoadingStats] = React.useState(true);
    const [selectedTab, setSelectedTab] = React.useState(0);
    const [donationHeads, setDonationHeads] = React.useState([]);

    // Mock data specific to tenant
    const mockTenantStats = {
      totalDonations: 125000,
      uniqueDonors: 45,
      last30DaysDonations: 8,
      averageDonation: 2777,
      donationHeadsCount: 5,
      currentPeriod: 100,
      topHead: 'Education',
    };

    const mockDonationData = {
      yearly: { series: [{ name: 'Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }], categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
      monthly: { series: [{ name: 'Donations', data: [200, 150, 300, 250, 350, 100] }], categories: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'] },
      weekly: { series: [{ name: 'Donations', data: [50, 30, 80, 60, 90, 70, 100] }], categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
      daily: { series: [{ name: 'Donations', data: [10, 15, 5, 20, 12, 25, 8] }], categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'] },
    };

    const mockDistributionData = {
      series: [3500, 2800, 2200, 1800, 1200],
      labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
    };

    const mockDonationHeadsList = [
      { id: '1', name: 'Education Fund', totalAmount: 3500, percentage: 30.4 },
      { id: '2', name: 'Healthcare Fund', totalAmount: 2800, percentage: 24.3 },
      { id: '3', name: 'Food Aid Fund', totalAmount: 2200, percentage: 19.1 },
      { id: '4', name: 'Shelter Fund', totalAmount: 1800, percentage: 15.7 },
      { id: '5', name: 'Environment Fund', totalAmount: 1200, percentage: 10.4 },
    ];

    // Simulate data loading
    React.useEffect(() => {
      const loadStats = async () => {
        setLoadingStats(true);
        try {
          await new Promise(resolve => setTimeout(resolve, 100));
          setStats(mockTenantStats);
          setDonationHeads(mockDonationHeadsList);
        } catch (error) {
          console.error('Error fetching tenant stats:', error);
          setStats(null);
        } finally {
          setLoadingStats(false);
        }
      };
      loadStats();
    }, []);

    const currencySymbol = '₹';
    const tabLabels = ['Yearly', 'Monthly', 'Weekly', 'Daily'];

    return (
      <div data-testid="donation-tenant-page">
        <div data-testid="page-header">
          <h1>Tenant Dashboard</h1>
          <p data-testid="organization-name">Education Foundation</p>
        </div>

        {/* Quick Stats */}
        <div data-testid="quick-stats-container">
          <div data-testid="stats-card-total-donations">
            <h3>Total Donations</h3>
            {loadingStats ? (
              <div data-testid="loading-total-donations">Loading...</div>
            ) : (
              <div data-testid="total-donations-value">
                {currencySymbol}{stats?.totalDonations?.toLocaleString() || '0'}
              </div>
            )}
          </div>

          <div data-testid="stats-card-unique-donors">
            <h3>Unique Donors</h3>
            {loadingStats ? (
              <div data-testid="loading-unique-donors">Loading...</div>
            ) : (
              <div data-testid="unique-donors-value">
                {stats?.uniqueDonors || '0'}
              </div>
            )}
          </div>

          <div data-testid="stats-card-donation-heads">
            <h3>Donation Heads</h3>
            {loadingStats ? (
              <div data-testid="loading-donation-heads">Loading...</div>
            ) : (
              <div data-testid="donation-heads-value">
                {stats?.donationHeadsCount || '0'}
              </div>
            )}
          </div>

          <div data-testid="stats-card-top-head">
            <h3>Top Performing Head</h3>
            {loadingStats ? (
              <div data-testid="loading-top-head">Loading...</div>
            ) : (
              <div data-testid="top-head-value">
                {stats?.topHead || 'N/A'}
              </div>
            )}
          </div>
        </div>

        {/* Charts Section */}
        <div data-testid="charts-container">
          {/* Donation Trends Chart */}
          <div data-testid="donation-trends-chart">
            <h2>Donation Trends</h2>
            <p>Track your organization's donation patterns over time</p>
            
            <div data-testid="donation-trends-tabs">
              {tabLabels.map((label, index) => (
                <button
                  key={index}
                  data-testid={`trends-tab-${label.toLowerCase()}`}
                  onClick={() => setSelectedTab(index)}
                  className={selectedTab === index ? 'active' : ''}
                >
                  {label}
                </button>
              ))}
            </div>

            <div data-testid="donation-trends-chart-content">
              <div data-testid="mock-chart" data-type="area" data-height="350">
                Donation Trends Area Chart ({tabLabels[selectedTab]})
              </div>
            </div>
          </div>

          {/* Distribution Chart */}
          <div data-testid="donation-distribution-chart">
            <h2>Donation Distribution</h2>
            <p>Breakdown by Donation Head</p>
            
            <div data-testid="distribution-chart-content">
              <div data-testid="mock-chart" data-type="donut" data-height="400">
                Donation Distribution Donut Chart
              </div>
            </div>
          </div>

          {/* Donation Heads Performance */}
          <div data-testid="donation-heads-performance">
            <h2>Donation Heads Performance</h2>
            <p>Performance metrics for each donation category</p>
            
            <div data-testid="donation-heads-list">
              {donationHeads.map((head) => (
                <div key={head.id} data-testid={`donation-head-${head.id}`}>
                  <div data-testid={`head-name-${head.id}`}>{head.name}</div>
                  <div data-testid={`head-amount-${head.id}`}>
                    {currencySymbol}{head.totalAmount?.toLocaleString()}
                  </div>
                  <div data-testid={`head-percentage-${head.id}`}>
                    {head.percentage}%
                  </div>
                  <div data-testid={`head-progress-${head.id}`} style={{ width: `${head.percentage}%` }}>
                    Progress Bar
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activities */}
        <div data-testid="recent-activities">
          <h2>Recent Activities</h2>
          <div data-testid="activities-list">
            <div data-testid="activity-1">
              <span>New donation received - ₹5,000</span>
              <span>2 hours ago</span>
            </div>
            <div data-testid="activity-2">
              <span>Donation head updated - Healthcare Fund</span>
              <span>1 day ago</span>
            </div>
            <div data-testid="activity-3">
              <span>Monthly report generated</span>
              <span>3 days ago</span>
            </div>
          </div>
        </div>
      </div>
    );
  };
});

describe('DonationTenantPage', () => {
  const mockUser = {
    id: '1',
    name: 'Tenant User',
    organisationCategory: 'TENANT',
    orgId: 'tenant-org-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the donation tenant page with all main sections', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByTestId('donation-tenant-page')).toBeInTheDocument();
      expect(screen.getByTestId('page-header')).toBeInTheDocument();
      expect(screen.getByTestId('quick-stats-container')).toBeInTheDocument();
      expect(screen.getByTestId('charts-container')).toBeInTheDocument();
      expect(screen.getByTestId('recent-activities')).toBeInTheDocument();
    });

    it('displays the correct page title and organization', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByText('Tenant Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Education Foundation')).toBeInTheDocument();
    });

    it('renders all quick stats cards', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByTestId('stats-card-total-donations')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-unique-donors')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-donation-heads')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-top-head')).toBeInTheDocument();
    });

    it('renders all chart sections', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByTestId('donation-trends-chart')).toBeInTheDocument();
      expect(screen.getByTestId('donation-distribution-chart')).toBeInTheDocument();
      expect(screen.getByTestId('donation-heads-performance')).toBeInTheDocument();
    });
  });

  describe('Stats Loading and Display', () => {
    it('shows loading state initially', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByTestId('loading-total-donations')).toBeInTheDocument();
      expect(screen.getByTestId('loading-unique-donors')).toBeInTheDocument();
      expect(screen.getByTestId('loading-donation-heads')).toBeInTheDocument();
      expect(screen.getByTestId('loading-top-head')).toBeInTheDocument();
    });

    it('displays stats data after loading', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toBeInTheDocument();
        expect(screen.getByTestId('unique-donors-value')).toBeInTheDocument();
        expect(screen.getByTestId('donation-heads-value')).toBeInTheDocument();
        expect(screen.getByTestId('top-head-value')).toBeInTheDocument();
      });

      expect(screen.getByText('₹125,000')).toBeInTheDocument();
      expect(screen.getByText('45')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('Education')).toBeInTheDocument();
    });

    it('formats currency values correctly', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByText('₹125,000')).toBeInTheDocument();
      });
    });
  });

  describe('Chart Interactions', () => {
    it('renders chart tabs for donation trends', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByTestId('trends-tab-yearly')).toBeInTheDocument();
      expect(screen.getByTestId('trends-tab-monthly')).toBeInTheDocument();
      expect(screen.getByTestId('trends-tab-weekly')).toBeInTheDocument();
      expect(screen.getByTestId('trends-tab-daily')).toBeInTheDocument();
    });

    it('switches between chart tabs', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      // Initially should show yearly data
      expect(screen.getByText('Donation Trends Area Chart (Yearly)')).toBeInTheDocument();

      // Click monthly tab
      await user.click(screen.getByTestId('trends-tab-monthly'));
      expect(screen.getByText('Donation Trends Area Chart (Monthly)')).toBeInTheDocument();

      // Click weekly tab
      await user.click(screen.getByTestId('trends-tab-weekly'));
      expect(screen.getByText('Donation Trends Area Chart (Weekly)')).toBeInTheDocument();
    });

    it('renders chart components with correct props', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      // Check that charts are rendered with correct types
      const areaChart = screen.getAllByTestId('mock-chart').find(chart => 
        chart.getAttribute('data-type') === 'area'
      );
      const donutChart = screen.getAllByTestId('mock-chart').find(chart => 
        chart.getAttribute('data-type') === 'donut'
      );

      expect(areaChart).toBeInTheDocument();
      expect(donutChart).toBeInTheDocument();
    });
  });

  describe('Donation Heads Performance', () => {
    it('displays donation heads list after loading', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
        expect(screen.getByTestId('donation-head-2')).toBeInTheDocument();
        expect(screen.getByTestId('donation-head-3')).toBeInTheDocument();
        expect(screen.getByTestId('donation-head-4')).toBeInTheDocument();
        expect(screen.getByTestId('donation-head-5')).toBeInTheDocument();
      });
    });

    it('displays correct donation head information', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByText('Education Fund')).toBeInTheDocument();
        expect(screen.getByText('Healthcare Fund')).toBeInTheDocument();
        expect(screen.getByText('₹3,500')).toBeInTheDocument();
        expect(screen.getByText('₹2,800')).toBeInTheDocument();
        expect(screen.getByText('30.4%')).toBeInTheDocument();
        expect(screen.getByText('24.3%')).toBeInTheDocument();
      });
    });

    it('renders progress bars for each donation head', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('head-progress-1')).toBeInTheDocument();
        expect(screen.getByTestId('head-progress-2')).toBeInTheDocument();
        expect(screen.getByTestId('head-progress-3')).toBeInTheDocument();
        expect(screen.getByTestId('head-progress-4')).toBeInTheDocument();
        expect(screen.getByTestId('head-progress-5')).toBeInTheDocument();
      });
    });
  });

  describe('Recent Activities', () => {
    it('displays recent activities section', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByTestId('recent-activities')).toBeInTheDocument();
      expect(screen.getByText('Recent Activities')).toBeInTheDocument();
    });

    it('shows activity items with timestamps', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByText('New donation received - ₹5,000')).toBeInTheDocument();
      expect(screen.getByText('2 hours ago')).toBeInTheDocument();
      expect(screen.getByText('Donation head updated - Healthcare Fund')).toBeInTheDocument();
      expect(screen.getByText('1 day ago')).toBeInTheDocument();
      expect(screen.getByText('Monthly report generated')).toBeInTheDocument();
      expect(screen.getByText('3 days ago')).toBeInTheDocument();
    });
  });

  describe('Chart Titles and Descriptions', () => {
    it('displays correct chart titles', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByText('Donation Trends')).toBeInTheDocument();
      expect(screen.getByText('Donation Distribution')).toBeInTheDocument();
      expect(screen.getByText('Donation Heads Performance')).toBeInTheDocument();
    });

    it('displays correct chart descriptions', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByText('Track your organization\'s donation patterns over time')).toBeInTheDocument();
      expect(screen.getByText('Breakdown by Donation Head')).toBeInTheDocument();
      expect(screen.getByText('Performance metrics for each donation category')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles stats loading errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // Mock component that throws error during stats loading
      const ErrorComponent = () => {
        React.useEffect(() => {
          throw new Error('Stats loading failed');
        }, []);
        return <DonationTenantPage />;
      };

      // Component should still render without crashing
      expect(() => {
        renderWithProviders(<ErrorComponent />, { user: mockUser });
      }).not.toThrow();

      consoleError.mockRestore();
    });

    it('displays fallback content when stats fail to load', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      // Even if stats fail, the page structure should remain
      expect(screen.getByTestId('donation-tenant-page')).toBeInTheDocument();
      expect(screen.getByTestId('quick-stats-container')).toBeInTheDocument();
      expect(screen.getByTestId('charts-container')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Tenant Dashboard');
      expect(screen.getByRole('heading', { level: 2, name: 'Donation Trends' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 2, name: 'Donation Distribution' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 2, name: 'Donation Heads Performance' })).toBeInTheDocument();
    });

    it('has accessible tab navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      const yearlyTab = screen.getByTestId('trends-tab-yearly');
      const monthlyTab = screen.getByTestId('trends-tab-monthly');

      // Tabs should be focusable
      await user.tab();
      expect(yearlyTab).toHaveFocus();

      await user.tab();
      expect(monthlyTab).toHaveFocus();
    });

    it('supports keyboard navigation for tabs', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      const monthlyTab = screen.getByTestId('trends-tab-monthly');
      monthlyTab.focus();

      await user.keyboard('{Enter}');
      expect(screen.getByText('Donation Trends Area Chart (Monthly)')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('renders efficiently without unnecessary re-renders', () => {
      const { rerender } = renderWithProviders(<DonationTenantPage />, { user: mockUser });
      
      // Re-render with same props should not cause issues
      rerender(<DonationTenantPage />);
      
      expect(screen.getByTestId('donation-tenant-page')).toBeInTheDocument();
    });

    it('handles large datasets gracefully', async () => {
      renderWithProviders(<DonationTenantPage />, { user: mockUser });

      // Component should render without performance issues
      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toBeInTheDocument();
      });

      expect(screen.getByTestId('donation-tenant-page')).toBeInTheDocument();
    });
  });
});
