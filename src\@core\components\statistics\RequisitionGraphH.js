import React, { useState, useEffect, useContext } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import authConfig from "src/configs/auth";
import Box from '@mui/material/Box'
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import axios from "axios";
import FallbackSpinner from "../spinner";
import { Card } from "@mui/material";

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const RequisitionGraphH = () => {
  const { user } = useContext(AuthContext);
  const router = useRouter();

  const [responseBody, setResponseBody] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      let url;
      if (user?.roleId === authConfig?.superAdminRoleId) {
        url = getUrl(
          authConfig?.statisticsEndpointGraphs +
            "/admin/status-count-service-requisitions"
        );
      } else {
        url = getUrl(
          authConfig?.statisticsEndpointGraphs + "/status-count-service-requisitions"
        );
      }

      let headers;
      if (user?.roleId === authConfig?.superAdminRoleId) {
        headers = getAuthorizationHeaders({
          accept: authConfig?.STATISTICS_GET_ADMIN_REQ_V1,
        });
      } else {
        headers = getAuthorizationHeaders({
          accept: authConfig?.STATISTICS_GET_REQ_V1,
        });
      }

      try {
        const response = await axios?.get(url, headers);
        setResponseBody(response?.data?.serviceRequisitionsBarChatList);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.roleId]);

  const predefinedColors = [
    "#FF5733", // Red
    "#33FF57", // Green
    "#3357FF", // Blue
    "#F1C40F", // Yellow
    "#8E44AD", // Purple
    "#E67E22", // Orange
    "#2ECC71", // Emerald
    "#3498DB", // Peter River
    "#9B59B6", // Amethyst
    "#E74C3C", // Alizarin
    "#16A085", // Teal
    "#27AE60", // Green Sea
    "#2980B9", // Belize Hole
    "#C0392B", // Pomegranate
    "#D35400", // Pumpkin
    "#34495E", // Wet Asphalt
    "#7F8C8D", // Asbestos
    "#BDC3C7", // Silver
    "#95A5A6", // Concrete
    "#1ABC9C", // Turquoise
    "#2C3E50", // Midnight Blue
    "#F39C12", // Sun Flower
    "#E67E22", // Carrot
    "#ECF0F1", // Clouds
    "#D5DBDB", // Light Gray
  ];

  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;
    const clickedItem = responseBody[clickedIndex];

    const uuidStatus = clickedItem?.status; 

    //Navigate to service-requisition page with the UUID status
    if (uuidStatus) {
      router.push({
        pathname: "/service-requisitions",
        query: { status: uuidStatus },
      });
    }
  };

  const state = {
    series: [
      {
        name: "Count",
        data: responseBody?.map((item) => item?.count),
      },
    ],
    options: {
      chart: {
        type: "bar",
        height: 500,
        events: {
          dataPointSelection: handleBarClick, // Add click event handler
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "50%",
          distributed: true, // Ensures different colors for each bar
        },
      },
      xaxis: {
        categories: responseBody?.map((item) => item?.name),
        labels: {
          style: {
            fontSize: "6px",
            fontWeight: "bold",
          },
          rotate: -45,
        },
        tooltip: {
          enabled: true,
        },
      },
      yaxis: {
        labels: {
          formatter: (val) => `${val}`,
          style: {
            fontSize: "10px",
          },
        },
        title: {
          text: "No. of Service Requisitions",
          style: {
            fontSize: "12px",
            fontWeight: "bold",
          },
        },
      },
      colors: responseBody?.map(
        (_, index) => predefinedColors[index % predefinedColors?.length]
      ),
      dataLabels: {
        enabled: true,
        formatter: (val) => `${val}`,
        style: {
          fontSize: "12px",
        },
      },
      tooltip: {
        enabled: true,
        y: {
          formatter: (val) => `${val} items`,
        },
      },
      legend: {
        show: false,
      },
    },
  };

  return (
    <Card sx={{ p: 3 }}>
      <label
        htmlFor="chart"
        style={{
          display: "block",
          textAlign: "center",
          fontSize: "18px",
          fontWeight: "bold",
          marginTop: "10px",
        }}
      >
        Service Requisition Workflow Stages
      </label>

      {loading ? (
        <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="60vh"
      >
        <FallbackSpinner />
      </Box>
      ) : (
        <div id="chart">
          <ApexChart
            options={state?.options}
            series={state?.series}
            type="bar"
            height={350}
          />
        </div>
      )}
    </Card>
  );
};

export default RequisitionGraphH;
