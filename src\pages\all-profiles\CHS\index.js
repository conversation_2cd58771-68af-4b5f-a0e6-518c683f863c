import { <PERSON><PERSON>, <PERSON>, Grid, Typography } from "@mui/material";


import NavTabsProfiles from "src/@core/components/custom-components/NavTabsProfiles";
import UsersOverView from "./UsersOverview";
import Invoices from "src/pages/invoices/Invoices";
import WorkOrderDetails from "src/pages/work-orders/WorkOrderDetails";
import QuotationDetails from "src/pages/quotations-old/QuotationDetails";
import UserViewSecurity from "../../security";
import Notifications from "src/pages/notifications";

const AllUsers = () => {
  return (
    <Card>
      <NavTabsProfiles
        tabContent1={
          <>
          <UsersOverView/>
          </>
        }
        tabContent2={
          <>
            <QuotationDetails/>
          </>
        }
        tabContent3={
            <>
             <WorkOrderDetails/>
            </>
        }
        tabContent4={
            <>
              
            </>
        }
        tabContent5={
            <>
             <Invoices/>
            </>
        }
        tabContent6={
            <>
             <UserViewSecurity/>
            </>
        }
        tabContent7={
            <>
             
            </>
        }
        tabContent8={
            <>
             <Notifications/>
            </>
        }
        tabContent9={
            <>
             
            </>
        }
      />
    </Card>
  );
};

export default AllUsers;
