// ** MUI Imports

import Box from '@mui/material/Box'
import MenuItem from '@mui/material/MenuItem'
import InputLabel from '@mui/material/InputLabel'
import FormControl from '@mui/material/FormControl'
import Select from '@mui/material/Select'
import {Controller } from 'react-hook-form'


const SelectBasic = props => {

  // ** Props
  const { id, label, nameArray, register,defaultValue } = props
  return (
    <>
      <Box sx={{ '& > *': { mt: 6, mr: 6 } }}>
        <FormControl sx={{ maxWidth: '100%', minWidth: '100%' }}>
          <InputLabel id={id + '-label'}>{label}</InputLabel>
          <Select {...register(id)} label={label}  defaultValue={defaultValue} id={id} 
            labelId={id + '-label'}
            MenuProps={{
              PaperProps: {
                style: {
                  maxHeight: 200, 
                },
              },
            }}
          >

            {nameArray.map(name => (
              <MenuItem key={name.value} value={name.value}>
                {name.key}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </>
  )
}
export default SelectBasic