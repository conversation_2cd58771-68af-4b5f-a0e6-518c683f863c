import { useContext, useMemo } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import StaticLeftMenu from 'src/utils/StaticLeftMenu';

const ALLOWED_PERMISSIONS_SET = new Set([1, 3, 5, 7, 15]);
const PAGE_TYPE = "PAGE";
const SUB_PAGE_TYPE = "SUB_PAGE";
const LEFT_MENU_NAME = "Left_Menu";

// Preprocess Static Left Menu for faster lookup
const leftMenuMap = new Map(StaticLeftMenu.map(item => [item?.name, item]));

const Navigation = () => {
  const { user } = useContext(AuthContext);

  const permissionsList = useMemo(() => user?.permissionsDTOList || [], [user?.permissionsDTOList]);

  const filterSubPages = (children, parentName) => {
    const parentStaticItem = leftMenuMap.get(parentName);
    if (!parentStaticItem?.children) return null;

    const filteredSubPages = children
      .filter(subChild => subChild?.type === SUB_PAGE_TYPE && ALLOWED_PERMISSIONS_SET.has(subChild?.permissions))
      .map(subChild => {
        const staticSubItem = parentStaticItem.children?.find(item => item?.name === subChild?.name);
        if (!staticSubItem) return null;

        return {
          title: staticSubItem?.title,
          path: staticSubItem?.path,
          icon: staticSubItem?.icon,
          displayNumber: subChild?.displayNumber || 0,
          permissions: subChild?.permissions,
          children: subChild?.children ? filterSubPages(subChild.children, subChild.name) : null,
        };
      })
      .filter(Boolean)
      .sort((a, b) => (a?.displayNumber || 0) - (b?.displayNumber || 0));

    return filteredSubPages.length > 0 ? filteredSubPages : null;
  };

  const leftNavArray = useMemo(() => {
    const leftMenu = permissionsList?.find(item => item?.name === LEFT_MENU_NAME);
    if (!leftMenu?.children) return [];

    return leftMenu.children
      .filter(child => child?.type === PAGE_TYPE && ALLOWED_PERMISSIONS_SET.has(child?.permissions))
      .map(child => {
        const staticMenuItem = leftMenuMap.get(child?.name);
        if (!staticMenuItem) return null;

        return {
          title: staticMenuItem?.title,
          path: staticMenuItem?.path,
          icon: staticMenuItem?.icon,
          displayNumber: child?.displayNumber || 0,
          permissions: child?.permissions,
          children: child?.children ? filterSubPages(child.children, child.name) : null,
        };
      })
      .filter(Boolean)
      .sort((a, b) => (a?.displayNumber || 0) - (b?.displayNumber || 0));
  }, [permissionsList]);

  return leftNavArray;
};

export default Navigation;
