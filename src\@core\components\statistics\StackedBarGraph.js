import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const StackedBarGraph = () => {
  const state = {
    series: [
      {
        name: 'Enrolled <PERSON>',
        data: [30, 50, 70, 90, 110, 130, 150] // Example data with a difference of 20K each year
      },
      {
        name: 'New SP',
        data: [20, 40, 60, 80, 100, 120, 140] // Example data with a difference of 20K each year
      },
      {
        name: 'Avoid SP',
        data: [10, 30, 50, 70, 90, 110, 130] // Example data with a difference of 20K each year
      }
    ],
    options: {
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,
      },
      plotOptions: {
        bar: {
          horizontal: true,
          dataLabels: {
            total: {
              enabled: true,
              offsetX: 0,
              style: {
                fontSize: '13px',
                fontWeight: 900
              }
            }
          }
        },
      },
      stroke: {
        width: 1,
        colors: ['#fff']
      },
      title: {
        text: 'SPs Data Over Years'
      },
      xaxis: {
        categories: [2018, 2019, 2020, 2021, 2022, 2023, 2024], // Years
        labels: {
          formatter: function (val) {
            return val + "K"; // Display data in thousands
          }
        }
      },
      yaxis: {
        title: {
          text: undefined
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + "K"; // Tooltip formatter to show data in thousands
          }
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left',
        offsetX: 40
      }
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="bar" height={350} />
      </div>
    </div>
  );
};

export default StackedBarGraph;
