import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DonationAdminPage from '@/pages/donation-admin/index.js';
import { renderWithProviders, mockStatsData, mockChartData, mockDistributionData } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the DonationAdminPage component
jest.mock('@/pages/donation-admin/index.js', () => {
  return function MockDonationAdminPage() {
    const [stats, setStats] = React.useState(null);
    const [loadingStats, setLoadingStats] = React.useState(true);
    const [selectedTab, setSelectedTab] = React.useState(0);

    // Mock data
    const mockStatsData = {
      totalDonations: 512986,
      uniqueDonors: 20,
      last30DaysDonations: 0,
      averageDonation: 25649,
    };

    const mockNgoSignupData = {
      yearly: { series: [{ name: 'NGO Signups', data: [15, 22, 18, 25, 30, 28, 35] }], categories: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'] },
      monthly: { series: [{ name: 'NGO Signups', data: [2, 1, 3, 2, 3, 1] }], categories: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
      weekly: { series: [{ name: 'NGO Signups', data: [0, 1, 2, 1, 1, 0, 1] }], categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
      daily: { series: [{ name: 'NGO Signups', data: [0, 0, 1, 0, 0, 0] }], categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'] },
    };

    const mockDonationData = {
      yearly: { series: [{ name: 'Total Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }], categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
      monthly: { series: [{ name: 'Total Donations', data: [200, 150, 300, 250, 350, 100] }], categories: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'] },
      weekly: { series: [{ name: 'Total Donations', data: [50, 30, 80, 60, 90, 70, 100] }], categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
      daily: { series: [{ name: 'Total Donations', data: [10, 15, 5, 20, 12, 25, 8] }], categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'] },
    };

    const mockDistributionData = {
      series: [4400, 5500, 3500, 4300, 2400],
      labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
    };

    // Simulate data loading
    React.useEffect(() => {
      const loadStats = async () => {
        setLoadingStats(true);
        try {
          await new Promise(resolve => setTimeout(resolve, 100));
          setStats(mockStatsData);
        } catch (error) {
          console.error('Error fetching stats data:', error);
          setStats(null);
        } finally {
          setLoadingStats(false);
        }
      };
      loadStats();
    }, []);

    const currencySymbol = '₹';
    const tabLabels = ['Yearly', 'Monthly', 'Weekly', 'Daily'];

    return (
      <div data-testid="donation-admin-page">
        <div data-testid="page-header">
          <h1>Donation Admin Dashboard</h1>
        </div>

        {/* Stats Cards */}
        <div data-testid="stats-cards-container">
          <div data-testid="stats-card-total-donations">
            <h3>Total Donations</h3>
            {loadingStats ? (
              <div data-testid="loading-total-donations">Loading...</div>
            ) : (
              <div data-testid="total-donations-value">
                {currencySymbol}{stats?.totalDonations?.toLocaleString() || '0'}
              </div>
            )}
          </div>

          <div data-testid="stats-card-unique-donors">
            <h3>Unique Donors</h3>
            {loadingStats ? (
              <div data-testid="loading-unique-donors">Loading...</div>
            ) : (
              <div data-testid="unique-donors-value">
                {stats?.uniqueDonors || '0'}
              </div>
            )}
          </div>

          <div data-testid="stats-card-last-30-days">
            <h3>Last 30 Days</h3>
            {loadingStats ? (
              <div data-testid="loading-last-30-days">Loading...</div>
            ) : (
              <div data-testid="last-30-days-value">
                {stats?.last30DaysDonations ?? '0'} donations
              </div>
            )}
          </div>

          <div data-testid="stats-card-average-donation">
            <h3>Average Donation</h3>
            {loadingStats ? (
              <div data-testid="loading-average-donation">Loading...</div>
            ) : (
              <div data-testid="average-donation-value">
                {currencySymbol}{stats?.averageDonation?.toLocaleString() || '0'}
              </div>
            )}
          </div>
        </div>

        {/* Charts Section */}
        <div data-testid="charts-container">
          {/* NGO Signup Chart */}
          <div data-testid="ngo-signup-chart">
            <h2>NGO Signups Overview</h2>
            <p>Number of NGOs registered over different periods</p>
            
            <div data-testid="ngo-signup-tabs">
              {tabLabels.map((label, index) => (
                <button
                  key={index}
                  data-testid={`ngo-tab-${label.toLowerCase()}`}
                  onClick={() => setSelectedTab(index)}
                  className={selectedTab === index ? 'active' : ''}
                >
                  {label}
                </button>
              ))}
            </div>

            <div data-testid="ngo-signup-chart-content">
              <div data-testid="mock-chart" data-type="bar" data-height="300">
                NGO Signup Bar Chart ({tabLabels[selectedTab]})
              </div>
            </div>
          </div>

          {/* Total Donations Chart */}
          <div data-testid="total-donations-chart">
            <h2>Total Donations Growth</h2>
            <p>Donation trends over different periods</p>
            
            <div data-testid="donations-chart-tabs">
              {tabLabels.map((label, index) => (
                <button
                  key={index}
                  data-testid={`donations-tab-${label.toLowerCase()}`}
                  onClick={() => setSelectedTab(index)}
                  className={selectedTab === index ? 'active' : ''}
                >
                  {label}
                </button>
              ))}
            </div>

            <div data-testid="total-donations-chart-content">
              <div data-testid="mock-chart" data-type="line" data-height="300">
                Total Donations Line Chart ({tabLabels[selectedTab]})
              </div>
            </div>
          </div>

          {/* Distribution Pie Chart */}
          <div data-testid="donation-distribution-chart">
            <h2>Donation Distribution</h2>
            <p>Breakdown by Donation Head</p>
            
            <div data-testid="distribution-chart-content">
              <div data-testid="mock-chart" data-type="pie" data-height="350">
                Donation Distribution Pie Chart
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
});

describe('DonationAdminPage', () => {
  const mockUser = {
    id: '1',
    name: 'Admin User',
    organisationCategory: 'SUPER_ADMIN',
    orgId: 'admin-org',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the donation admin page with all main sections', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByTestId('donation-admin-page')).toBeInTheDocument();
      expect(screen.getByTestId('page-header')).toBeInTheDocument();
      expect(screen.getByTestId('stats-cards-container')).toBeInTheDocument();
      expect(screen.getByTestId('charts-container')).toBeInTheDocument();
    });

    it('displays the correct page title', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByText('Donation Admin Dashboard')).toBeInTheDocument();
    });

    it('renders all stats cards', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByTestId('stats-card-total-donations')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-unique-donors')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-last-30-days')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-average-donation')).toBeInTheDocument();
    });

    it('renders all chart sections', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByTestId('ngo-signup-chart')).toBeInTheDocument();
      expect(screen.getByTestId('total-donations-chart')).toBeInTheDocument();
      expect(screen.getByTestId('donation-distribution-chart')).toBeInTheDocument();
    });
  });

  describe('Stats Cards Loading and Display', () => {
    it('shows loading state initially', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByTestId('loading-total-donations')).toBeInTheDocument();
      expect(screen.getByTestId('loading-unique-donors')).toBeInTheDocument();
      expect(screen.getByTestId('loading-last-30-days')).toBeInTheDocument();
      expect(screen.getByTestId('loading-average-donation')).toBeInTheDocument();
    });

    it('displays stats data after loading', async () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toBeInTheDocument();
        expect(screen.getByTestId('unique-donors-value')).toBeInTheDocument();
        expect(screen.getByTestId('last-30-days-value')).toBeInTheDocument();
        expect(screen.getByTestId('average-donation-value')).toBeInTheDocument();
      });

      expect(screen.getByText('₹512,986')).toBeInTheDocument();
      expect(screen.getByText('20')).toBeInTheDocument();
      expect(screen.getByText('0 donations')).toBeInTheDocument();
      expect(screen.getByText('₹25,649')).toBeInTheDocument();
    });

    it('formats currency values correctly', async () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByText('₹512,986')).toBeInTheDocument();
        expect(screen.getByText('₹25,649')).toBeInTheDocument();
      });
    });
  });

  describe('Chart Interactions', () => {
    it('renders chart tabs for NGO signups', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByTestId('ngo-tab-yearly')).toBeInTheDocument();
      expect(screen.getByTestId('ngo-tab-monthly')).toBeInTheDocument();
      expect(screen.getByTestId('ngo-tab-weekly')).toBeInTheDocument();
      expect(screen.getByTestId('ngo-tab-daily')).toBeInTheDocument();
    });

    it('renders chart tabs for total donations', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByTestId('donations-tab-yearly')).toBeInTheDocument();
      expect(screen.getByTestId('donations-tab-monthly')).toBeInTheDocument();
      expect(screen.getByTestId('donations-tab-weekly')).toBeInTheDocument();
      expect(screen.getByTestId('donations-tab-daily')).toBeInTheDocument();
    });

    it('switches between chart tabs', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      // Initially should show yearly data
      expect(screen.getByText('NGO Signup Bar Chart (Yearly)')).toBeInTheDocument();

      // Click monthly tab
      await user.click(screen.getByTestId('ngo-tab-monthly'));
      expect(screen.getByText('NGO Signup Bar Chart (Monthly)')).toBeInTheDocument();

      // Click weekly tab
      await user.click(screen.getByTestId('ngo-tab-weekly'));
      expect(screen.getByText('NGO Signup Bar Chart (Weekly)')).toBeInTheDocument();
    });

    it('renders chart components with correct props', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      // Check that charts are rendered with correct types
      const barChart = screen.getAllByTestId('mock-chart').find(chart => 
        chart.getAttribute('data-type') === 'bar'
      );
      const lineChart = screen.getAllByTestId('mock-chart').find(chart => 
        chart.getAttribute('data-type') === 'line'
      );
      const pieChart = screen.getAllByTestId('mock-chart').find(chart => 
        chart.getAttribute('data-type') === 'pie'
      );

      expect(barChart).toBeInTheDocument();
      expect(lineChart).toBeInTheDocument();
      expect(pieChart).toBeInTheDocument();
    });
  });

  describe('Chart Titles and Descriptions', () => {
    it('displays correct chart titles', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByText('NGO Signups Overview')).toBeInTheDocument();
      expect(screen.getByText('Total Donations Growth')).toBeInTheDocument();
      expect(screen.getByText('Donation Distribution')).toBeInTheDocument();
    });

    it('displays correct chart descriptions', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByText('Number of NGOs registered over different periods')).toBeInTheDocument();
      expect(screen.getByText('Donation trends over different periods')).toBeInTheDocument();
      expect(screen.getByText('Breakdown by Donation Head')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('renders stats cards in grid layout', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      const statsContainer = screen.getByTestId('stats-cards-container');
      expect(statsContainer).toBeInTheDocument();
      
      // All 4 stats cards should be present
      expect(screen.getByTestId('stats-card-total-donations')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-unique-donors')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-last-30-days')).toBeInTheDocument();
      expect(screen.getByTestId('stats-card-average-donation')).toBeInTheDocument();
    });

    it('renders charts in proper layout', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      const chartsContainer = screen.getByTestId('charts-container');
      expect(chartsContainer).toBeInTheDocument();
      
      // All 3 chart sections should be present
      expect(screen.getByTestId('ngo-signup-chart')).toBeInTheDocument();
      expect(screen.getByTestId('total-donations-chart')).toBeInTheDocument();
      expect(screen.getByTestId('donation-distribution-chart')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles stats loading errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // Mock component that throws error during stats loading
      const ErrorComponent = () => {
        React.useEffect(() => {
          throw new Error('Stats loading failed');
        }, []);
        return <DonationAdminPage />;
      };

      // Component should still render without crashing
      expect(() => {
        renderWithProviders(<ErrorComponent />, { user: mockUser });
      }).not.toThrow();

      consoleError.mockRestore();
    });

    it('displays fallback content when stats fail to load', async () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      // Even if stats fail, the page structure should remain
      expect(screen.getByTestId('donation-admin-page')).toBeInTheDocument();
      expect(screen.getByTestId('stats-cards-container')).toBeInTheDocument();
      expect(screen.getByTestId('charts-container')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Donation Admin Dashboard');
      expect(screen.getByRole('heading', { level: 2, name: 'NGO Signups Overview' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 2, name: 'Total Donations Growth' })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 2, name: 'Donation Distribution' })).toBeInTheDocument();
    });

    it('has accessible tab navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      const yearlyTab = screen.getByTestId('ngo-tab-yearly');
      const monthlyTab = screen.getByTestId('ngo-tab-monthly');

      // Tabs should be focusable
      await user.tab();
      expect(yearlyTab).toHaveFocus();

      await user.tab();
      expect(monthlyTab).toHaveFocus();
    });

    it('supports keyboard navigation for tabs', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      const monthlyTab = screen.getByTestId('ngo-tab-monthly');
      monthlyTab.focus();

      await user.keyboard('{Enter}');
      expect(screen.getByText('NGO Signup Bar Chart (Monthly)')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('renders efficiently without unnecessary re-renders', () => {
      const { rerender } = renderWithProviders(<DonationAdminPage />, { user: mockUser });

      // Re-render with same props should not cause issues
      rerender(<DonationAdminPage />);

      expect(screen.getByTestId('donation-admin-page')).toBeInTheDocument();
    });

    it('handles large datasets gracefully', async () => {
      renderWithProviders(<DonationAdminPage />, { user: mockUser });

      // Component should render without performance issues
      await waitFor(() => {
        expect(screen.getByTestId('total-donations-value')).toBeInTheDocument();
      });

      expect(screen.getByTestId('donation-admin-page')).toBeInTheDocument();
    });
  });
});
