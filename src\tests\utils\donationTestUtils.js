import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// Create a test theme
const testTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Mock contexts
const mockAuthContext = {
  donationHeadDetails: null,
  user: { id: '1', name: 'Test User', role: 'admin' },
  isAuthenticated: true,
};

const mockRBACContext = {
  canAccessActions: jest.fn(() => true),
  canAccessHeads: jest.fn(() => true),
  hasPermission: jest.fn(() => true),
};

// Test wrapper component
const TestWrapper = ({ children }) => {
  return (
    <ThemeProvider theme={testTheme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

// Enhanced render function with providers
export const renderWithProviders = (component, options = {}) => {
  return render(component, {
    wrapper: TestWrapper,
    ...options,
  });
};

// Simple render function for basic tests
export const renderComponent = (component) => {
  return renderWithProviders(component);
};

// Mock data generators
export const mockDonationHead = (overrides = {}) => ({
  id: '1',
  name: 'Test Donation Head',
  description: 'Test description',
  orgId: 'org-1',
  isActive: true,
  createdOn: '2024-01-01T00:00:00Z',
  updatedOn: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockTenant = (overrides = {}) => ({
  value: 'org-1',
  key: 'Test Organization',
  label: 'Test Organization',
  ...overrides,
});

export const mockApiResponse = (data, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
});

// Mock chart data
export const mockChartData = {
  series: [{ name: 'Test Data', data: [10, 20, 30, 40, 50] }],
  categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
};

// Export mock contexts for use in tests
export { mockAuthContext, mockRBACContext };
