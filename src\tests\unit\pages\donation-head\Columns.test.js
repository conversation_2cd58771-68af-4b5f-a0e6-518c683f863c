import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import useColumns from '@/pages/donation-head/Columns.js';
import { renderWithProviders, mockDonationHead, mockTenant } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
setupAllMocks();

// Mock the useColumns hook
jest.mock('@/pages/donation-head/Columns.js', () => {
  return function useColumns({
    onClickViewProfile,
    onClickToggleStatus,
    tenantsList = [],
    user = null,
  }) {
    const mockColumns = [
      {
        field: 'name',
        headerName: 'Name',
        flex: 0.13,
        minWidth: 120,
      },
      user?.organisationCategory === 'SUPER_ADMIN' ? {
        field: 'orgId',
        headerName: 'NGO Name',
        flex: 0.13,
        minWidth: 120,
        renderCell: (params) => {
          const org = tenantsList?.find(
            (item) => item?.value === params?.row?.orgId
          );
          return (
            <div data-testid={`org-cell-${params.row.id}`}>
              <span title={org ? org?.key : ''}>{org ? org?.key : ''}</span>
            </div>
          );
        },
      } : null,
      {
        field: 'description',
        headerName: 'Description',
        flex: 0.13,
        minWidth: 120,
      },
      {
        field: 'createdOn',
        headerName: 'Created On',
        flex: 0.07,
        minWidth: 120,
        valueFormatter: (params) => params.value?.split('T')[0],
      },
      {
        field: 'updatedOn',
        headerName: 'Updated On',
        flex: 0.07,
        minWidth: 120,
        valueFormatter: (params) => params.value?.split('T')[0],
      },
      {
        field: 'isActive',
        headerName: 'Status',
        flex: 0.07,
        minWidth: 120,
        renderCell: (params) => {
          const status = params.row.isActive ? 'Active' : 'InActive';
          const color = params.row.isActive ? 'success' : 'error';
          return (
            <div data-testid={`status-cell-${params.row.id}`}>
              <span data-color={color}>{status}</span>
            </div>
          );
        },
      },
      {
        field: 'actions',
        headerName: 'Actions',
        flex: 0.07,
        minWidth: 120,
        sortable: false,
        renderCell: (params) => {
          const [menuOpen, setMenuOpen] = React.useState(false);
          const [anchorEl, setAnchorEl] = React.useState(null);

          const handleMenuClick = (event) => {
            setAnchorEl(event.currentTarget);
            setMenuOpen(true);
          };

          const handleMenuClose = () => {
            setMenuOpen(false);
            setAnchorEl(null);
          };

          const handleEdit = () => {
            onClickViewProfile(params.row);
            handleMenuClose();
          };

          const handleToggleStatus = () => {
            onClickToggleStatus(params.row);
            handleMenuClose();
          };

          return (
            <div data-testid={`actions-cell-${params.row.id}`}>
              <button
                data-testid={`actions-menu-${params.row.id}`}
                onClick={handleMenuClick}
                aria-label="Actions menu"
              >
                ⋮
              </button>
              
              {menuOpen && (
                <div 
                  data-testid={`actions-menu-items-${params.row.id}`}
                  role="menu"
                >
                  <button
                    data-testid={`edit-action-${params.row.id}`}
                    onClick={handleEdit}
                    role="menuitem"
                  >
                    Edit
                  </button>
                  <button
                    data-testid={`toggle-status-action-${params.row.id}`}
                    onClick={handleToggleStatus}
                    role="menuitem"
                  >
                    {params.row.isActive ? 'Deactivate' : 'Activate'}
                  </button>
                </div>
              )}
            </div>
          );
        },
      },
    ].filter(Boolean);

    return mockColumns;
  };
});

// Test component to render columns
const TestDataGrid = ({ columns, rows }) => {
  return (
    <div data-testid="test-data-grid">
      <div data-testid="grid-headers">
        {columns.map((col, index) => (
          <div key={index} data-testid={`header-${col.field}`}>
            {col.headerName}
          </div>
        ))}
      </div>
      
      <div data-testid="grid-rows">
        {rows.map((row) => (
          <div key={row.id} data-testid={`row-${row.id}`}>
            {columns.map((col) => (
              <div key={col.field} data-testid={`cell-${row.id}-${col.field}`}>
                {col.renderCell ? (
                  col.renderCell({ row, value: row[col.field] })
                ) : col.valueFormatter ? (
                  col.valueFormatter({ value: row[col.field] })
                ) : (
                  row[col.field]
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

describe('useColumns Hook', () => {
  const mockTenants = [
    mockTenant({ value: 'org-1', key: 'Organization 1' }),
    mockTenant({ value: 'org-2', key: 'Organization 2' }),
  ];

  const mockDonationHeads = [
    mockDonationHead({
      id: '1',
      name: 'Education Fund',
      description: 'Education related donations',
      orgId: 'org-1',
      isActive: true,
      createdOn: '2024-01-01T10:00:00Z',
      updatedOn: '2024-01-02T10:00:00Z',
    }),
    mockDonationHead({
      id: '2',
      name: 'Healthcare Fund',
      description: 'Healthcare related donations',
      orgId: 'org-2',
      isActive: false,
      createdOn: '2024-01-03T10:00:00Z',
      updatedOn: '2024-01-04T10:00:00Z',
    }),
  ];

  const mockCallbacks = {
    onClickViewProfile: jest.fn(),
    onClickToggleStatus: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Column Configuration', () => {
    it('returns correct column structure for tenant user', () => {
      const tenantUser = {
        id: '1',
        organisationCategory: 'TENANT',
        orgId: 'org-1',
      };

      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: tenantUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: tenantUser });

      // Should have basic columns but not org column
      expect(screen.getByTestId('header-name')).toBeInTheDocument();
      expect(screen.getByTestId('header-description')).toBeInTheDocument();
      expect(screen.getByTestId('header-createdOn')).toBeInTheDocument();
      expect(screen.getByTestId('header-updatedOn')).toBeInTheDocument();
      expect(screen.getByTestId('header-isActive')).toBeInTheDocument();
      expect(screen.getByTestId('header-actions')).toBeInTheDocument();
      
      // Should not have org column for tenant users
      expect(screen.queryByTestId('header-orgId')).not.toBeInTheDocument();
    });

    it('returns correct column structure for super admin user', () => {
      const superAdminUser = {
        id: '1',
        organisationCategory: 'SUPER_ADMIN',
        orgId: 'org-1',
      };

      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      // Should have all columns including org column
      expect(screen.getByTestId('header-name')).toBeInTheDocument();
      expect(screen.getByTestId('header-orgId')).toBeInTheDocument();
      expect(screen.getByTestId('header-description')).toBeInTheDocument();
      expect(screen.getByTestId('header-createdOn')).toBeInTheDocument();
      expect(screen.getByTestId('header-updatedOn')).toBeInTheDocument();
      expect(screen.getByTestId('header-isActive')).toBeInTheDocument();
      expect(screen.getByTestId('header-actions')).toBeInTheDocument();
    });
  });

  describe('Cell Rendering', () => {
    const superAdminUser = {
      id: '1',
      organisationCategory: 'SUPER_ADMIN',
      orgId: 'org-1',
    };

    it('renders organization name correctly', () => {
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      expect(screen.getByTestId('org-cell-1')).toBeInTheDocument();
      expect(screen.getByText('Organization 1')).toBeInTheDocument();
    });

    it('renders status correctly for active items', () => {
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const statusCell = screen.getByTestId('status-cell-1');
      expect(statusCell).toBeInTheDocument();
      expect(statusCell.querySelector('[data-color="success"]')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });

    it('renders status correctly for inactive items', () => {
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const statusCell = screen.getByTestId('status-cell-2');
      expect(statusCell).toBeInTheDocument();
      expect(statusCell.querySelector('[data-color="error"]')).toBeInTheDocument();
      expect(screen.getByText('InActive')).toBeInTheDocument();
    });

    it('formats dates correctly', () => {
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      expect(screen.getByText('2024-01-01')).toBeInTheDocument();
      expect(screen.getByText('2024-01-02')).toBeInTheDocument();
    });
  });

  describe('Actions Menu', () => {
    const superAdminUser = {
      id: '1',
      organisationCategory: 'SUPER_ADMIN',
      orgId: 'org-1',
    };

    it('renders actions menu button', () => {
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      expect(screen.getByTestId('actions-menu-1')).toBeInTheDocument();
      expect(screen.getByTestId('actions-menu-2')).toBeInTheDocument();
    });

    it('opens actions menu when clicked', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-1');
      await user.click(menuButton);

      expect(screen.getByTestId('actions-menu-items-1')).toBeInTheDocument();
      expect(screen.getByTestId('edit-action-1')).toBeInTheDocument();
      expect(screen.getByTestId('toggle-status-action-1')).toBeInTheDocument();
    });

    it('calls onClickViewProfile when edit is clicked', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-1');
      await user.click(menuButton);

      const editButton = screen.getByTestId('edit-action-1');
      await user.click(editButton);

      expect(mockCallbacks.onClickViewProfile).toHaveBeenCalledWith(mockDonationHeads[0]);
    });

    it('calls onClickToggleStatus when toggle status is clicked', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-1');
      await user.click(menuButton);

      const toggleButton = screen.getByTestId('toggle-status-action-1');
      await user.click(toggleButton);

      expect(mockCallbacks.onClickToggleStatus).toHaveBeenCalledWith(mockDonationHeads[0]);
    });

    it('shows correct toggle status text for active items', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-1');
      await user.click(menuButton);

      expect(screen.getByText('Deactivate')).toBeInTheDocument();
    });

    it('shows correct toggle status text for inactive items', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-2');
      await user.click(menuButton);

      expect(screen.getByText('Activate')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    const superAdminUser = {
      id: '1',
      organisationCategory: 'SUPER_ADMIN',
      orgId: 'org-1',
    };

    it('has proper ARIA labels for actions menu', () => {
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-1');
      expect(menuButton).toHaveAttribute('aria-label', 'Actions menu');
    });

    it('has proper roles for menu items', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const columns = useColumns({
          ...mockCallbacks,
          tenantsList: mockTenants,
          user: superAdminUser,
        });

        return <TestDataGrid columns={columns} rows={mockDonationHeads} />;
      };

      renderWithProviders(<TestComponent />, { user: superAdminUser });

      const menuButton = screen.getByTestId('actions-menu-1');
      await user.click(menuButton);

      const menu = screen.getByTestId('actions-menu-items-1');
      expect(menu).toHaveAttribute('role', 'menu');

      const editButton = screen.getByTestId('edit-action-1');
      expect(editButton).toHaveAttribute('role', 'menuitem');
    });
  });
});
