import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { renderWithProviders, mockStatsData, mockApiResponse } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the complete donation tenant dashboard
const DonationTenantDashboard = () => {
  const [stats, setStats] = React.useState(null);
  const [loadingStats, setLoadingStats] = React.useState(true);
  const [donationTrends, setDonationTrends] = React.useState(null);
  const [distributionData, setDistributionData] = React.useState(null);
  const [donationHeads, setDonationHeads] = React.useState([]);
  const [recentActivities, setRecentActivities] = React.useState([]);
  const [selectedTab, setSelectedTab] = React.useState(0);
  const [error, setError] = React.useState(null);

  const tabLabels = ['Yearly', 'Monthly', 'Weekly', 'Daily'];
  const tabKeys = ['yearly', 'monthly', 'weekly', 'daily'];

  // Mock data
  const mockTenantStats = {
    totalDonations: 125000,
    uniqueDonors: 45,
    last30DaysDonations: 8,
    averageDonation: 2777,
    donationHeadsCount: 5,
    topHead: 'Education',
  };

  const mockTrendsData = {
    yearly: { series: [{ name: 'Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }], categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] },
    monthly: { series: [{ name: 'Donations', data: [200, 150, 300, 250, 350, 100] }], categories: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'] },
    weekly: { series: [{ name: 'Donations', data: [50, 30, 80, 60, 90, 70, 100] }], categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] },
    daily: { series: [{ name: 'Donations', data: [10, 15, 5, 20, 12, 25, 8] }], categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'] },
  };

  const mockDistribution = {
    series: [3500, 2800, 2200, 1800, 1200],
    labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
  };

  const mockHeadsList = [
    { id: '1', name: 'Education Fund', totalAmount: 3500, percentage: 30.4, isActive: true },
    { id: '2', name: 'Healthcare Fund', totalAmount: 2800, percentage: 24.3, isActive: true },
    { id: '3', name: 'Food Aid Fund', totalAmount: 2200, percentage: 19.1, isActive: true },
    { id: '4', name: 'Shelter Fund', totalAmount: 1800, percentage: 15.7, isActive: false },
    { id: '5', name: 'Environment Fund', totalAmount: 1200, percentage: 10.4, isActive: true },
  ];

  const mockActivitiesList = [
    { id: '1', type: 'donation', description: 'New donation received', amount: 5000, timestamp: '2024-01-01T10:00:00Z', donor: 'John Doe' },
    { id: '2', type: 'head_update', description: 'Donation head updated', headName: 'Healthcare Fund', timestamp: '2024-01-01T08:00:00Z' },
    { id: '3', type: 'report', description: 'Monthly report generated', timestamp: '2023-12-31T23:59:00Z' },
  ];

  // API calls
  const fetchTenantStats = async () => {
    try {
      const response = await axios.get('/api/tenant/dashboard/stats');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch tenant stats');
    }
  };

  const fetchDonationTrends = async (period = 'yearly') => {
    try {
      const response = await axios.get(`/api/tenant/dashboard/trends?period=${period}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch donation trends');
    }
  };

  const fetchDistributionData = async () => {
    try {
      const response = await axios.get('/api/tenant/dashboard/distribution');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch distribution data');
    }
  };

  const fetchDonationHeads = async () => {
    try {
      const response = await axios.get('/api/tenant/donation-heads');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch donation heads');
    }
  };

  const fetchRecentActivities = async () => {
    try {
      const response = await axios.get('/api/tenant/dashboard/activities');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch recent activities');
    }
  };

  // Load all data
  React.useEffect(() => {
    const loadAllData = async () => {
      setLoadingStats(true);
      setError(null);
      
      try {
        const [statsData, trendsData, distributionData, headsData, activitiesData] = await Promise.all([
          fetchTenantStats(),
          fetchDonationTrends('yearly'),
          fetchDistributionData(),
          fetchDonationHeads(),
          fetchRecentActivities(),
        ]);

        setStats(statsData);
        setDonationTrends(trendsData);
        setDistributionData(distributionData);
        setDonationHeads(headsData);
        setRecentActivities(activitiesData);
      } catch (error) {
        console.error('Error loading tenant dashboard data:', error);
        setError(error.message);
        // Set mock data as fallback
        setStats(mockTenantStats);
        setDonationTrends(mockTrendsData);
        setDistributionData(mockDistribution);
        setDonationHeads(mockHeadsList);
        setRecentActivities(mockActivitiesList);
      } finally {
        setLoadingStats(false);
      }
    };

    loadAllData();
  }, []);

  // Load trends data when tab changes
  React.useEffect(() => {
    const loadTrendsData = async () => {
      try {
        const trendsData = await fetchDonationTrends(tabKeys[selectedTab]);
        setDonationTrends(trendsData);
      } catch (error) {
        console.error('Error loading trends data:', error);
        setDonationTrends(mockTrendsData);
      }
    };

    if (!loadingStats) {
      loadTrendsData();
    }
  }, [selectedTab, loadingStats]);

  const refreshData = async () => {
    setLoadingStats(true);
    try {
      const statsData = await fetchTenantStats();
      setStats(statsData);
      setError(null);
    } catch (error) {
      setError('Failed to refresh data');
    } finally {
      setLoadingStats(false);
    }
  };

  const exportData = async (format = 'csv') => {
    try {
      const response = await axios.get(`/api/tenant/dashboard/export?format=${format}`, {
        responseType: 'blob',
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `tenant-dashboard.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const currencySymbol = '₹';

  if (error && !stats) {
    return (
      <div data-testid="error-state">
        <h2>Error Loading Dashboard</h2>
        <p data-testid="error-message">{error}</p>
        <button data-testid="retry-button" onClick={refreshData}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div data-testid="donation-tenant-dashboard">
      <div data-testid="dashboard-header">
        <h1>Tenant Dashboard</h1>
        <div data-testid="header-actions">
          <button data-testid="refresh-button" onClick={refreshData} disabled={loadingStats}>
            {loadingStats ? 'Refreshing...' : 'Refresh'}
          </button>
          <button data-testid="export-csv-button" onClick={() => exportData('csv')}>
            Export CSV
          </button>
          <button data-testid="export-excel-button" onClick={() => exportData('xlsx')}>
            Export Excel
          </button>
        </div>
      </div>

      {error && (
        <div data-testid="error-banner" role="alert">
          <p>Warning: {error}</p>
        </div>
      )}

      {/* Quick Stats */}
      <div data-testid="quick-stats">
        <h2>Overview</h2>
        <div data-testid="stats-grid">
          <div data-testid="stat-total-donations">
            <h3>Total Donations</h3>
            {loadingStats ? (
              <div data-testid="loading-total">Loading...</div>
            ) : (
              <div data-testid="total-value">
                {currencySymbol}{stats?.totalDonations?.toLocaleString() || '0'}
              </div>
            )}
          </div>

          <div data-testid="stat-unique-donors">
            <h3>Unique Donors</h3>
            {loadingStats ? (
              <div data-testid="loading-donors">Loading...</div>
            ) : (
              <div data-testid="donors-value">{stats?.uniqueDonors || '0'}</div>
            )}
          </div>

          <div data-testid="stat-donation-heads">
            <h3>Active Heads</h3>
            {loadingStats ? (
              <div data-testid="loading-heads">Loading...</div>
            ) : (
              <div data-testid="heads-value">{stats?.donationHeadsCount || '0'}</div>
            )}
          </div>

          <div data-testid="stat-top-head">
            <h3>Top Performing</h3>
            {loadingStats ? (
              <div data-testid="loading-top">Loading...</div>
            ) : (
              <div data-testid="top-value">{stats?.topHead || 'N/A'}</div>
            )}
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div data-testid="charts-section">
        <h2>Analytics</h2>
        
        {/* Donation Trends */}
        <div data-testid="trends-analytics">
          <h3>Donation Trends</h3>
          <div data-testid="trends-tabs">
            {tabLabels.map((label, index) => (
              <button
                key={index}
                data-testid={`trends-tab-${label.toLowerCase()}`}
                onClick={() => setSelectedTab(index)}
                className={selectedTab === index ? 'active' : ''}
                aria-pressed={selectedTab === index}
              >
                {label}
              </button>
            ))}
          </div>
          <div data-testid="trends-chart">
            {donationTrends ? (
              <div data-testid="mock-chart" data-type="area" data-period={tabKeys[selectedTab]}>
                Donation Trends Chart - {tabLabels[selectedTab]}
              </div>
            ) : (
              <div data-testid="chart-loading">Loading chart...</div>
            )}
          </div>
        </div>

        {/* Distribution Chart */}
        <div data-testid="distribution-analytics">
          <h3>Donation Distribution</h3>
          <div data-testid="distribution-chart">
            {distributionData ? (
              <div data-testid="mock-chart" data-type="donut">
                Distribution Donut Chart
              </div>
            ) : (
              <div data-testid="chart-loading">Loading chart...</div>
            )}
          </div>
        </div>
      </div>

      {/* Donation Heads Performance */}
      <div data-testid="donation-heads-section">
        <h2>Donation Heads Performance</h2>
        <div data-testid="donation-heads-list">
          {donationHeads.map((head) => (
            <div key={head.id} data-testid={`donation-head-${head.id}`}>
              <div data-testid={`head-name-${head.id}`}>{head.name}</div>
              <div data-testid={`head-amount-${head.id}`}>
                {currencySymbol}{head.totalAmount?.toLocaleString()}
              </div>
              <div data-testid={`head-percentage-${head.id}`}>
                {head.percentage}%
              </div>
              <div data-testid={`head-status-${head.id}`}>
                {head.isActive ? 'Active' : 'Inactive'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activities */}
      <div data-testid="recent-activities-section">
        <h2>Recent Activities</h2>
        <div data-testid="activities-list">
          {recentActivities.map((activity) => (
            <div key={activity.id} data-testid={`activity-${activity.id}`}>
              <div data-testid={`activity-description-${activity.id}`}>
                {activity.description}
                {activity.amount && ` - ${currencySymbol}${activity.amount.toLocaleString()}`}
                {activity.headName && ` - ${activity.headName}`}
              </div>
              <div data-testid={`activity-timestamp-${activity.id}`}>
                {new Date(activity.timestamp).toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

describe('Donation Tenant Dashboard Integration', () => {
  const mockTenantStatsResponse = {
    totalDonations: 125000,
    uniqueDonors: 45,
    last30DaysDonations: 8,
    averageDonation: 2777,
    donationHeadsCount: 5,
    topHead: 'Education',
  };

  const mockUser = {
    id: '1',
    name: 'Tenant User',
    organisationCategory: 'TENANT',
    orgId: 'tenant-org-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockAxios.get.mockImplementation((url) => {
      switch (true) {
        case url.includes('/api/tenant/dashboard/stats'):
          return Promise.resolve(mockApiResponse(mockTenantStatsResponse));
        case url.includes('/api/tenant/dashboard/trends'):
          return Promise.resolve(mockApiResponse({}));
        case url.includes('/api/tenant/dashboard/distribution'):
          return Promise.resolve(mockApiResponse({}));
        case url.includes('/api/tenant/donation-heads'):
          return Promise.resolve(mockApiResponse([]));
        case url.includes('/api/tenant/dashboard/activities'):
          return Promise.resolve(mockApiResponse([]));
        case url.includes('/api/tenant/dashboard/export'):
          return Promise.resolve({ data: new Blob(['test']) });
        default:
          return Promise.reject(new Error('Unknown endpoint'));
      }
    });
  });

  describe('Initial Load and Data Fetching', () => {
    it('loads all tenant dashboard data on mount', async () => {
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      expect(screen.getByTestId('loading-total')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('total-value')).toBeInTheDocument();
      });

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/stats');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/trends?period=yearly');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/distribution');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/donation-heads');
      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/activities');
    });

    it('displays loaded tenant stats correctly', async () => {
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByText('₹125,000')).toBeInTheDocument();
        expect(screen.getByText('45')).toBeInTheDocument();
        expect(screen.getByText('5')).toBeInTheDocument();
        expect(screen.getByText('Education')).toBeInTheDocument();
      });
    });

    it('loads charts and performance data after initial load', async () => {
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('trends-chart')).toBeInTheDocument();
        expect(screen.getByTestId('distribution-chart')).toBeInTheDocument();
        expect(screen.getByTestId('donation-heads-list')).toBeInTheDocument();
        expect(screen.getByTestId('activities-list')).toBeInTheDocument();
      });
    });
  });

  describe('Interactive Chart Navigation', () => {
    it('switches between donation trend periods', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('trends-analytics')).toBeInTheDocument();
      });

      // Initially shows yearly
      expect(screen.getByText('Donation Trends Chart - Yearly')).toBeInTheDocument();

      // Switch to monthly
      await user.click(screen.getByTestId('trends-tab-monthly'));
      
      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/trends?period=monthly');
      });

      expect(screen.getByText('Donation Trends Chart - Monthly')).toBeInTheDocument();
    });

    it('maintains chart state during tab switches', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('trends-analytics')).toBeInTheDocument();
      });

      // Switch to weekly
      await user.click(screen.getByTestId('trends-tab-weekly'));
      expect(screen.getByText('Donation Trends Chart - Weekly')).toBeInTheDocument();

      // Switch to daily
      await user.click(screen.getByTestId('trends-tab-daily'));
      expect(screen.getByText('Donation Trends Chart - Daily')).toBeInTheDocument();
    });

    it('loads new data when switching chart periods', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('trends-analytics')).toBeInTheDocument();
      });

      // Clear previous calls
      mockAxios.get.mockClear();

      // Switch to weekly
      await user.click(screen.getByTestId('trends-tab-weekly'));

      await waitFor(() => {
        expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/trends?period=weekly');
      });
    });
  });

  describe('Data Refresh and Export Functionality', () => {
    it('refreshes tenant stats when refresh button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      // Clear previous calls
      mockAxios.get.mockClear();

      await user.click(screen.getByTestId('refresh-button'));

      expect(screen.getByText('Refreshing...')).toBeInTheDocument();
      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/stats');
    });

    it('exports data as CSV when export CSV button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('export-csv-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('export-csv-button'));

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/export?format=csv', {
        responseType: 'blob',
      });
    });

    it('exports data as Excel when export Excel button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('export-excel-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('export-excel-button'));

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/export?format=xlsx', {
        responseType: 'blob',
      });
    });

    it('disables refresh button during refresh', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('refresh-button'));

      const refreshButton = screen.getByTestId('refresh-button');
      expect(refreshButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('displays error state when all APIs fail', async () => {
      mockAxios.get.mockRejectedValue(new Error('Network error'));

      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('error-state')).toBeInTheDocument();
      });

      expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('error-message')).toHaveTextContent('Failed to fetch tenant stats');
    });

    it('shows fallback data when APIs fail', async () => {
      mockAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        // Should show fallback data instead of error state
        expect(screen.getByTestId('donation-tenant-dashboard')).toBeInTheDocument();
      });
    });

    it('allows retry when in error state', async () => {
      const user = userEvent.setup();
      mockAxios.get.mockRejectedValueOnce(new Error('Network error'))
                  .mockResolvedValue(mockApiResponse(mockTenantStatsResponse));

      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('error-state')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('retry-button'));

      await waitFor(() => {
        expect(screen.getByTestId('donation-tenant-dashboard')).toBeInTheDocument();
      });
    });

    it('shows error banner for partial failures', async () => {
      mockAxios.get.mockImplementation((url) => {
        if (url.includes('/api/tenant/dashboard/stats')) {
          return Promise.resolve(mockApiResponse(mockTenantStatsResponse));
        }
        return Promise.reject(new Error('Chart data failed'));
      });

      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('error-banner')).toBeInTheDocument();
      });

      expect(screen.getByText(/Warning:/)).toBeInTheDocument();
    });

    it('handles export errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxios.get.mockImplementation((url) => {
        if (url.includes('export')) {
          return Promise.reject(new Error('Export failed'));
        }
        return Promise.resolve(mockApiResponse({}));
      });

      const user = userEvent.setup();
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('export-csv-button')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('export-csv-button'));

      await waitFor(() => {
        expect(consoleError).toHaveBeenCalledWith('Export failed:', expect.any(Error));
      });

      consoleError.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes for tab navigation', async () => {
      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        expect(screen.getByTestId('trends-tab-yearly')).toBeInTheDocument();
      });

      const yearlyTab = screen.getByTestId('trends-tab-yearly');
      expect(yearlyTab).toHaveAttribute('aria-pressed', 'true');

      const monthlyTab = screen.getByTestId('trends-tab-monthly');
      expect(monthlyTab).toHaveAttribute('aria-pressed', 'false');
    });

    it('has proper alert role for error messages', async () => {
      mockAxios.get.mockImplementation((url) => {
        if (url.includes('/api/tenant/dashboard/stats')) {
          return Promise.resolve(mockApiResponse(mockTenantStatsResponse));
        }
        return Promise.reject(new Error('Chart data failed'));
      });

      renderWithProviders(<DonationTenantDashboard />, { user: mockUser });

      await waitFor(() => {
        const errorBanner = screen.getByTestId('error-banner');
        expect(errorBanner).toHaveAttribute('role', 'alert');
      });
    });
  });
});
