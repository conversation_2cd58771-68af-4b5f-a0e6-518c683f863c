// ** React Imports
import { forwardRef, useEffect, useState, useContext } from "react";

// ** MUI Imports
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import CardContent from "@mui/material/CardContent";
import FormControl from "@mui/material/FormControl";
import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";
import authConfig from "src/configs/auth";
// ** Third Party Imports
import { useForm, Controller } from "react-hook-form";
import {
  FormHelperText,
  InputLabel,
  Typography,
  Select,
  Button,
} from "@mui/material";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import MenuItem from "@mui/material/MenuItem";

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section7 = ({
  onCancel,
  formData,
  employeesData,
  createdByName,
  assignedTo,
  handleAssignedToChange,
}) => {
  //Hooks
  const auth = useAuth();

  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
    setValue,
  } = useForm({});
  const [sourceGroup, setSourceGroup] = useState(formData?.sourceGroup);
  const [leadStatusData, setLeadStatusData] = useState(null);
  const[leadPriorityData,setLeadPriorityData]=useState(null);
  const [groupData, setGroupData] = useState(null);
  const [subGroupData, setSubGroupData] = useState(null);

  const [leadStatus, setLeadStatus] = useState(formData?.leadStatus);
  const [leadPriority, setLeadPriority] = useState(formData?.leadPriority);
  const [subSourceGroup, setSubSourceGroup] = useState(
    formData?.subSourceGroup
  );
  const[isSubmitting, setIsSubmitting] = useState(false);
  async function submit(data) {
    setIsSubmitting(true);
    console.log("Submitted data in society", data);
    console.log("assined TO", assignedTo);
    try {
      const trimmedData = Object.fromEntries(
        Object.entries(data).map(([key, value]) => [
          key,
          typeof value === "string" ? value.trim() : value,
        ])
      );
      trimmedData.assignedTo = assignedTo;
      trimmedData.sourceGroup = sourceGroup;
      trimmedData.subSourceGroup = subSourceGroup;
      trimmedData.leadStatus = leadStatus;
      trimmedData.leadPriority=leadPriority;
    
      const userUniqueId =
        formData && formData.userId !== undefined
          ? formData.userId
          : auth.user?.id;

      const response = await auth.updateEntity(trimmedData, userUniqueId);

      onCancel();
    } catch (error) {
      console.error("Error updating entity:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };
  const handleLeadStatusChange = (event) => {
    const value = event.target.value;
    setLeadStatus(value);
  };
  useEffect(() => {

    if(!!authConfig) {
    getAllListValuesByListNameId(
      authConfig.leadStatusListNamesId,
      (data) =>
        setLeadStatusData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );

    getAllListValuesByListNameId(
      authConfig.leadPriorityListNamesId,
      (data) =>
        setLeadPriorityData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );

    getAllListValuesByListNameId(
      authConfig.groupDataListNameId,
      (data) =>
        setGroupData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }
  }, [authConfig]);

  useEffect(() => {
    if (authConfig && sourceGroup) {
      getAllListValuesByListNameId(sourceGroup, (data) =>
        setSubGroupData(
          data?.listValues.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        )
      );
    }
    }, [authConfig,sourceGroup]);

  const handleLeadStatusSuccess = (data) => {
    setLeadStatusData(data?.listValues);
  };

  const handleLeadPrioritySuccess = (data) => {
    setLeadPriorityData(data?.listValues);
  };

  const handleLeadPriorityChange = (event) => {
    const selectedId = event.target.value;
    setLeadPriority(selectedId);
  };


  const handleGroupDataSuccess = (data) => {
    setGroupData(data?.listValues);
  };
  const handleSubGroupDataSuccess = (data) => {
    setSubGroupData(data?.listValues);
  };

 

  const handleSourceGroupChange = (event) => {
    const selectedId = event.target.value;
    setSourceGroup(selectedId);
  };

  const handleSubSourceGroupChange = (event) => {
    const selectedId = event.target.value;
    setSubSourceGroup(selectedId);
  };

  const [createdOn, setCreatedOn] = useState(formData?.createdOn);

  return (
    <Card>
      <CardContent>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            {formData && formData.userId !== undefined ? (
               <FormControl fullWidth>
               <Controller
                 name="assignedTo"
                 control={control}
                 defaultValue={formData?.assignedTo}
                 render={({ field }) => (
                   <SelectAutoComplete
                     {...field}
                     id="assigned-to"
                     label="Assigned To"
                     nameArray={employeesData?.map((data) => ({
                       value: data.id,
                       key: data.name,
                     }))}
                     value={assignedTo}
                     onChange={(e) => {
                       field.onChange(e);
                       handleAssignedToChange(e);
                     }}
                   />
                 )}
               />
             </FormControl>
           ) : (
             <FormControl fullWidth>
               <Typography>Assigned To</Typography>
             </FormControl>
            )}
          </Grid>
          {formData && formData.userId !== undefined && (
            <>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                <Controller
                  name="status"
                  control={control}
                  size="small"
                  defaultValue={formData?.leadStatus}
                  InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="status"
                      label="Lead Status"
                      nameArray={leadStatusData}
                      value={leadStatus}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadStatusChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <Controller
                  name="lead-priority"
                  control={control}
                  size="small"
                  defaultValue={formData?.leadPriority}
                  // InputLabelProps={{ shrink: true }}
                  render={({ field }) => (
                    <SelectAutoComplete
                      id="lead-priority"
                      label="Lead Priority"
                      nameArray={leadPriorityData}
                      value={leadPriority}
                      onChange={(e) => {
                        field.onChange(e);
                        handleLeadPriorityChange(e);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

              <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
              <Controller
                name="sourceGroup"
                control={control}
                defaultValue={formData?.sourceGroup}
                render={({ field }) => (
                  <SelectAutoComplete
                    {...field}
                    id="source-group"
                    label="Source Group"
                    nameArray={groupData}
                    value={sourceGroup}
                    onChange={(e) => {
                      field.onChange(e);
                      handleSourceGroupChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
              </Grid>

              <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
              <Controller
                name="subSourceGroup"
                control={control}
                defaultValue={formData?.subSourceGroup || ""}
                render={({ field }) => (
                  <SelectAutoComplete
                    {...field}
                    id="sub-source-group"
                    label="Sub Source Group"
                    nameArray={subGroupData}
                    value={subSourceGroup}
                    onChange={(e) => {
                      field.onChange(e);
                      handleSubSourceGroupChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                
               
                </Grid>
             
              <Grid item xs={12} sm={4} sx={{ml:2}}>
                
                <FormControl fullWidth style={{ marginTop: "16px" }}>
                <Typography style={{ marginBottom: "8px" }}>
             <strong>Created On:</strong>  {createdOn
                      ? new Date(createdOn).toLocaleDateString("en-GB")
                      : ""}
                </Typography>
              </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
              <FormControl fullWidth style={{ marginTop: "16px" }}>
                <Typography style={{ marginBottom: "8px" }}>
             <strong>Created By:</strong> {createdByName}
                </Typography>
              </FormControl>
              </Grid>

              <Grid item  xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="remarks"
                    control={control}
                    defaultValue={formData?.remarks}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        rows={4}
                        multiline
                        label="Remarks"
                        InputLabelProps={{ shrink: true }}
                        error={Boolean(errors.remarks)}
                        helperText={errors.remarks?.message}
                        aria-describedby="statusAssignmentDetails_remarks"
                        inputProps={{
                          title:'Enter any remarks'
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </>
          )}
          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSubmitting}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default Section7;
