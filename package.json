{"name": "vuexy-react-admin-template", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:beta": "dotenv -e .env.development next build", "build:prod": "dotenv -e .env.production next build", "start": "next start", "start:dev": "dotenv -e .env.development next start", "start:prod": "dotenv -e .env.production next start", "dev:beta": "dotenv -e .env.development next dev", "dev:prod": "dotenv -e .env.production next dev", "export:beta": "dotenv -e .env.development next build && next export", "export:prod": "dotenv -e .env.production next build && next export", "export": "next build && next export", "lint": "eslint --fix \"src/**/*.{js,jsx}\"", "format": "prettier --write \"src/**/*.{js,jsx}\"", "build:icons": "node src/iconify-bundle/bundle-icons-react.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:all": "node src/tests/runAllTests.js", "test:unit": "jest --testPathPattern=src/tests/unit", "test:integration": "jest --testPathPattern=src/tests/integration", "test:api": "jest --testPathPattern=src/tests/api", "test:donation-head": "jest --testPath<PERSON>attern=donation-head", "test:donation-admin": "jest --testPathPattern=donation-admin", "test:donation-tenant": "jest --testPathPattern=donation-tenant", "test:coverage:open": "jest --coverage && open coverage/lcov-report/index.html", "pre-commit": "npm run lint && npm run test:all"}, "dependencies": {"@casl/ability": "6.3.3", "@casl/react": "3.1.0", "@emotion/cache": "11.10.5", "@emotion/react": "11.10.5", "@emotion/server": "11.10.0", "@emotion/styled": "11.10.5", "@fullcalendar/bootstrap5": "^6.1.12", "@fullcalendar/core": "^6.1.12", "@fullcalendar/daygrid": "^6.1.12", "@fullcalendar/interaction": "^6.1.12", "@fullcalendar/list": "^6.1.12", "@fullcalendar/react": "^6.1.12", "@fullcalendar/timegrid": "^6.1.12", "@hookform/resolvers": "^2.9.10", "@iconify/react": "4.0.1", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@mui/icons-material": "^5.14.1", "@mui/lab": "5.0.0-alpha.115", "@mui/material": "^5.11.4", "@mui/x-data-grid": "5.17.18", "@popperjs/core": "2.11.6", "@react-google-maps/api": "^2.19.3", "@reduxjs/toolkit": "^2.2.5", "@tabler/icons": "^3.2.0", "apexcharts": "^3.52.0", "axios": "1.2.2", "axios-mock-adapter": "^1.22.0", "bootstrap-icons": "^1.11.3", "cleave.js": "1.6.0", "clipboard-copy": "4.0.1", "clsx": "1.2.1", "date-fns": "^2.29.3", "dayjs": "^1.11.12", "docx-preview": "^0.3.2", "docx2html": "^1.3.2", "draft-js": "0.11.7", "file-saver": "^2.0.5", "i18next": "22.4.9", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "jsonwebtoken": "8.5.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.3", "keen-slider": "6.8.5", "mammoth": "^1.8.0", "next": "13.1.1", "nprogress": "0.2.0", "payment": "2.4.6", "pdfjs-dist": "^4.5.136", "prismjs": "1.29.0", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^4.6.1", "react-credit-cards": "0.8.3", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-draft-wysiwyg": "1.15.0", "react-dropzone": "14.2.3", "react-hook-form": "^7.41.5", "react-hot-toast": "^2.4.0", "react-i18next": "12.1.4", "react-icons": "^5.4.0", "react-pdf": "^9.1.0", "react-perfect-scrollbar": "1.5.8", "react-player": "^2.12.0", "react-popper": "2.3.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-slick": "^0.29.0", "react-time-picker": "^7.0.0", "slick-carousel": "^1.8.1", "stylis": "4.1.3", "stylis-plugin-rtl": "2.1.1", "vuexy-react-admin-template": "file:", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@iconify/json": "2.2.4", "@iconify/tools": "2.2.0", "@iconify/utils": "2.0.11", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "babel-jest": "^29.7.0", "dotenv-cli": "^7.3.0", "eslint": "8.31.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "2.8.2"}, "resolutions": {"minipass": "4.0.0", "@mui/x-data-grid/@mui/system": "5.4.1", "react-credit-cards/prop-types": "15.7.2", "react-hot-toast/goober/csstype": "3.0.10", "recharts/react-smooth/prop-types": "15.6.0", "react-draft-wysiwyg/html-to-draftjs/immutable": "4.2.2", "react-draft-wysiwyg/draftjs-utils/immutable": "4.2.2", "@emotion/react/@emotion/babel-plugin/@babel/core": "7.0.0", "@emotion/react/@emotion/babel-plugin/@babel/plugin-syntax-jsx/@babel/core": "7.0.0"}, "overrides": {"react-credit-cards": {"react": "$react"}}, "description": "\"# Houzer Applicaiton\"\r Only Accessible for the logged in user.", "main": "ecosystem.config.js", "author": "", "license": "ISC"}