import React, { useState, useEffect } from 'react'
import Box from '@mui/material/Box'
import Autocomplete from '@mui/material/Autocomplete'
import TextField from '@mui/material/TextField'

const MultiSelectAutoComplete = props => {
  const { id, label, nameArray = [], value = [], onChange } = props

  const [selectedValues, setSelectedValues] = useState(value)

  useEffect(() => {
    setSelectedValues(value || [])
  }, [value])

  const handleChange = (event, newValue) => {
    setSelectedValues(newValue)
    onChange({ target: { value: newValue } })
  }

  return (
    <Box sx={{ position: 'relative' }}>
      <Autocomplete
        multiple
        id={id}
        options={nameArray}
        // Hide items that are already selected
        filterSelectedOptions
        // Tell MUI how to check if an item is selected
        isOptionEqualToValue={(option, val) => option?.value === val?.value}
        getOptionLabel={option => option?.key || ''}
        value={selectedValues}
        onChange={handleChange}
        renderInput={params => (
          <TextField
            {...params}
            label={label}
            size='small'
            InputLabelProps={{
              ...params.InputLabelProps,
              shrink: true
            }}
          />
        )}
      />
    </Box>
  )
}

export default MultiSelectAutoComplete
