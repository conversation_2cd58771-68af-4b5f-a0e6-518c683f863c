import React, { useState } from "react";
import Box from "@mui/material/Box";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import TextField from "@mui/material/TextField";
import { styled, alpha } from "@mui/material/styles";
import MenuItem from "@mui/material/MenuItem";
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";
import Grid from "@mui/material/Grid";
import FormControl from "@mui/material/FormControl";
import { Controller, useForm } from "react-hook-form";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { DialogContentText } from "@mui/material";
import CreateUser from "src/pages/CHS/CreateUser";

// Styled component for custom select item
const CustomSelectItem = styled(MenuItem)(({ theme }) => ({
  color: theme.palette.success.main,
  backgroundColor: "transparent !important",
  "&:hover": {
    color: `${theme.palette.success.main} !important`,
    backgroundColor: `${alpha(theme.palette.success.main, 0.1)} !important`,
  },
  "&.Mui-focusVisible": {
    backgroundColor: `${alpha(theme.palette.success.main, 0.2)} !important`,
  },
  "&.Mui-selected": {
    color: `${theme.palette.success.contrastText} !important`,
    backgroundColor: `${theme.palette.success.main} !important`,
    "&.Mui-focusVisible": {
      backgroundColor: `${theme.palette.success.dark} !important`,
    },
  },
}));

const SelectAutoCompleteSociety = (props) => {
  const { control,reset } = useForm();
  const { id, label, nameArray, value, onChange,setAddSection,addSection } = props;

  const [selectedValue, setSelectedValue] = useState(value || null);
  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const [isDialogOpen, setDialogOpen] = useState(false);

  // Handle option selection
  const handleOptionSelect = (event, option) => {
    if (option?.value === "add-new") {
      setDialogOpen(true); // Open dialog for "Add New"
      setAddSection(true)
    } else {
      const selectedValue = option ? option.value : null;
      setSelectedValue(selectedValue);
      if (onChange) {
        onChange({ target: { value: selectedValue } });
      }
    }
    setDropdownOpen(false);
  };

  const auth = useAuth();



  const handleButtonClick = () => {
    setDialogOpen(false);
  };
  

  return (
    <Box sx={{ position: "relative", zIndex: 1 }}>
      <CustomAutocomplete
        autoHighlight
        id={id}
        options={[{ key: "Add New Society", value: "add-new" }, ...nameArray]} // Move "Add New" to the top
        getOptionLabel={(option) => option.key || ""}
        value={
          nameArray.find((option) => option.value === selectedValue) || null
        }
        onChange={handleOptionSelect}
        open={isDropdownOpen}
        onOpen={() => setDropdownOpen(true)}
        onClose={() => setDropdownOpen(false)}
        renderOption={(props, option) => (
          <Box
            component="li"
            {...props}
            onClick={(event) => handleOptionSelect(event, option)}
          >
            {option.value === "add-new" ? (
              <CustomSelectItem component="li" {...props}>
                  <Icon icon="tabler:circle-plus" />
                  Add New Society
              </CustomSelectItem>
            ) : (
              option.key
            )}
          </Box>
        )}
        renderInput={(params) => (
          <CustomTextField
            {...params}
            size="small"
            label={label}
            placeholder={`${label}`}
            InputLabelProps={{
              ...params.InputLabelProps,
              shrink: true,
            }}
          />
        )}
      />
       <CreateUser
          openDialog={isDialogOpen}
          handleDialogClose={handleButtonClick}
          setAddSection={setAddSection}
          addSection={addSection}
        />
      
    </Box>
  );
};

export default SelectAutoCompleteSociety;