import React from 'react';
import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const CandleStickChart = () => {
  const state = {
    series: [
      {
        name: "Service Metrics",
        data: [
          { x: new Date('2024-01-01').getTime(), y: [700, 750, 690, 720] },
          { x: new Date('2024-02-01').getTime(), y: [720, 780, 700, 760] },
          { x: new Date('2024-03-01').getTime(), y: [760, 800, 740, 780] },
          { x: new Date('2024-04-01').getTime(), y: [780, 820, 760, 800] },
          { x: new Date('2024-05-01').getTime(), y: [800, 850, 780, 820] },
          { x: new Date('2024-06-01').getTime(), y: [820, 870, 800, 850] },
          { x: new Date('2024-07-01').getTime(), y: [850, 900, 820, 880] },
          { x: new Date('2024-08-01').getTime(), y: [880, 920, 840, 900] },
          { x: new Date('2024-09-01').getTime(), y: [900, 940, 860, 920] },
          { x: new Date('2024-10-01').getTime(), y: [920, 960, 880, 940] },
          { x: new Date('2024-11-01').getTime(), y: [940, 980, 900, 960] },
          { x: new Date('2024-12-01').getTime(), y: [960, 1000, 920, 980] }
        ],
      }
    ],
    options: {
      chart: {
        type: 'candlestick',
        height: 350
      },
      title: {
        text: 'Monthly Service Metrics',
        align: 'left'
      },
      xaxis: {
        type: 'datetime',
        labels: {
          format: 'MMM'
        }
      },
      yaxis: {
        tooltip: {
          enabled: true
        }
      },
      plotOptions: {
        candlestick: {
          colors: {
            upward: '#00E396', // Green for positive change (closing > opening)
            downward: '#FF4560' // Red for negative change (closing < opening)
          }
        }
      },
      annotations: {
        xaxis: [
          {
            x: new Date('2024-05-01').getTime(), // Example of a specific date for annotation
            borderColor: '#FF4560',
            label: {
              borderColor: '#FF4560',
              style: {
                color: '#FFF',
                background: '#FF4560'
              },
              text: 'Service Decline'
            }
          }
        ],
        yaxis: [
          {
            y: 800, // Example of a specific value for annotation
            borderColor: '#00E396',
            label: {
              borderColor: '#00E396',
              style: {
                color: '#FFF',
                background: '#00E396'
              },
              text: 'Service Peak'
            }
          }
        ]
      }
    }
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="candlestick" height={350} />
      </div>
    </div>
  );
};

export default CandleStickChart;
