// ** React Imports

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { Box, Button, FormControl, TextField } from "@mui/material";

import { useAuth } from "src/hooks/useAuth";
import { yupResolver } from "@hookform/resolvers/yup";
import OtherServicesValidation, { yup } from "./OtherServicesValidation";
import React, { useState } from "react";

// ** Icon Imports

const OtherServicesEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(OtherServicesValidation(yup)),
    mode: "onChange",
  });
  const [fieldChanged, setFieldChanged] = useState(false);

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const response = await auth.updateEntity(trimmedData, () => {
      console.error(" Architect Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="otherServices"
                control={control}
                defaultValue={formData?.otherServices}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="Other Services"
                    InputLabelProps={{ shrink: true }}
                    helperText={errors.otherServices?.message}
                    error={Boolean(errors.otherServices)}
                    aria-describedby="Section2_otherServices"
                    onChange={(e) => {
                      field.onChange(e);
                      setFieldChanged(true);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={!fieldChanged}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default OtherServicesEdit;
