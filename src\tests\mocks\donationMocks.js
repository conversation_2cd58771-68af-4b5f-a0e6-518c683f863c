// Mock authentication hooks and contexts
export const mockUseAuth = () => ({
  postDonationHead: jest.fn(),
  patchDonationHead: jest.fn(),
  postDonationReceipt: jest.fn(),
  getDonationHeads: jest.fn(),
  getDonationReceipts: jest.fn(),
  getStats: jest.fn(),
});

// Mock AuthContext
export const mockAuthContext = {
  user: {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    organisationCategory: 'TENANT',
    orgId: 'org-1',
  },
  listValues: [],
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  donationHeadDetails: null,
  setDonationHeadDetails: jest.fn(),
};

// Mock RBAC Context
export const mockRBACContext = {
  canAccessActions: jest.fn(() => true),
  canAccessHeads: jest.fn(() => true),
  permissions: ['FULL_ACCESS'],
};

// Mock custom components
export const mockCustomComponents = () => {
  jest.mock('src/@core/components/spinner', () => {
    return function MockSpinner() {
      return <div data-testid="fallback-spinner">Loading...</div>;
    };
  });

  jest.mock('src/@core/components/icon', () => {
    return function MockIcon({ icon, ...props }) {
      return <span data-testid="mock-icon" data-icon={icon} {...props}>{icon}</span>;
    };
  });

  jest.mock('src/@core/components/mui/avatar', () => {
    return function MockAvatar({ children, ...props }) {
      return <div data-testid="custom-avatar" {...props}>{children}</div>;
    };
  });

  jest.mock('src/@core/components/mui/chip', () => {
    return function MockChip({ label, ...props }) {
      return <span data-testid="custom-chip" {...props}>{label}</span>;
    };
  });

  jest.mock('src/@core/components/custom-components/NameTextField', () => {
    return function MockNameTextField({ label, value, onChange, ...props }) {
      return (
        <input
          data-testid="name-text-field"
          placeholder={label}
          value={value}
          onChange={onChange}
          {...props}
        />
      );
    };
  });

  jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => {
    return function MockSelectAutoComplete({ label, value, onChange, nameArray, ...props }) {
      return (
        <select
          data-testid="select-auto-complete"
          value={value}
          onChange={onChange}
          {...props}
        >
          <option value="">{label}</option>
          {nameArray?.map((item) => (
            <option key={item.value} value={item.value}>
              {item.key}
            </option>
          ))}
        </select>
      );
    };
  });

  jest.mock('src/@core/components/custom-components/MobileNumberValidation', () => {
    return function MockMobileNumberValidation({ label, value, onChange, ...props }) {
      return (
        <input
          data-testid="mobile-number-validation"
          placeholder={label}
          value={value}
          onChange={onChange}
          {...props}
        />
      );
    };
  });

  jest.mock('react-perfect-scrollbar', () => {
    return function MockPerfectScrollbar({ children, ...props }) {
      return <div data-testid="perfect-scrollbar" {...props}>{children}</div>;
    };
  });
};

// Mock utility functions
export const mockUtils = () => {
  jest.mock('src/helpers/utils', () => ({
    getAuthorizationHeaders: jest.fn(() => ({
      'Authorization': 'Bearer mock-token',
      'Content-Type': 'application/json',
    })),
    getUrl: jest.fn((endpoint) => `http://localhost:8080${endpoint}`),
  }));

  jest.mock('src/configs/auth', () => ({
    baseURL: 'http://localhost:8080',
    donationHeadEndpoint: '/api/donation-heads',
    donationReceiptEndpoint: '/api/donation-receipts',
    dashboardStatisticsEndpoint: '/api/dashboard/stats',
    donationListNameId: 'donation-types',
    listValuesAll: '/api/list-values',
  }));

  jest.mock('src/constants', () => ({
    MENUS: {
      DONATION_HEAD: 'donation-head',
      DONATION_ADMIN: 'donation-admin',
      DONATION_TENANT: 'donation-tenant',
    },
    PAGES: {
      DONATION_HEAD: 'donation-head',
      DONATION_ADMIN: 'donation-admin',
      DONATION_TENANT: 'donation-tenant',
    },
    PERMISSIONS: {
      FULL_ACCESS: 'FULL_ACCESS',
      READ: 'READ',
      WRITE: 'WRITE',
    },
  }));
};

// Mock hooks
export const mockHooks = () => {
  jest.mock('src/hooks/useAuth', () => ({
    useAuth: () => mockUseAuth(),
  }));

  jest.mock('../permission/RBACContext', () => ({
    useRBAC: () => mockRBACContext,
  }));

  jest.mock('src/context/AuthContext', () => ({
    AuthContext: React.createContext(mockAuthContext),
  }));
};

// Mock axios
export const mockAxios = () => {
  const mockAxiosInstance = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  };

  jest.mock('axios', () => ({
    ...mockAxiosInstance,
    create: () => mockAxiosInstance,
  }));

  return mockAxiosInstance;
};

// Mock form libraries
export const mockFormLibraries = () => {
  jest.mock('react-hook-form', () => ({
    useForm: () => ({
      register: jest.fn(),
      handleSubmit: jest.fn((fn) => (e) => {
        e?.preventDefault?.();
        return fn({});
      }),
      setError: jest.fn(),
      setValue: jest.fn(),
      clearErrors: jest.fn(),
      control: {},
      reset: jest.fn(),
      formState: { errors: {} },
    }),
    Controller: ({ render, name }) => {
      const field = {
        onChange: jest.fn(),
        onBlur: jest.fn(),
        value: '',
        name,
      };
      return render({ field });
    },
  }));
};

// Setup all mocks
export const setupAllMocks = () => {
  mockCustomComponents();
  mockUtils();
  mockHooks();
  mockFormLibraries();
  return mockAxios();
};
