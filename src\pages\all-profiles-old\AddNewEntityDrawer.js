// ** React Imports
import { useState } from "react";

// ** MUI Imports
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import { Grid, FormControl, TextField, InputAdornment } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { useTheme } from "@mui/material/styles";
import SearchIcon from "@mui/icons-material/Search";
import PerfectScrollbar from "react-perfect-scrollbar";

// ** Icon Imports
import Icon from "src/@core/components/icon";
import SelectProject from "src/@core/components/custom-components/SelectProject";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const names = [
  {
    value: "LESS_THAN_5_YEARS",
    key: "Less than 5 years",
  },
  {
    value: "_5_TO_10_YEARS",
    key: "5-10 years",
  },
  {
    value: "MORE_THAN_10_YEARS",
    key: "More than 10 years",
  },
];

const entityCategoryOptions = [
  {
    value: "ARCHITECT",
    key: "Architect",
  },
  {
    value: "BROKER",
    key: "Broker",
  },
  {
    value: "CHARTERED_ACCOUNTANT",
    key: "Chartered Accountant",
  },
  {
    value: "SOCIETY",
    key: "Society",
  },
  {
    value: "PMC",
    key: "PMC",
  },
  {
    value: "STRUCTURAL_ENGINEER",
    key: "Structural Engineer",
  },
];

const area = [
  {
    value: "ISLAND",
    key: "Island",
  },
  {
    value: "WESTERN_SUBURB",
    key: "Western Subrub",
  },
  {
    value: "CENTRAL_SUBURB",
    key: "Central Subrub",
  },
  {
    value: "THANE",
    key: "Thane",
  },
  {
    value: "ALL",
    key: "All",
  },
  {
    value: "OTHER",
    key: "Other",
  },
];

const AddNewEntityDrawer = (props) => {
  // ** Props
  const { open, toggle, searchKeyword, setSearchKeyword,fetchUsers,page,pageSize } = props;

  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [keyword, setKeyword] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [remarks, setRemarks] = useState("");
  const [pincode, setPincode] = useState("");
  const [selectedField, setSelectedField] = useState("");
  const [yearsOfExperience, setYearsOfExperience] = useState("");
  const [areaofOperation, setAreaofOperation] = useState("");

  const handleFieldChange = (event) => {
    setSelectedField(event.target.value);
  };

  const handleClose = () => {
    toggle();
  };

  const handleSuccess = () => {
    setSearchKeyword(keyword);
    toggle();
  };

  const {
    register,
    control,
    formState: { errors },
  } = useForm();

  const ArchitectSearchForm = () => {
    return (
      <>
        <Box sx={{ pt: 3 }}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="systemCode"
                  control={control}
                  //defaultValue={formData?.systemCode}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="First systemCode"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="indicativeFeesPerSqft"
                  control={control}
                  //defaultValue={formData?.indicativeFeesPerSqft}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="Indicative fees per sqft"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };
  const BrokerSearchForm = () => {
    return (
      <>
        <Box sx={{ pt: 3 }}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="systemCode"
                  control={control}
                  //defaultValue={formData?.systemCode}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="System Code"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="reraNumber"
                  control={control}
                  //defaultValue={formData?.reraNumber}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value ? value.toUpperCase() : ""}
                      label="Rera Number"
                      onChange={onChange}
                      placeholder="Enter RERA number"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };

  const CASearchForm = () => {
    return (
      <>
        <Box sx={{ pt: 3 }}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="systemCode"
                  control={control}
                  //defaultValue={formData?.systemCode}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="System Code"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <SelectProject
                register={register}
                id={"yearsOfExperience"}
                label={"Years Of Experience"}
                nameArray={names}
                //defaultValue={formData?.yearsOfExperience}
                value={yearsOfExperience}
                onChange={(e) => setYearsOfExperience(e.target.value)}
                error={Boolean(errors.yearsOfExperience)}
                aria-describedby="validation-yearsOfExperience"
              />
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };

  const SocietySearchForm = () => {
    return (
      <>
        <Box sx={{ pt: 3 }}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="systemCode"
                  control={control}
                  //defaultValue={formData?.systemCode}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="System Code"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="referenceSocietyName"
                  control={control}
                  //defaultValue={formData?.referenceSocietyName}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="Society Name"
                      onChange={onChange}
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };

  const StructuralSearchForm = () => {
    return (
      <>
        <Box sx={{ pt: 3 }}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="systemCode"
                  control={control}
                  //defaultValue={formData?.systemCode}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="System Code"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="companyName"
                  control={control}
                  //defaultValue={formData?.companyName}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="Company Name"
                      onChange={onChange}
                      placeholder="Enter company name"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };

  const PMCSearchForm = () => {
    return (
      <>
        <Box sx={{ pt: 3 }}>
          <Grid container spacing={5}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <Controller
                  name="systemCode"
                  control={control}
                  //defaultValue={formData?.systemCode}
                  render={({ field: { value, onChange } }) => (
                    <TextField
                      value={value}
                      label="System Code"
                      onChange={onChange}
                      placeholder=""
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <SelectProject
                register={register}
                id={"areaofOperation"}
                label={"Area of operation"}
                nameArray={area}
                //defaultValue={formData?.areaofOperation}
                value={areaofOperation}
                onChange={(e) => setAreaofOperation(e.target.value)}
              />
            </Grid>
          </Grid>
        </Box>
      </>
    );
  };

  const renderSearchForm = () => {
    switch (selectedField) {
      case "ARCHITECT":
        return <ArchitectSearchForm />;
      case "BROKER":
        return <BrokerSearchForm />;
      case "CHARTERED_ACCOUNTANT":
        return <CASearchForm />;
      case "SOCIETY":
        return <SocietySearchForm />;
      case "PMC":
        return <PMCSearchForm />;
      case "STRUCTURAL_ENGINEER":
        return <StructuralSearchForm />;
      default:
        return null;
    }
  };

  return (
    <>
      {/* <Tooltip title="Search Filter">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip> */}
      <Grid item xs={12} sm={12}>
        <FormControl fullWidth>
          <Controller
            name="mainSearch"
            control={control}
            defaultValue={name}
            render={({ field: { onChange } }) => (
              <TextField
                id="mainSearch"
                placeholder="Search"
                value={keyword}
                onChange={(e) => {
                  onChange(e.target.value);
                  setKeyword(e.target.value);
                  setSearchKeyword(e.target.value);
                  
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSuccess();
                    fetchUsers(page,pageSize,searchKeyword)
                  }
                }}
                sx={{
                  "& .MuiInputBase-root": {
                    height: "40px", 
                  },
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{ cursor: "pointer", marginRight: "-15px" }}
                        onClick={() => {
                          handleSuccess();
                          fetchUsers(page, pageSize, searchKeyword);
                        }}
                      />{" "}
                    </InputAdornment>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* 
                   <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "100%", sm: 700 } } }}
      >
        <Header>
          <Typography variant="h5">Advanced Search</Typography>
          <Box sx={{position:"absolute",top:"4px",right:"14px"}}>
          <IconButton
            size="small"
            onClick={handleClose}
            sx={{
              p: "0.438rem",
              borderRadius: 1,
              color: "text.primary",
              backgroundColor: "action.selected",
              "&:hover": {
                backgroundColor: (theme) =>
                  `rgba(${theme.palette.customColors.main}, 0.16)`,
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1.125rem" />
          </IconButton>
          </Box>
         
        </Header>
        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>     
            <Grid container spacing={3} alignItems={"center"}>



            <Grid item xs={12} sm={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="mainSearch"
                      control={control}
                      defaultValue={name}
                      render={({ field: { onChange } }) => (
                        <TextField
                          id="mainSearch"
                          label="Main Search"
                          placeholder=""
                          value={keyword}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setKeyword(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

               <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="name"
                      control={control}
                      defaultValue={name}
                      render={({ field: { onChange } }) => (
                        <TextField
                          id="name"
                          label="Name"
                          placeholder=""
                          value={name}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setName(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="email"
                      control={control}
                      rules={{
                        pattern:
                          /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                      }}
                      defaultValue={email}
                      render={({ field: { onChange } }) => (
                        <TextField
                          type="email"
                          value={email}
                          label="Email address"
                          onChange={(e) => {
                            onChange(e.target.value);
                            setEmail(e.target.value);
                          }}
                          error={Boolean(errors.email)}
                          placeholder="Enter email address"
                          aria-describedby="validation-email"
                        />
                      )}
                    />
                    {errors.email?.type === "pattern" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-email"
                      >
                        Please enter a valid email address
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="mobileNumber"
                      control={control}
                      rules={{ pattern: /^[0-9]{10,20}$/ }}
                      defaultValue={mobileNumber}
                      render={({ field: { onChange } }) => (
                        <TextField
                          type="tel"
                          label="Mobile Number"
                          value={mobileNumber}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setMobileNumber(e.target.value);
                          }}
                          error={Boolean(errors.mobileNumber)}
                          placeholder="1234567890"
                          inputProps={{ maxLength: 10 }}
                          aria-describedby="validation-mobileNumber"
                        />
                      )}
                    />
                    {errors.mobileNumber?.type === "pattern" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-mobileNumber"
                      >
                        Please enter a valid 10 digit contact number
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="pincode"
                      control={control}
                      rules={{ pattern: /^[0-9]{10,20}$/ }}
                      defaultValue={pincode}
                      render={({ field: { onChange } }) => (
                        <TextField
                          label="Pincode"
                          value={pincode}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setPincode(e.target.value);
                          }}
                          error={Boolean(errors.pincode)}
                          placeholder="123456"
                          inputProps={{ maxLength: 6 }}
                          aria-describedby="validation-pincode"
                        />
                      )}
                    />
                    {errors.pincode?.type === "pattern" && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-pincode"
                      >
                        Please enter a valid 6 digit pincode
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <Controller
                      name="remarks"
                      control={control}
                      defaultValue={remarks}
                      render={({ field: { onChange } }) => (
                        <TextField
                          id="remarks"
                          label="Remarks"
                          placeholder=""
                          value={remarks}
                          onChange={(e) => {
                            onChange(e.target.value);
                            setRemarks(e.target.value);
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <SelectProject
                    register={register}
                    id="entityCategory"
                    label="Select Category"
                    name="entityCategory"
                    nameArray={entityCategoryOptions}
                    value={selectedField}
                    onChange={handleFieldChange}
                  />
                </Grid>

                <Grid item xs={12} sm={12}>
                  {renderSearchForm()}
                </Grid>    
            </Grid>
                </Box>
                </PerfectScrollbar>

          <Box sx={{ 
            borderTop: theme => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),justifyContent:'center',
            display: "flex", alignItems: "center" 
            }}>
            <Button type="submit" variant="contained" sx={{ mr: 3 }} onClick={handleSuccess}>
              Search
            </Button>
            <Button variant="tonal" color="secondary" onClick={handleClose}>
              Cancel
            </Button>
          </Box>
          
        
      </Drawer>

                */}
    </>
  );
};

export default AddNewEntityDrawer;
