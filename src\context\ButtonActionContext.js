// context/ButtonActionContext.js
import React, { createContext, useContext, useState } from 'react';

const ButtonActionContext = createContext();

export const ButtonActionProvider = ({ children }) => {
  const [buttonClicked, setButtonClicked] = useState(false);

  return (
    <ButtonActionContext.Provider value={{ buttonClicked, setButtonClicked }}>
      {children}
    </ButtonActionContext.Provider>
  );
};

export const useButtonAction = () => useContext(ButtonActionContext);
