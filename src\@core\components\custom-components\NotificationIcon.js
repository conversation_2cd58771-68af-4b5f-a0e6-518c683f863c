import React from 'react';
import { Badge, IconButton } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';

const NotificationIcon = ({ unreadCount, onClick }) => {
  return (
    <IconButton onClick={onClick}>
    <Badge
  badgeContent={unreadCount}
  color="error"
  componentsProps={{
    badge: {
      sx: {
        fontSize: '0.6rem', // Adjust the font size as needed
      },
    },
  }}
>

      <NotificationsIcon sx={{ fontSize: 30 }} /> 
    </Badge>
  </IconButton>
  );
};

export default NotificationIcon;
