// ** React Imports
import { useEffect, useState } from 'react';

// ** MUI Imports
import Tab from '@mui/material/Tab';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import TabContext from '@mui/lab/TabContext';
import Typography from '@mui/material/Typography';
import Icon from 'src/@core/components/icon';

const NavTabsProfiles = props => {
  // ** Props
  const { tabContent1, tabContent2, tabContent3, tabContent4, tabContent5, activeTab, onTabChange } = props;

  // ** State
  const [value, setValue] = useState(activeTab || 'onboarded'); // Default to 'all'

  // Sync state with the provided activeTab prop
  useEffect(() => {
    if (activeTab) {
      setValue(activeTab);
    }
    else{
      setValue("onboarded")
    }
  }, [activeTab,value]);

  const handleChange = (event, newValue) => {
    setValue(newValue);
    if (onTabChange) {
      onTabChange(newValue); // Notify parent of tab change
    }
  };

  return (
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleChange}
        aria-label="scrollable tabs example"
      >
        <Tab
          value="onboarded"
          label="Onboarded"
          sx={{ textTransform: 'none', color: '#384551' }}
          icon={<Icon fontSize='1.125rem' icon='tabler:user-check' />}
        />
        <Tab
          value="snapshots"
          label="Snapshots"
          sx={{ textTransform: 'none', color: '#384551' }}
          icon={<Icon fontSize='1.125rem' icon='tabler:camera' />}
        />
        <Tab
          value="email"
          label="Lead Info Capture"
          sx={{ textTransform: 'none', color: '#384551' }}
          icon={<Icon icon="tabler:mail" fontSize='1.125rem' />}
        />
      </TabList>
      <TabPanel value="onboarded" sx={{ pb: 1, pt: 3, px: { xs: '0', md: '0.5rem' } }}>
        <Typography>{tabContent1}</Typography>
      </TabPanel>
      <TabPanel value="snapshots" sx={{ pb: 1, pt: 3, px: { xs: '0', md: '0.5rem' } }}>
        <Typography>{tabContent2}</Typography>
      </TabPanel>
      <TabPanel value="email" sx={{ pb: 1, pt: 3, px: { xs: '0', md: '0.5rem' } }}>
        <Typography>{tabContent3}</Typography>
      </TabPanel>
    </TabContext>
  );
};

export default NavTabsProfiles;
