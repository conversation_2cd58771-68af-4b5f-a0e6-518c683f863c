// ** React Imports

// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { Box, Button, FormControl, TextField } from "@mui/material";
//import { C } from '@fullcalendar/core/internal-common'

import { useAuth } from "src/hooks/useAuth";
import { yupResolver } from "@hookform/resolvers/yup";
import RemarksValidation, { yup } from "./RemarksValidaion";
import React, { useState } from "react";

// ** Icon Imports

const RemarksEdit = ({ onCancel, formData }) => {
  const auth = useAuth();

  // ** Hooks
  const {
    register,
    setError,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(RemarksValidation(yup)),
    mode: "onChange",
  });
  const [fieldChanged, setFieldChanged] = useState(false);

  async function submit(data) {
    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );
    const response = await auth.updateEntity(trimmedData, () => {
      console.error("Remarks Details failed");
    });
    onCancel();
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="remarks"
                control={control}
                defaultValue={formData?.remarks}
                render={({ field }) => (
                  <TextField
                    {...field}
                    rows={4}
                    multiline
                    label="remarks"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.remarks)}
                    helperText={errors.remarks?.message}
                    aria-describedby="Section2_remarks"
                    onChange={(e) => {
                      field.onChange(e);
                      setFieldChanged(true);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={!fieldChanged}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default RemarksEdit;
