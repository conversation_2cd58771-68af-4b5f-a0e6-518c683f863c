import React, { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import {
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
  Card,
} from "@mui/material";
import { Icon } from "@iconify/react";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";

dayjs.extend(isBetween);

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: 200, // Set the maximum height of the menu
      overflowY: "auto", // Enable vertical scrolling
    },
  },
};

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const SocietyOnboardingChartFilter = () => {

  const router = useRouter();
  const [sampleData, setSampleData] = useState({});
  const [employeesData, setEmployeesData] = useState(null);
  const [employeeId, setEmployeeId] = useState(null);
  const [fromDate, setFromDate] = useState(dayjs().format("YYYY-MM-DD"));
  const [toDate, setToDate] = useState(dayjs().format("YYYY-MM-DD"));
  const [anchorEl, setAnchorEl] = useState(null);
  useEffect(() => {
    const data = {
      roleId: "ad60ce4e-f528-4722-82aa-8757baafce7a",
      fromDate: fromDate,
      toDate: toDate,
      employeeId: employeeId,
    };
    const url = getUrl(
      authConfig?.statisticsEndpointGraphs + "/sp-chs-statistics"
    );

    const headers = getAuthorizationHeaders({
      contentType: authConfig.STATISTICS_GET_SP_CHS_STATISTICS_REQUEST,
      accept: authConfig?.STATISTICS_GET_SP_CHS_STATISTICS_RESPONSE,
    });

    axios({
      method: "post",
      url: url,
      headers: headers,
      data: data,
    })
      .then((res) => {
        setSampleData(res?.data?.spAndChsStatistics);
      })
      .catch((err) => console.log("Employees error", err));
  }, [fromDate, toDate, employeeId]);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdown) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

    const handleFromDateChange = (e) => {
      const newFromDate = e.target.value;
      setFromDate(newFromDate);
      if (dayjs(newFromDate).isAfter(toDate)) {
        setToDate(newFromDate);
      }
    };
  
    const handleToDateChange = (e) => {
      const newToDate = e.target.value;
      setToDate(newToDate);
      if (dayjs(newToDate).isBefore(fromDate)) {
        setFromDate(newToDate);
      }
    };

  // Extracting categories and values from sampleData
  const categories = Object.keys(sampleData);
  const seriesData = Object.values(sampleData);


  const stateRef = useRef();
    stateRef.current = { fromDate, toDate, employeeId };
  
    const handleBarClick = (event, chartContext, config) => {
      const clickedCategory = categories[config.dataPointIndex];
     
  
      const { fromDate, toDate, employeeId } = stateRef.current;
  
      console.log("Current values:", fromDate, toDate, employeeId);
  
      const queryParams = {
        fromDate: fromDate,
        toDate: toDate,
        employee: employeeId,
      };
  
      switch (clickedCategory) {
        case "Onboarded":
          router.push({
            pathname: "/CHS",
            query: queryParams,
          });
          break;
  
        case "Meetings Happened":
          router.push({
            pathname: "/appointments-calendar",
            query: queryParams,
          });
          break;
  
        case "Total Conversations":
          router.push({
            pathname: "/CHS/conversations",
            query: queryParams,
          });
          break;
  
        case "Requisitions Raised":
          router.push({
            pathname: "/service-requisitions",
            query: queryParams,
          });
          break;
  
        default:
          break;
      }
    };

  const options = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: { show: false },
      events: {
        dataPointSelection: handleBarClick, // Add click event handler
      },
    },
    xaxis: {
      categories,
      title: { text: "CHS's Count" },
    },
    yaxis: {
      title: { text: "Category" },
    },
    plotOptions: { bar: { horizontal: true } },
    // colors: ["rgb(16, 138, 0)"],
    colors: ["rgb(96, 194, 96)"],
    dataLabels: {
      enabled: true,
      style: {
        colors: ["black"],
      },
    },
  };

  return (
    <Card sx={{ mb: 2,mt:2, p: 2, borderRadius: 2, boxShadow: 2 }}>
      <Box width="100%" p={3}>
        {/* Header Section */}
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mb={3}
        >
          <Typography variant="body1" fontWeight="bold" color="black">
            {employeeId
              ? `CHS metrics onboarded by ${
                  employeesData?.find((item) => item.id === employeeId)?.name ||
                  ""
                }`
              : "Total CHS metrics"}
          </Typography>
          <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
            <Icon
              icon="tabler:baseline-density-medium"
              width="20"
              height="20"
              style={{ cursor: "pointer", color: "black" }}
            />
          </IconButton>
        </Box>

        <Box display="flex" gap={2} mb={3}>
          <label>
            From:
            <input
              type="date"
              value={fromDate}
              onChange={handleFromDateChange}
              max={dayjs().format("YYYY-MM-DD")}
            />
          </label>
          <label>
            To:
            <input
              type="date"
              value={toDate}
              onChange={handleToDateChange}
              min={fromDate}
              max={dayjs().format("YYYY-MM-DD")}
            />
          </label>
          <label>
            <Typography
              variant="body1"
              component="span"
              sx={{ fontWeight: "bold" }}
            >
              No. of Day(s):
            </Typography>
            <Typography variant="body1" component="span" sx={{ marginLeft: 1 }}>
              {dayjs(toDate).diff(dayjs(fromDate), "days") + 1 || "0"}
            </Typography>
          </label>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          {...MenuProps}
        >
          <MenuItem
            key="all"
            onClick={() => {
              setEmployeeId(null);
              setAnchorEl(null);
            }}
          >
            All Employees
          </MenuItem>
          {employeesData?.map((employee) => (
            <MenuItem
              key={employee.id}
              onClick={() => {
                setEmployeeId(employee.id);
                setAnchorEl(null);
              }}
            >
              {employee.name}
            </MenuItem>
          ))}
        </Menu>

        {/* Chart */}
        <ApexChart
          options={options}
          series={[{ name: "Count", data: seriesData }]}
          type="bar"
          height={350}
        />
      </Box>
    </Card>
  );
};

export default SocietyOnboardingChartFilter;
