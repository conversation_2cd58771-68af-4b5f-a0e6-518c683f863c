import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const RadialBarChart = () => {
  const state = {
    series: [70, 25, 4, 1], // Example data values for Service Providers, Societies, and Employees
    options: {
      chart: {
        height: 350,
        type: 'radialBar',
      },
      plotOptions: {
        radialBar: {
          dataLabels: {
            name: {
              fontSize: '22px',
            },
            value: {
              fontSize: '16px',
            },
            total: {
              show: true,
              label: 'Total',
              formatter: function (w) {
                // Custom formatter function to calculate the total or another relevant metric
                return state.series.reduce((a, b) => a + b, 0); // Sum of all series
              },
            },
          },
        },
      },
      labels: ['Service Providers', 'Societies', 'Employees', 'Other'], // Updated labels
    },
  };

  return (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="radialBar" height={350} />
      </div>
    </div>
  );
};

export default RadialBarChart;
