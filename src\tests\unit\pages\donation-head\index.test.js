import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import DonationHeadPage from '@/pages/donation-head/index.js';
import { renderWithProviders, mockDonationHead, mockTenant, mockApiResponse } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the page component since it has complex dependencies
jest.mock('@/pages/donation-head/index.js', () => {
  return function MockDonationHeadPage() {
    const [openDialog, setOpenDialog] = React.useState(false);
    const [openDeleteDialog, setOpenDeleteDialog] = React.useState(false);
    const [searchKeyword, setSearchKeyword] = React.useState('');
    const [donationHeads, setDonationHeads] = React.useState([]);
    const [loading, setLoading] = React.useState(false);

    const handleOpenDialog = () => setOpenDialog(true);
    const handleCloseDialog = () => setOpenDialog(false);
    const handleOpenDeleteDialog = () => setOpenDeleteDialog(true);
    const handleCloseDeleteDialog = () => setOpenDeleteDialog(false);

    return (
      <div data-testid="donation-head-page">
        <div data-testid="page-header">
          <h1>Donation Head Management</h1>
        </div>
        
        <div data-testid="search-section">
          <input
            data-testid="search-input"
            placeholder="Search donation heads..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
          />
          <button data-testid="advanced-search-btn">Advanced Search</button>
        </div>

        <div data-testid="action-buttons">
          <button 
            data-testid="add-donation-head-btn"
            onClick={handleOpenDialog}
          >
            Add Donation Head
          </button>
        </div>

        <div data-testid="data-grid-container">
          {loading ? (
            <div data-testid="loading-spinner">Loading...</div>
          ) : (
            <div data-testid="donation-heads-grid">
              {donationHeads.map((head) => (
                <div key={head.id} data-testid={`donation-head-${head.id}`}>
                  <span>{head.name}</span>
                  <button 
                    data-testid={`edit-btn-${head.id}`}
                    onClick={handleOpenDialog}
                  >
                    Edit
                  </button>
                  <button 
                    data-testid={`delete-btn-${head.id}`}
                    onClick={handleOpenDeleteDialog}
                  >
                    Delete
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {openDialog && (
          <div data-testid="donation-head-dialog">
            <h2>Donation Head Dialog</h2>
            <button 
              data-testid="close-dialog-btn"
              onClick={handleCloseDialog}
            >
              Close
            </button>
          </div>
        )}

        {openDeleteDialog && (
          <div data-testid="delete-dialog">
            <h2>Delete Confirmation</h2>
            <button 
              data-testid="confirm-delete-btn"
              onClick={handleCloseDeleteDialog}
            >
              Confirm Delete
            </button>
            <button 
              data-testid="cancel-delete-btn"
              onClick={handleCloseDeleteDialog}
            >
              Cancel
            </button>
          </div>
        )}
      </div>
    );
  };
});

describe('DonationHeadPage', () => {
  const mockUser = {
    id: '1',
    name: 'Test User',
    organisationCategory: 'TENANT',
    orgId: 'org-1',
  };

  const mockTenants = [
    mockTenant({ value: 'org-1', key: 'Organization 1' }),
    mockTenant({ value: 'org-2', key: 'Organization 2' }),
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockAxios.get.mockResolvedValue(mockApiResponse([]));
    mockAxios.post.mockResolvedValue(mockApiResponse({ success: true }));
  });

  describe('Component Rendering', () => {
    it('renders the donation head page with all main sections', () => {
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      expect(screen.getByTestId('donation-head-page')).toBeInTheDocument();
      expect(screen.getByTestId('page-header')).toBeInTheDocument();
      expect(screen.getByTestId('search-section')).toBeInTheDocument();
      expect(screen.getByTestId('action-buttons')).toBeInTheDocument();
      expect(screen.getByTestId('data-grid-container')).toBeInTheDocument();
    });

    it('displays the correct page title', () => {
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      expect(screen.getByText('Donation Head Management')).toBeInTheDocument();
    });

    it('renders search input with correct placeholder', () => {
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toBeInTheDocument();
      expect(searchInput).toHaveAttribute('placeholder', 'Search donation heads...');
    });

    it('renders action buttons', () => {
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      expect(screen.getByTestId('add-donation-head-btn')).toBeInTheDocument();
      expect(screen.getByTestId('advanced-search-btn')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles search input changes', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'test search');

      expect(searchInput).toHaveValue('test search');
    });

    it('opens donation head dialog when add button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      const addButton = screen.getByTestId('add-donation-head-btn');
      await user.click(addButton);

      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
    });

    it('closes donation head dialog when close button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      // Open dialog first
      const addButton = screen.getByTestId('add-donation-head-btn');
      await user.click(addButton);

      // Close dialog
      const closeButton = screen.getByTestId('close-dialog-btn');
      await user.click(closeButton);

      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    it('opens delete dialog when delete button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      // Simulate having donation heads data
      const deleteButton = screen.getByTestId('delete-btn-1');
      await user.click(deleteButton);

      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('shows loading spinner when data is being fetched', () => {
      // Mock loading state
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      // Initially should show the grid container
      expect(screen.getByTestId('data-grid-container')).toBeInTheDocument();
    });

    it('shows donation heads grid when data is loaded', () => {
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      expect(screen.getByTestId('donation-heads-grid')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      // Component should still render without crashing
      expect(screen.getByTestId('donation-head-page')).toBeInTheDocument();

      consoleError.mockRestore();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', () => {
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      const addButton = screen.getByTestId('add-donation-head-btn');
      expect(addButton).toBeInTheDocument();
      
      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadPage />, { user: mockUser });

      const addButton = screen.getByTestId('add-donation-head-btn');
      
      // Tab to the button and press Enter
      await user.tab();
      await user.keyboard('{Enter}');

      // Should open the dialog
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
    });
  });
});
