import { useContext, useEffect, useState } from "react";
import Drawer from "@mui/material/Drawer";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { styled } from "@mui/material/styles";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import { Grid, FormControl, InputLabel, Select, FormHelperText, FormControlLabel, Checkbox } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import Icon from "src/@core/components/icon";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "src/pages/permission/RBACContext";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const { open, toggle, searchKeyword, setSearchData, fetchServiceProviders, page, pageSize } = props;
  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const { control, setValue, reset, getValues, formState: { errors } } = useForm({
    defaultValues: {
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
    },
  });

  const handleCancel = () => { 
    reset({
      serviceTypeUUIDs: [],
      locationUUIDs: [],
      leadStatusUUIDs: [],
      leadPriorityUUIDs: [],
      isMicroSiteEmpanelled: null,
      isListingEmpanelled: null,
    });
    setSearchData({})
    fetchServiceProviders(page, pageSize, searchKeyword)
  };

  const handleClose = () => {
    toggle();
  };

  const [listOfEmployees, setListOfEmployees] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);
  const [employeeId, setEmployeeId] = useState("");

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfEmployees(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (listOfEmployees.length) {
      const data = listOfEmployees.map((entry) => ({ value: entry.id, key: entry.name }));
      setEmployeesOptions(data);
    }
  }, [listOfEmployees]);

  const updateFilters = (data) => {
    setSearchData(data);
    fetchServiceProviders(page, pageSize, searchKeyword, data);
  };

  useEffect(() => {
    if (authConfig) {
      getAllListValuesByListNameId(authConfig.leadPriorityListNamesId, handleLeadPrioritySuccess, handleError);
      getAllListValuesByListNameId(authConfig.leadStatusListNamesId, handleLeadStatusSuccess, handleError);
      getAllListValuesByListNameId(authConfig.locationlistNameId, handleLocationsSuccess, handleError);
      getAllListValuesByListNameId(authConfig.allServicesListNameId, handleServices, handleError);
    }
  }, [authConfig]);
  const { can } = useRBAC();

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);
  const [locationsData, setLocationsData] = useState(null);
  const [services, setServices] = useState(null);

  const handleLeadStatusSuccess = (data) => setLeadStatusData(data?.listValues);
  const handleLeadPrioritySuccess = (data) => setLeadPriorityData(data?.listValues);
  const handleLocationsSuccess = (data) => setLocationsData(data?.listValues);
  const handleServices = (data) => setServices(data?.listValues);
  const handleError = (error) => console.error("Error:", error);



  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>
      
      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "100%", sm: 500 } } }}
      >
        <Header>
          <Typography variant="h5">Advanced Search</Typography>
          <Box sx={{position:"absolute",top:"8px",right:"14px"}}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color:"common.white", 
                  backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: 
                  '#66BB6A',
                   transition: 'background 0.5s ease, transform 0.5s ease',                       
                  },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>
        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>     
            <Grid container spacing={3} alignItems={"center"}>
              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <InputLabel id="services-select-label">Services</InputLabel>
                  <Controller
                    name="serviceTypeUUIDs"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="services-select-label"
                        id="services-select"
                        size="small"
                        multiple
                        value={field.value || []}
                        label="Services"
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({ ...getValues(), serviceTypeUUIDs: e.target.value });
                        }}
                      >
                        {services?.map((service) => (
                          <MenuItem key={service.id} value={service.id}>
                            {service.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
                {errors.service && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-service">
                    {errors.service?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <InputLabel id="lead-status-select-label">Lead Status</InputLabel>
                  <Controller
                    name="leadStatusUUIDs"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="lead-status-select-label"
                        id="lead-status-select"
                        size="small"
                        multiple
                        value={field.value || []}
                        label="Lead Status"
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({ ...getValues(), leadStatusUUIDs: e.target.value });
                        }}
                      >
                        {leadStatusData?.map((leadStatus) => (
                          <MenuItem key={leadStatus.id} value={leadStatus.id}>
                            {leadStatus.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
                {errors.leadStatus && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-leadStatus">
                    {errors.leadStatus?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <InputLabel id="lead-priority-select-label">Lead Priority</InputLabel>
                  <Controller
                    name="leadPriorityUUIDs"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="lead-priority-select-label"
                        id="lead-priority-select"
                        size="small"
                        multiple
                        value={field.value || []}
                        label="Lead Priority"
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({ ...getValues(), leadPriorityUUIDs: e.target.value });
                        }}
                      >
                        {leadPriorityData?.map((leadPriority) => (
                          <MenuItem key={leadPriority.id} value={leadPriority.id}>
                            {leadPriority.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
                {errors.leadPriority && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-leadPriority">
                    {errors.leadPriority?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <InputLabel id="locations-select-label">Location</InputLabel>
                  <Controller
                    name="locationUUIDs"
                    control={control}
                    render={({ field }) => (
                      <Select
                        {...field}
                        labelId="locations-select-label"
                        id="locations-select"
                        size="small"
                        multiple
                        value={field.value || []}
                        label="Location"
                        onChange={(e) => {
                          field.onChange(e);
                          updateFilters({ ...getValues(), locationUUIDs: e.target.value });
                        }}
                      >
                        {locationsData?.map((location) => (
                          <MenuItem key={location.id} value={location.id}>
                            {location.listValue}
                          </MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                </FormControl>
                {errors.location && (
                  <FormHelperText sx={{ color: "error.main" }} id="validation-location">
                    {errors.location?.message}
                  </FormHelperText>
                )}
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl>
                  <Controller
                    name="isListingEmpanelled"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              const value = e.target.checked ? true : null;
                              setValue("isListingEmpanelled", value);
                              updateFilters({ ...getValues(), isListingEmpanelled: value });
                            }}
                          />
                        }
                        label={<span>Listing Empanelled</span>}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl>
                  <Controller
                    name="isMicroSiteEmpanelled"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value}
                            style={{ transform: "scale(1)" }}
                            onChange={(e) => {
                              field.onChange(e);
                              const value = e.target.checked ? true : null;
                              setValue("isMicroSiteEmpanelled", value);
                              updateFilters({ ...getValues(), isMicroSiteEmpanelled: value });
                            }}
                          />
                        }
                        label={<span>MicroSite Empanelled</span>}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box sx={{ 
          borderTop: theme => `1px solid ${theme.palette.divider}`,
          p: (theme) => theme.spacing(2),justifyContent:'center',
          display: "flex", alignItems: "center" 
        }}>
           
          <Button variant="contained" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="tonal" onClick={handleClose}>
              Close
            </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
