// Jest setup file
import '@testing-library/jest-dom';

// Set up environment variables for tests
process.env.VITE_APP_API_URL = 'http://localhost:8080';

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock Next.js router for donation modules
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    pathname: '/test',
    query: {},
    asPath: '/test',
  }),
}));

// Mock dynamic imports for ApexCharts
jest.mock('react-apexcharts', () => {
  return function MockChart({ options, series, type, height }) {
    return (
      <div
        data-testid="mock-chart"
        data-type={type}
        data-height={height}
        data-series={JSON.stringify(series)}
        data-options={JSON.stringify(options)}
      >
        Mock Chart ({type})
      </div>
    );
  };
});

// Mock dynamic imports for Next.js
jest.mock('next/dynamic', () => {
  return (fn) => {
    const Component = fn();
    return Component;
  };
});
