import { useState } from "react";
import { useForm } from "react-hook-form";

import Menu from "@mui/material/Menu";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { Grid } from "@mui/material";

const NewUserDropDown = ({
  register,
  id,
  nameArray,
  setValue,
  setShowForm,
  setEntityCategory,
  getValues,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedValue, setSelectedValue] = useState("");

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuClick = (value) => {
    setSelectedValue(value);
    setShowForm(value);
    setEntityCategory(value);
    setValue("entityCategory", value);
    handleClose();
  };

  return (
    <div>
      <Grid container justifyContent="flex-end">
        <Grid item>
          <Button
            aria-controls="simple-menu"
            aria-haspopup="true"
            onClick={handleClick}
            variant="contained"
            sx={{ px: 3 }}
          >
            Add New User
          </Button>
        </Grid>
      </Grid>
      <Menu
        id={id}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        open={Boolean(anchorEl)}
        keepMounted
      >
        {nameArray.map((name) => (
          <MenuItem
            onClick={() => handleMenuClick(name.value)}
            key={name.value}
            selected={name.value === selectedValue}
          >
            {name.key}
          </MenuItem>
        ))}
      </Menu>
      <input
        type="hidden"
        {...register("entityCategory", { required: true })}
        value={getValues("entityCategory")}
      />
    </div>
  );
};

export default NewUserDropDown;
