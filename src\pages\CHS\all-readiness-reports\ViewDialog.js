// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import axios from "axios";
// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

// ** Styled Component
import {
  Box,
  Button,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";
import { DataGrid } from "@mui/x-data-grid";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const SampleReadiness = ({
  open,
  onClose,
  title
}) => {
  // ** Hook
  const theme = useTheme();


  const handleClose = () => {
    onClose();
  };

  return (
    <>
      <Dialog open={open} onClose={handleClose} fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
            height:"50px", // height
          }}
          textAlign={"center"}
        >
        {title} Readiness Details
          <Box sx={{ position: "absolute", top: "9px", right: "10px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
           
          </>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height:"50px", // height
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleClose}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
   
    </>
  );
};
export default SampleReadiness;
