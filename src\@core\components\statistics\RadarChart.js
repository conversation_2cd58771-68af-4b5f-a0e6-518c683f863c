import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const RadarChart = () => {
  const state = {
    series: [{
      name: 'Performance', // Example series name
      data: [85, 75, 65], // Example data corresponding to the actors
    }],
    options: {
      chart: {
        height: 350,
        type: 'radar',
      },
      title: {
        text: 'Performance Overview of Actors',
        align: 'center',
      },
      yaxis: {
        stepSize: 20
      },
      xaxis: {
        categories: ['Service Providers', 'Societies', 'Employees'] // Updated categories
      },
      colors: ['#FF4560', '#00E396', '#008FFB'], // Custom colors for the chart
      legend: {
        position: 'right',
        offsetY: 0,
        itemMargin: {
          horizontal: 5,
          vertical: 5
        }
      }
    },
  };

  return (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="radar" height={350} />
      </div>
    </div>
  );
};

export default RadarChart;
