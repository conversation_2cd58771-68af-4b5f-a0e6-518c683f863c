import React from 'react';
import { render } from '@testing-library/react';

// Simple render function for testing
export const renderComponent = (component) => {
  return render(component);
};

// Mock data generators
export const mockDonationHead = (overrides = {}) => ({
  id: '1',
  name: 'Test Donation Head',
  description: 'Test description',
  orgId: 'org-1',
  isActive: true,
  createdOn: '2024-01-01T00:00:00Z',
  updatedOn: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockTenant = (overrides = {}) => ({
  value: 'org-1',
  key: 'Test Organization',
  label: 'Test Organization',
  ...overrides,
});

export const mockApiResponse = (data, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
});
