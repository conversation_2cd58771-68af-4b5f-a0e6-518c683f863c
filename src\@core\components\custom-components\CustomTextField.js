import { forwardRef } from 'react';
import TextField from '@mui/material/TextField';
import { styled } from '@mui/material/styles';

const TextFieldStyled = styled(TextField)(({ theme }) => ({
  '& .MuiInputLabel-root': {
    // Control label positioning, color, and interaction
    position: 'absolute',
    left: 12,
    top: '50%', // Start vertically centered
    transform: 'translateY(-50%)', // Perfect centering
    color: theme.palette.text.secondary, // Default color
    transition: theme.transitions.create(['color', 'transform', 'top'], {
      duration: theme.transitions.duration.shorter,
      easing: theme.transitions.easing.easeOut,
    }),
    pointerEvents: 'none', // Ignore pointer events to pass through clicks
  },
  '& .MuiInputLabel-shrink': {
    // Control the label shrink
    transform: 'translateY(-100%) scale(0.75)', // Move up and scale down
    // color: theme.palette.primary.main, // Change color on active/focus
  },
  '& .MuiInputBase-root': {
    // General styling for the input base
    alignItems: 'center',
    padding: '18px 14px', // Adjust padding as necessary
  },
  '& .MuiInputBase-input': {
    // Adjust the input field padding
    paddingLeft: '0 !important', // Zero out left padding
  },
  '&.Mui-focused fieldset': {  
    borderColor: theme.palette.primary.main, // Border color on focus
    color: theme.palette.primary.main,
  },
}));

const CustomTextField = forwardRef((props, ref) => {
  const { InputLabelProps, ...rest } = props;

  return (
    <TextFieldStyled
      ref={ref}
      InputLabelProps={{
        ...InputLabelProps,
        shrink: true,
        style: { transform: 'translate(0, -135%) scale(0.9)' },
      }}
      {...rest}
    />
  );
});

export default CustomTextField;
