// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'

const NavTabsSettings = props => {
  // ** State
  const [value, setValue] = useState('1')

  // ** Props
  const { tabContent1, tabContent2, tabContent3, tabContent4,tabContent5,tabContent6,tabContent7,tabContent8,tabContent9,tabContent10,tabContent11,tabContent12 } = props

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  return (    
    
    <TabContext value={value}>
      <TabList
        variant="scrollable"
        scrollButtons="auto"
        onChange={handleChange}
        aria-label="forced scroll tabs example"
      >
        <Tab
          value="1"
          component="a"
          label="Initiating Redevelopment"
          href="/drafts"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="2"
          component="a"
          label="Types of Redevelopment"
          href="/trash"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="3"
          component="a"
          label="Redevelopment Committee"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="4"
          component="a"
          label="Consents"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="5"
          component="a"
          label="Conveyance"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="6"
          component="a"
          label="Valid Tenure of Managing Committee"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="7"
          component="a"
          label="Redevelopment Documents"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="8"
          component="a"
          label="Appointment of Professionals"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="9"
          component="a"
          label="PreTendering Stage"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="10"
          component="a"
          label="Tendering Stage"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        <Tab
          value="11"
          component="a"
          label="Financial Closure"
          href="/spam"
          onClick={(e) => e.preventDefault()}
        />
        {tabContent12 && (
          <Tab
            value="4"
            component="a"
            label="Other Service"
            href="/spam"
            onClick={(e) => e.preventDefault()}
          />
        )}
      </TabList>
      <TabPanel value="1" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent1}</Typography>
      </TabPanel>
      <TabPanel value="2" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent2}</Typography>
      </TabPanel>
      <TabPanel value="3" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent3}</Typography>
      </TabPanel>
      <TabPanel value="4" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent4}</Typography>
      </TabPanel>
      <TabPanel value="5" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent5}</Typography>
      </TabPanel>
      <TabPanel value="6" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent6}</Typography>
      </TabPanel>
      <TabPanel value="7" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent7}</Typography>
      </TabPanel>
      <TabPanel value="8" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent8}</Typography>
      </TabPanel>
      <TabPanel value="9" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent9}</Typography>
      </TabPanel>
      <TabPanel value="10" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent10}</Typography>
      </TabPanel>
      <TabPanel value="11" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent11}</Typography>
      </TabPanel>
      <TabPanel value="12" sx={{ pb: 1, pt: 3.5, px: { xs: 1.25, md: "0.5rem" } }}>
        <Typography>{tabContent12}</Typography>
      </TabPanel>
    </TabContext>
  );
}

export default NavTabsSettings
