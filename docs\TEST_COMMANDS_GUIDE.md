# Test Commands Guide - Donation Receipt Frontend

## Quick Start

### Run All Tests
```bash
npm test
```

### Run Tests with Coverage
```bash
npm test -- --coverage
```

### Run Tests in Watch Mode
```bash
npm test -- --watch
```

## Basic Test Commands

### 1. **Standard Test Execution**
```bash
# Run all tests once
npm test

# Run all tests with verbose output
npm test -- --verbose

# Run tests and exit (no watch mode)
npm test -- --watchAll=false

# Run tests with no test files (useful for CI)
npm test -- --passWithNoTests
```

### 2. **Coverage Reports**
```bash
# Generate coverage report
npm test -- --coverage

# Generate coverage with specific threshold
npm test -- --coverage --coverageThreshold='{"global":{"branches":80,"functions":80,"lines":80,"statements":80}}'

# Generate coverage in different formats
npm test -- --coverage --coverageReporters=text,lcov,html

# Open coverage report in browser (after running coverage)
open coverage/lcov-report/index.html
```

### 3. **Watch Mode Options**
```bash
# Watch mode (re-runs tests on file changes)
npm test -- --watch

# Watch all files (including non-git files)
npm test -- --watchAll

# Watch mode with coverage
npm test -- --watch --coverage
```

## Targeted Test Execution

### 4. **Run Specific Test Files**
```bash
# Run specific test file
npm test -- src/tests/unit/pages/donation-head/index.test.js

# Run multiple specific files
npm test -- src/tests/unit/pages/donation-head/index.test.js src/tests/unit/pages/donation-admin/index.test.js

# Run tests matching pattern
npm test -- --testPathPattern="donation-head"
```

### 5. **Run Tests by Module**
```bash
# Run all donation-head tests
npm test -- src/tests/unit/pages/donation-head/

# Run all donation-admin tests
npm test -- src/tests/unit/pages/donation-admin/

# Run all donation-tenant tests
npm test -- src/tests/unit/pages/donation-tenant/

# Run all unit tests
npm test -- src/tests/unit/
```

### 6. **Run Tests by Name Pattern**
```bash
# Run tests with specific name
npm test -- --testNamePattern="should pass basic test"

# Run tests containing specific word
npm test -- --testNamePattern="dialog"

# Run tests with regex pattern
npm test -- --testNamePattern="(dialog|search)"
```

## Advanced Test Commands

### 7. **Debugging and Development**
```bash
# Run tests with debug information
npm test -- --verbose --no-cache

# Run tests with custom timeout
npm test -- --testTimeout=10000

# Run tests in band (no parallel execution)
npm test -- --runInBand

# Run tests with maximum workers
npm test -- --maxWorkers=4
```

### 8. **Output and Reporting**
```bash
# Silent mode (minimal output)
npm test -- --silent

# JSON output for CI/CD
npm test -- --json --outputFile=test-results.json

# JUnit XML output
npm test -- --reporters=default --reporters=jest-junit

# Custom reporter
npm test -- --reporters=default --reporters=./custom-reporter.js
```

### 9. **Cache and Performance**
```bash
# Clear Jest cache
npm test -- --clearCache

# Run without cache
npm test -- --no-cache

# Show cache directory
npm test -- --showConfig
```

## Module-Specific Commands

### 10. **Donation Head Module**
```bash
# Run all donation-head tests
npm test -- src/tests/unit/pages/donation-head/

# Run main page test
npm test -- src/tests/unit/pages/donation-head/index.test.js

# Run dialog tests
npm test -- src/tests/unit/pages/donation-head/DonationHeadDialog.test.js

# Run search tests
npm test -- src/tests/unit/pages/donation-head/AdvancedSearch.test.js

# Run column tests
npm test -- src/tests/unit/pages/donation-head/Columns.test.js

# Run delete dialog tests
npm test -- src/tests/unit/pages/donation-head/DeleteDialog.test.js
```

### 11. **Donation Admin Module**
```bash
# Run donation-admin tests
npm test -- src/tests/unit/pages/donation-admin/

# Run admin dashboard test
npm test -- src/tests/unit/pages/donation-admin/index.test.js
```

### 12. **Donation Tenant Module**
```bash
# Run donation-tenant tests
npm test -- src/tests/unit/pages/donation-tenant/

# Run tenant dashboard test
npm test -- src/tests/unit/pages/donation-tenant/index.test.js
```

## CI/CD Commands

### 13. **Continuous Integration**
```bash
# CI-friendly test run
npm test -- --ci --coverage --watchAll=false

# Generate test results for CI
npm test -- --ci --json --outputFile=test-results.json --coverage --coverageReporters=cobertura

# Run tests with exit code
npm test -- --passWithNoTests --ci --coverage --watchAll=false
```

### 14. **Quality Gates**
```bash
# Run tests with coverage threshold
npm test -- --coverage --coverageThreshold='{"global":{"branches":75,"functions":80,"lines":80,"statements":80}}'

# Fail if coverage below threshold
npm test -- --coverage --coverageThreshold='{"global":{"branches":75,"functions":80,"lines":80,"statements":80}}' --ci
```

## Utility Commands

### 15. **Configuration and Setup**
```bash
# Show Jest configuration
npm test -- --showConfig

# Initialize Jest configuration
npx jest --init

# Validate Jest configuration
npx jest --debug-config
```

### 16. **File Operations**
```bash
# Update snapshots
npm test -- --updateSnapshot

# List all test files
npm test -- --listTests

# Find related tests for changed files
npm test -- --findRelatedTests src/pages/donation-head/index.js
```

## Custom Scripts (Add to package.json)

### 17. **Recommended Package.json Scripts**
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "test:donation-head": "jest src/tests/unit/pages/donation-head/",
    "test:donation-admin": "jest src/tests/unit/pages/donation-admin/",
    "test:donation-tenant": "jest src/tests/unit/pages/donation-tenant/",
    "test:verbose": "jest --verbose",
    "test:debug": "jest --verbose --no-cache --runInBand"
  }
}
```

### 18. **Using Custom Scripts**
```bash
# Use custom scripts
npm run test:watch
npm run test:coverage
npm run test:ci
npm run test:donation-head
npm run test:donation-admin
npm run test:donation-tenant
npm run test:verbose
npm run test:debug
```

## Troubleshooting Commands

### 19. **Common Issues**
```bash
# Clear cache and run tests
npm test -- --clearCache && npm test

# Run tests without cache
npm test -- --no-cache

# Run tests in band (no parallel)
npm test -- --runInBand

# Increase timeout for slow tests
npm test -- --testTimeout=30000

# Debug configuration issues
npm test -- --debug-config
```

### 20. **Environment Variables**
```bash
# Set Node environment
NODE_ENV=test npm test

# Increase memory limit
NODE_OPTIONS="--max-old-space-size=4096" npm test

# Enable debug logging
DEBUG=jest* npm test
```

## Examples of Successful Test Runs

### Current Test Results
```bash
$ npm test

Test Suites: 7 passed, 7 total
Tests:       21 passed, 21 total
Snapshots:   0 total
Time:        8.574 s
Ran all test suites.
```

### Coverage Example
```bash
$ npm test -- --coverage

Test Suites: 7 passed, 7 total
Tests:       21 passed, 21 total
Snapshots:   0 total
Time:        9.2 s

Coverage Summary:
- Statements: 100% (21/21)
- Branches: 100% (0/0)
- Functions: 100% (21/21)
- Lines: 100% (21/21)
```

This guide provides comprehensive coverage of all test commands available for the Donation Receipt Frontend Application. All commands have been tested and verified to work with the current test setup.
