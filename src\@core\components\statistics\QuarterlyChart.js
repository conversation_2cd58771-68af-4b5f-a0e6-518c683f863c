import React, { useContext, useEffect, useState } from "react";
import dynamic from "next/dynamic";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";
import { Box, Card } from "@mui/material";
// Dynamically import ApexChart to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const QuarterlyChart = () => {
  const [sampleData, setSampleData] = useState([]);
  const { user } = useContext(AuthContext);

  const router = useRouter();
  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url =
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/admin/service-requisitions-quarterly-report";
    } else {
      url =
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/service-requisitions-quarterly-report";
    }

    let headers;

    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        accept:
          authConfig?.STATISTICS_GET_SERVICE_REQUISITIONS_QUARTERLY_REPORT_ADMIN_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        accept:
          authConfig?.STATISTICS_GET_SERVICE_REQUISITIONS_QUARTERLY_REPORT_EMPLOYEE_V1,
      });
    }

    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const requisitionCount = sampleData?.map((item) => item.requisitionCount);
  const totalBudgetValue = sampleData?.map(
    (item) => item.totalQuarterlyBudgetValue
  );

  const averageBudgetValue = sampleData?.map(
    (item) => item.averageQuarterlyBudgetValue
  );
  const durations = sampleData?.map((item) => {
    return item.quarterDuration
      .split(/\s+/)
      ?.map((word) =>
        isNaN(word) && word?.length > 3 ? word.slice(0, 3) : word
      )
      .join(" ")
      .replace(/ to /i, "-")
      .replace(/ of /i, " ");
  });

  const quarters = ["Q1", "Q2", "Q3", "Q4"];

  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;
    const clickedQuarter = quarters[clickedIndex];

    const query = { quarter: clickedQuarter };

    if (clickedQuarter) {
      if (user?.roleId !== authConfig?.superAdminRoleId) {
        query.assignedInQuarter = true;
      }
      // Navigate to a specific page with the clicked quarter
      router.push({
        pathname: "/service-requisitions",
        query,
      });
    }
  };

  const options = {
    chart: {
      id: "quarterly-bar-chart",
      height: 350,
      events: {
        dataPointSelection: handleBarClick, // Add click event handler
      },
    },
    xaxis: {
      categories: durations,
      title: {
        text: "Quarters",
      },
    },
    yaxis: {
      title: {
        text: "Requisition Amount (in ₹)",
      },
    },
    title: {
      text: "Quarterly SR Budget Insights",
      align: "center",
      style: {
        fontSize: "16px",
        fontWeight: "bold",
      },
    },
    colors: ["#77CE77", "#108A00", "#0D7000"], // Define unique colors for each series
    legend: {
      position: "top",
      horizontalAlign: "center",
    },
    dataLabels: {
      enabled: true,
      offsetY: -15,
      style: {
        fontSize: "12px",
        colors: ["#000"],
      },
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
    plotOptions: {
      bar: {
        columnWidth: "50%",
        dataLabels: {
          position: "top",
        },
      },
    },
  };

  const series = [
    {
      name: "Total Req.",
      data: requisitionCount,
    },
    {
      name: "Total Value (in ₹ Lakhs)",
      data: totalBudgetValue,
    },
    {
      name: "Average Value per Req. (in ₹ Thousands)",
      data: averageBudgetValue,
    },
  ];

  return (
    <Card sx={{ mb: 2, p: 2, borderRadius: 2, boxShadow: 2 }}>
      <Box width="100%" p={3}>
        <ApexChart options={options} series={series} type="bar" height={400} />
      </Box>
    </Card>
  );
};

export default QuarterlyChart;
