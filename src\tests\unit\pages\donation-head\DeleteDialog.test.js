import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import DeleteDialog from '@/pages/donation-head/DeleteDialog.js';
import { renderWithProviders, mockDonationHead, mockApiResponse } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the DeleteDialog component
jest.mock('@/pages/donation-head/DeleteDialog.js', () => {
  return function MockDeleteDialog({ open, onClose, data }) {
    const [loading, setLoading] = React.useState(false);
    const [successMessage, setSuccessMessage] = React.useState('');

    const handleDelete = async () => {
      setLoading(true);
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (data?.isActive) {
          setSuccessMessage(`${data.name} Deactivated successfully.`);
        } else {
          setSuccessMessage(`${data.name} Activated successfully.`);
        }
      } catch (error) {
        console.error('Delete operation failed', error);
      } finally {
        setLoading(false);
        onClose();
      }
    };

    const handleCloseSnackbar = () => {
      setSuccessMessage('');
    };

    if (!open) return null;

    return (
      <>
        <div data-testid="delete-dialog" role="dialog" aria-labelledby="dialog-title">
          <div data-testid="dialog-backdrop" onClick={onClose} />
          
          <div data-testid="dialog-content">
            <h2 id="dialog-title" data-testid="dialog-title">
              Confirm Action
            </h2>
            
            <div data-testid="dialog-message">
              {data?.isActive 
                ? `Are you sure you want to deactivate ${data?.name}?`
                : `Are you sure, you want to activate the ${data?.name}?`
              }
            </div>
            
            <div data-testid="dialog-actions">
              <button
                data-testid="confirm-button"
                onClick={handleDelete}
                disabled={loading}
              >
                {data?.isActive 
                  ? (loading ? 'Deactivating...' : 'Deactivate')
                  : (loading ? 'Activating...' : 'Activate')
                }
              </button>
              
              <button
                data-testid="cancel-button"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

        {successMessage && (
          <div data-testid="success-dialog" role="dialog">
            <div data-testid="success-message">
              {successMessage}
            </div>
            <button
              data-testid="close-success-button"
              onClick={handleCloseSnackbar}
            >
              Close
            </button>
          </div>
        )}
      </>
    );
  };
});

describe('DeleteDialog', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    data: mockDonationHead({
      id: '1',
      name: 'Education Fund',
      isActive: true,
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockAxios.delete.mockResolvedValue(mockApiResponse({ success: true }));
    mockAxios.patch.mockResolvedValue(mockApiResponse({ success: true }));
  });

  describe('Component Rendering', () => {
    it('renders delete dialog when open', () => {
      renderWithProviders(<DeleteDialog {...mockProps} />);

      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      renderWithProviders(<DeleteDialog {...mockProps} open={false} />);

      expect(screen.queryByTestId('delete-dialog')).not.toBeInTheDocument();
    });

    it('displays correct title', () => {
      renderWithProviders(<DeleteDialog {...mockProps} />);

      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      renderWithProviders(<DeleteDialog {...mockProps} />);

      expect(screen.getByTestId('confirm-button')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
    });
  });

  describe('Deactivation Flow', () => {
    it('displays correct message for active item deactivation', () => {
      const activeData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: true,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={activeData} />);

      expect(screen.getByText('Are you sure you want to deactivate Education Fund?')).toBeInTheDocument();
    });

    it('displays correct button text for active item', () => {
      const activeData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: true,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={activeData} />);

      expect(screen.getByText('Deactivate')).toBeInTheDocument();
    });

    it('handles deactivation successfully', async () => {
      const user = userEvent.setup();
      const activeData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: true,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={activeData} />);

      const confirmButton = screen.getByTestId('confirm-button');
      await user.click(confirmButton);

      // Should show loading state
      expect(screen.getByText('Deactivating...')).toBeInTheDocument();

      // Wait for completion
      await waitFor(() => {
        expect(mockProps.onClose).toHaveBeenCalled();
      });
    });

    it('shows success message after deactivation', async () => {
      const user = userEvent.setup();
      const activeData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: true,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={activeData} />);

      await user.click(screen.getByTestId('confirm-button'));

      await waitFor(() => {
        expect(screen.getByText('Education Fund Deactivated successfully.')).toBeInTheDocument();
      });
    });
  });

  describe('Activation Flow', () => {
    it('displays correct message for inactive item activation', () => {
      const inactiveData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: false,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={inactiveData} />);

      expect(screen.getByText('Are you sure, you want to activate the Education Fund?')).toBeInTheDocument();
    });

    it('displays correct button text for inactive item', () => {
      const inactiveData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: false,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={inactiveData} />);

      expect(screen.getByText('Activate')).toBeInTheDocument();
    });

    it('handles activation successfully', async () => {
      const user = userEvent.setup();
      const inactiveData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: false,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={inactiveData} />);

      const confirmButton = screen.getByTestId('confirm-button');
      await user.click(confirmButton);

      // Should show loading state
      expect(screen.getByText('Activating...')).toBeInTheDocument();

      // Wait for completion
      await waitFor(() => {
        expect(mockProps.onClose).toHaveBeenCalled();
      });
    });

    it('shows success message after activation', async () => {
      const user = userEvent.setup();
      const inactiveData = mockDonationHead({
        id: '1',
        name: 'Education Fund',
        isActive: false,
      });

      renderWithProviders(<DeleteDialog {...mockProps} data={inactiveData} />);

      await user.click(screen.getByTestId('confirm-button'));

      await waitFor(() => {
        expect(screen.getByText('Education Fund Activated successfully.')).toBeInTheDocument();
      });
    });
  });

  describe('Dialog Actions', () => {
    it('closes dialog when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      const cancelButton = screen.getByTestId('cancel-button');
      await user.click(cancelButton);

      expect(mockProps.onClose).toHaveBeenCalled();
    });

    it('closes dialog when backdrop is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      const backdrop = screen.getByTestId('dialog-backdrop');
      await user.click(backdrop);

      expect(mockProps.onClose).toHaveBeenCalled();
    });

    it('disables buttons during loading', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      const confirmButton = screen.getByTestId('confirm-button');
      const cancelButton = screen.getByTestId('cancel-button');

      await user.click(confirmButton);

      expect(confirmButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();
    });
  });

  describe('Success Message Dialog', () => {
    it('displays success dialog after successful operation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      await user.click(screen.getByTestId('confirm-button'));

      await waitFor(() => {
        expect(screen.getByTestId('success-dialog')).toBeInTheDocument();
      });
    });

    it('closes success dialog when close button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      await user.click(screen.getByTestId('confirm-button'));

      await waitFor(() => {
        expect(screen.getByTestId('success-dialog')).toBeInTheDocument();
      });

      const closeSuccessButton = screen.getByTestId('close-success-button');
      await user.click(closeSuccessButton);

      expect(screen.queryByTestId('success-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxios.delete.mockRejectedValue(new Error('API Error'));
      
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      await user.click(screen.getByTestId('confirm-button'));

      await waitFor(() => {
        expect(mockProps.onClose).toHaveBeenCalled();
      });

      expect(consoleError).toHaveBeenCalledWith('Delete operation failed', expect.any(Error));
      consoleError.mockRestore();
    });

    it('handles missing data gracefully', () => {
      const propsWithoutData = {
        ...mockProps,
        data: null,
      };

      renderWithProviders(<DeleteDialog {...propsWithoutData} />);

      // Should still render without crashing
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<DeleteDialog {...mockProps} />);

      const dialog = screen.getByTestId('delete-dialog');
      expect(dialog).toHaveAttribute('role', 'dialog');
      expect(dialog).toHaveAttribute('aria-labelledby', 'dialog-title');
    });

    it('has proper dialog title', () => {
      renderWithProviders(<DeleteDialog {...mockProps} />);

      const title = screen.getByTestId('dialog-title');
      expect(title).toHaveAttribute('id', 'dialog-title');
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      // Tab to confirm button
      await user.tab();
      expect(screen.getByTestId('confirm-button')).toHaveFocus();

      // Tab to cancel button
      await user.tab();
      expect(screen.getByTestId('cancel-button')).toHaveFocus();
    });

    it('supports Enter key for confirmation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      const confirmButton = screen.getByTestId('confirm-button');
      confirmButton.focus();
      
      await user.keyboard('{Enter}');

      expect(screen.getByText('Deactivating...')).toBeInTheDocument();
    });

    it('supports Escape key for cancellation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DeleteDialog {...mockProps} />);

      await user.keyboard('{Escape}');

      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });
});
