{"C:\\Users\\<USER>\\Downloads\\Donation-Receipt-Frontend-Application\\src\\tests\\utils\\donationTestUtils.js": {"path": "C:\\Users\\<USER>\\Downloads\\Donation-Receipt-Frontend-Application\\src\\tests\\utils\\donationTestUtils.js", "statementMap": {"0": {"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 32}}, "1": {"start": {"line": 90, "column": 13}, "end": {"line": 90, "column": 28}}, "2": {"start": {"line": 95, "column": 13}, "end": {"line": 95, "column": 29}}, "3": {"start": {"line": 106, "column": 13}, "end": {"line": 106, "column": 23}}, "4": {"start": {"line": 113, "column": 13}, "end": {"line": 113, "column": 28}}, "5": {"start": {"line": 122, "column": 13}, "end": {"line": 122, "column": 26}}, "6": {"start": {"line": 128, "column": 13}, "end": {"line": 128, "column": 34}}, "7": {"start": {"line": 135, "column": 13}, "end": {"line": 135, "column": 28}}, "8": {"start": {"line": 142, "column": 9}, "end": {"line": 142, "column": 24}}, "9": {"start": {"line": 142, "column": 26}, "end": {"line": 142, "column": 41}}, "10": {"start": {"line": 142, "column": 43}, "end": {"line": 142, "column": 53}}, "11": {"start": {"line": 1, "column": 18}, "end": {"line": 1, "column": null}}, "12": {"start": {"line": 2, "column": 23}, "end": {"line": 2, "column": null}}, "13": {"start": {"line": 3, "column": 43}, "end": {"line": 3, "column": null}}, "14": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": null}}, "15": {"start": {"line": 7, "column": 18}, "end": {"line": 17, "column": null}}, "16": {"start": {"line": 20, "column": 24}, "end": {"line": 33, "column": null}}, "17": {"start": {"line": 35, "column": 24}, "end": {"line": 42, "column": null}}, "18": {"start": {"line": 36, "column": 34}, "end": {"line": 36, "column": 38}}, "19": {"start": {"line": 37, "column": 32}, "end": {"line": 37, "column": 36}}, "20": {"start": {"line": 38, "column": 31}, "end": {"line": 38, "column": 35}}, "21": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 33}}, "22": {"start": {"line": 40, "column": 36}, "end": {"line": 40, "column": 40}}, "23": {"start": {"line": 45, "column": 19}, "end": {"line": 58, "column": null}}, "24": {"start": {"line": 61, "column": 20}, "end": {"line": 68, "column": null}}, "25": {"start": {"line": 71, "column": 35}, "end": {"line": 87, "column": null}}, "26": {"start": {"line": 72, "column": 67}, "end": {"line": 72, "column": null}}, "27": {"start": {"line": 83, "column": 2}, "end": {"line": 86, "column": null}}, "28": {"start": {"line": 90, "column": 31}, "end": {"line": 92, "column": null}}, "29": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": null}}, "30": {"start": {"line": 95, "column": 32}, "end": {"line": 104, "column": null}}, "31": {"start": {"line": 95, "column": 53}, "end": {"line": 104, "column": null}}, "32": {"start": {"line": 106, "column": 26}, "end": {"line": 111, "column": null}}, "33": {"start": {"line": 106, "column": 47}, "end": {"line": 111, "column": null}}, "34": {"start": {"line": 113, "column": 31}, "end": {"line": 119, "column": null}}, "35": {"start": {"line": 113, "column": 56}, "end": {"line": 119, "column": null}}, "36": {"start": {"line": 122, "column": 29}, "end": {"line": 125, "column": null}}, "37": {"start": {"line": 128, "column": 37}, "end": {"line": 132, "column": null}}, "38": {"start": {"line": 135, "column": 31}, "end": {"line": 139, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 71, "column": 32}, "end": {"line": 71, "column": 35}}, "loc": {"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 32}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 90, "column": 28}, "end": {"line": 90, "column": 31}}, "loc": {"start": {"line": 90, "column": 13}, "end": {"line": 90, "column": 28}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 95, "column": 29}, "end": {"line": 95, "column": 32}}, "loc": {"start": {"line": 95, "column": 13}, "end": {"line": 95, "column": 29}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 106, "column": 23}, "end": {"line": 106, "column": 26}}, "loc": {"start": {"line": 106, "column": 13}, "end": {"line": 106, "column": 23}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 113, "column": 28}, "end": {"line": 113, "column": 31}}, "loc": {"start": {"line": 113, "column": 13}, "end": {"line": 113, "column": 28}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 122, "column": 26}, "end": {"line": 122, "column": 29}}, "loc": {"start": {"line": 122, "column": 13}, "end": {"line": 122, "column": 26}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 128, "column": 34}, "end": {"line": 128, "column": 37}}, "loc": {"start": {"line": 128, "column": 13}, "end": {"line": 128, "column": 34}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 135, "column": 28}, "end": {"line": 135, "column": 31}}, "loc": {"start": {"line": 135, "column": 13}, "end": {"line": 135, "column": 28}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 142, "column": 24}, "end": {"line": 142, "column": 26}}, "loc": {"start": {"line": 142, "column": 9}, "end": {"line": 142, "column": 24}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 142, "column": 41}, "end": {"line": 142, "column": 43}}, "loc": {"start": {"line": 142, "column": 26}, "end": {"line": 142, "column": 41}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 142, "column": 53}, "end": {"line": 142, "column": null}}, "loc": {"start": {"line": 142, "column": 43}, "end": {"line": 142, "column": 53}}}, "11": {"name": "(anonymous_13)", "decl": {"start": {"line": 36, "column": 28}, "end": {"line": 36, "column": 34}}, "loc": {"start": {"line": 36, "column": 34}, "end": {"line": 36, "column": 38}}}, "12": {"name": "(anonymous_14)", "decl": {"start": {"line": 37, "column": 26}, "end": {"line": 37, "column": 32}}, "loc": {"start": {"line": 37, "column": 32}, "end": {"line": 37, "column": 36}}}, "13": {"name": "(anonymous_15)", "decl": {"start": {"line": 38, "column": 25}, "end": {"line": 38, "column": 31}}, "loc": {"start": {"line": 38, "column": 31}, "end": {"line": 38, "column": 35}}}, "14": {"name": "(anonymous_16)", "decl": {"start": {"line": 39, "column": 23}, "end": {"line": 39, "column": 29}}, "loc": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 33}}}, "15": {"name": "(anonymous_17)", "decl": {"start": {"line": 40, "column": 30}, "end": {"line": 40, "column": 36}}, "loc": {"start": {"line": 40, "column": 36}, "end": {"line": 40, "column": 40}}}, "16": {"name": "(anonymous_18)", "decl": {"start": {"line": 61, "column": 20}, "end": {"line": 61, "column": 21}}, "loc": {"start": {"line": 61, "column": 110}, "end": {"line": 68, "column": null}}}, "17": {"name": "(anonymous_19)", "decl": {"start": {"line": 71, "column": 35}, "end": {"line": 71, "column": 36}}, "loc": {"start": {"line": 71, "column": 64}, "end": {"line": 87, "column": null}}}, "18": {"name": "(anonymous_21)", "decl": {"start": {"line": 90, "column": 31}, "end": {"line": 90, "column": 32}}, "loc": {"start": {"line": 90, "column": 46}, "end": {"line": 92, "column": null}}}, "19": {"name": "(anonymous_22)", "decl": {"start": {"line": 95, "column": 32}, "end": {"line": 95, "column": 33}}, "loc": {"start": {"line": 95, "column": 53}, "end": {"line": 104, "column": null}}}, "20": {"name": "(anonymous_23)", "decl": {"start": {"line": 106, "column": 26}, "end": {"line": 106, "column": 27}}, "loc": {"start": {"line": 106, "column": 47}, "end": {"line": 111, "column": null}}}, "21": {"name": "(anonymous_24)", "decl": {"start": {"line": 113, "column": 31}, "end": {"line": 113, "column": 32}}, "loc": {"start": {"line": 113, "column": 56}, "end": {"line": 119, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 61, "column": 33}, "end": {"line": 61, "column": 67}}, "type": "default-arg", "locations": [{"start": {"line": 61, "column": 52}, "end": {"line": 61, "column": 67}}]}, "1": {"loc": {"start": {"line": 61, "column": 69}, "end": {"line": 61, "column": 103}}, "type": "default-arg", "locations": [{"start": {"line": 61, "column": 88}, "end": {"line": 61, "column": 103}}]}, "2": {"loc": {"start": {"line": 71, "column": 47}, "end": {"line": 71, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 71, "column": 57}, "end": {"line": 71, "column": 59}}]}, "3": {"loc": {"start": {"line": 95, "column": 33}, "end": {"line": 95, "column": 47}}, "type": "default-arg", "locations": [{"start": {"line": 95, "column": 45}, "end": {"line": 95, "column": 47}}]}, "4": {"loc": {"start": {"line": 106, "column": 27}, "end": {"line": 106, "column": 41}}, "type": "default-arg", "locations": [{"start": {"line": 106, "column": 39}, "end": {"line": 106, "column": 41}}]}, "5": {"loc": {"start": {"line": 113, "column": 38}, "end": {"line": 113, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 113, "column": 47}, "end": {"line": 113, "column": 50}}]}}, "s": {"0": 59, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 6, "12": 6, "13": 6, "14": 6, "15": 6, "16": 6, "17": 6, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 6, "24": 6, "25": 6, "26": 59, "27": 59, "28": 6, "29": 0, "30": 6, "31": 18, "32": 6, "33": 18, "34": 6, "35": 0, "36": 6, "37": 6, "38": 6}, "f": {"0": 59, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 59, "17": 59, "18": 0, "19": 18, "20": 18, "21": 0}, "b": {"0": [59], "1": [59], "2": [59], "3": [0], "4": [0], "5": [0]}}}