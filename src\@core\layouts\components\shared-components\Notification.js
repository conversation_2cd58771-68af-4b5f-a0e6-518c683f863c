import React, { useState, useEffect } from "react";
import { Box, Popper } from "@mui/material";
import NotificationIcon from "src/@core/components/custom-components/NotificationIcon";
import NotificationDropdown from "src/@core/components/custom-components/NotificationDropdown";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { styled } from "@mui/material/styles";

const IconContainer = styled(Box)(({ theme }) => ({
  height: "100%", // Ensure the container fills the height of its parent
  width: "100%", // Ensure the container fills the width of its parent
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  "&:hover, &:active": {
    // Adjust these styles as needed for the hover and active states
    backgroundColor: theme.palette.action.hover,
    // For example, to make the icon itself scale, you might adjust its font size or transform scale here.
    "& .icon": {
      // Assuming .icon can be targeted within <NotificationIcon>
      transform: "scale(1.1)", // Example: scale up the icon
    },
  },
}));

const Notification = () => {
  const [notifications, setNotifications] = useState([]);

  const fetchNotifications = () => {
    axios({
      method: "GET",
      url: getUrl(authConfig.notificationV2Endpoint),
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setNotifications(res.data?.notifications);
      })
      .catch((err) => console.log("Notifications error", err));
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const [openDrawer, setOpenDrawer] = useState(false);

  const [notificationContent, setNotificationContent] = useState("");
  const [notificationData, setNotificationData] = useState(null);


  const [createdOnDate, setCreatedOnDate] = useState("");

  const unreadNotifications = notifications.filter((n) => !n.isRead).length;

  const handleToggle = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleDrawerClose = () => {
    setOpenDrawer(false);
  };

  const markAsRead = (id) => {
    setOpenDrawer(true);
   
    const matchedNotificationRecord = notifications.find(
      (notification) => notification.notificationId === id
    );

    setNotificationData(matchedNotificationRecord);

    // if (notificationToMarkAsRead && !notificationToMarkAsRead.isRead) {
    axios({
      method: "patch",
      url: getUrl(authConfig.notificationV2Endpoint),
      data: {
        notifications: [id],
      },
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        if (res.status === 200) {
          setNotifications((prevNotifications) =>
            prevNotifications.map((notification) =>
              notification.notificationId === id
                ? { ...notification, isRead: !notification?.isRead }
                : notification
            )
          );
        }
      })
      .catch((err) => console.log("Mark as Read error", err));
    // } else {
    //   console.log("Notification is already marked as read ");
    // }
  };
  const markAsReadV2 = (id) => {


    axios({
      method: "patch",
      url: getUrl(authConfig.notificationV2Endpoint),
      data: {
        notifications: [id],
      },
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        if (res.status === 200) {
          setNotifications((prevNotifications) =>
            prevNotifications.map((notification) =>
              notification.notificationId === id
                ? { ...notification, isRead: !notification?.isRead }
                : notification
            )
          );
        }
      })
      .catch((err) => console.log("Mark as Read error", err));
    
  };

  const deleteNotification = (id) => {
    axios({
      method: "DELETE",
      url: getUrl(authConfig.notificationV2Endpoint),
      data: {
        notifications: [id],
      },
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setNotifications((prevNotifications) =>
          prevNotifications.filter((notification) => notification.id !== id)
        );
        fetchNotifications();
      })
      .catch((err) => console.log("Delete Notification error", err));
  };

  return (
    <>
      <header>
        <IconContainer>
          <NotificationIcon
            unreadCount={unreadNotifications}
            onClick={handleToggle}
          />
        </IconContainer>
      </header>

      <Popper open={open} anchorEl={anchorEl} placement="bottom-start">
        <NotificationDropdown
          open={open}
          openDrawer={openDrawer}
          notificationData={notificationData}
          notifications={notifications}
          setNotifications={setNotifications}
          fetchNotifications={fetchNotifications}
          notificationContent={notificationContent}
          createdOnDate={createdOnDate}
          markAsRead={markAsRead}
          deleteNotification={deleteNotification}
          closeDropdown={handleClose}
          handleDrawerClose={handleDrawerClose}
          markAsReadV2={markAsReadV2}
        />
      </Popper>
    </>
  );
};

export default Notification;
