# Donation Receipt Frontend - Test Suite Documentation

## Overview

This comprehensive test suite provides complete coverage for the Donation Receipt Frontend Application, specifically focusing on the three main donation modules:

- **donation-head**: Donation head management functionality
- **donation-admin**: Administrative dashboard and analytics
- **donation-tenant**: Tenant-specific dashboard and operations

## Test Architecture

### Test Structure

```
src/tests/
├── unit/                    # Unit tests for individual components
│   └── pages/
│       ├── donation-head/   # Unit tests for donation head components
│       ├── donation-admin/  # Unit tests for admin dashboard
│       └── donation-tenant/ # Unit tests for tenant dashboard
├── integration/             # Integration tests for user workflows
│   └── pages/
│       ├── donation-head/   # End-to-end donation head workflows
│       ├── donation-admin/  # Admin dashboard integration tests
│       └── donation-tenant/ # Tenant dashboard integration tests
├── api/                     # API endpoint tests
│   ├── donation-head/       # Donation head API tests
│   ├── donation-admin/      # Admin dashboard API tests
│   └── donation-tenant/     # Tenant dashboard API tests
├── utils/                   # Shared test utilities
│   └── donationTestUtils.js # Common test helpers and providers
├── mocks/                   # Mock implementations
│   └── donationMocks.js     # Mock data and API responses
└── runAllTests.js          # Comprehensive test runner script
```

### Testing Frameworks and Libraries

- **Jest**: Primary testing framework
- **React Testing Library**: Component testing utilities
- **@testing-library/user-event**: User interaction simulation
- **@testing-library/jest-dom**: Additional Jest matchers

## Test Categories

### 1. Unit Tests

**Purpose**: Test individual components, functions, and utilities in isolation.

**Coverage**:
- Component rendering and props handling
- User interactions (clicks, form inputs, navigation)
- State management and lifecycle methods
- Error handling and edge cases
- Accessibility features

**Example Test Files**:
- `donation-head/index.test.js` - Main donation head page component
- `donation-head/DonationHeadDialog.test.js` - Create/edit dialog component
- `donation-head/AdvancedSearch.test.js` - Advanced search functionality
- `donation-head/Columns.test.js` - Data grid column configuration
- `donation-head/DeleteDialog.test.js` - Delete confirmation dialog

### 2. Integration Tests

**Purpose**: Test component interactions, user workflows, and feature integration.

**Coverage**:
- Complete user workflows (create, read, update, delete)
- Multi-component interactions
- Data flow between components
- Form submission and validation
- Chart interactions and data visualization

**Example Test Files**:
- `donation-head/DonationHeadWorkflow.test.js` - Complete CRUD workflow
- `donation-admin/DonationAdminDashboard.test.js` - Dashboard interactions
- `donation-tenant/DonationTenantDashboard.test.js` - Tenant dashboard workflow

### 3. API Tests

**Purpose**: Test backend service calls, API endpoints, and data handling.

**Coverage**:
- HTTP request/response handling
- Error scenarios (network errors, server errors, validation errors)
- Data transformation and formatting
- Authentication and authorization
- Request headers and configuration

**Example Test Files**:
- `donation-head/donationHeadApi.test.js` - Donation head API endpoints
- `donation-admin/donationAdminApi.test.js` - Admin dashboard APIs
- `donation-tenant/donationTenantApi.test.js` - Tenant dashboard APIs

## Test Utilities and Mocks

### donationTestUtils.js

Provides shared utilities for consistent testing:

- **renderWithProviders**: Custom render function with theme and context providers
- **Mock data generators**: Functions to create test data objects
- **Test helpers**: Common testing utilities and assertions
- **Mock components**: Simplified versions of complex UI components

### donationMocks.js

Comprehensive mocking setup:

- **API mocking**: Mock axios and HTTP requests
- **Component mocking**: Mock complex third-party components
- **Context mocking**: Mock authentication and RBAC contexts
- **Utility mocking**: Mock helper functions and configurations

## Running Tests

### Prerequisites

Ensure all dependencies are installed:

```bash
npm install
```

### Running All Tests

```bash
# Run the comprehensive test suite
npm run test:all

# Or run the test runner script directly
node src/tests/runAllTests.js
```

### Running Specific Test Categories

```bash
# Unit tests only
npm run test:unit
node src/tests/runAllTests.js --unit

# Integration tests only
npm run test:integration
node src/tests/runAllTests.js --integration

# API tests only
npm run test:api
node src/tests/runAllTests.js --api
```

### Running Tests for Specific Modules

```bash
# Donation head tests
npm test -- --testPathPattern="donation-head"

# Donation admin tests
npm test -- --testPathPattern="donation-admin"

# Donation tenant tests
npm test -- --testPathPattern="donation-tenant"
```

### Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# View coverage in browser
npm run test:coverage:open
```

## Test Patterns and Best Practices

### 1. Test Organization

- **Descriptive test names**: Use clear, descriptive names for test suites and individual tests
- **Logical grouping**: Group related tests using `describe` blocks
- **Setup and teardown**: Use `beforeEach` and `afterEach` for consistent test setup

### 2. Component Testing

```javascript
describe('ComponentName', () => {
  describe('Component Rendering', () => {
    it('renders with required props', () => {
      // Test basic rendering
    });
  });

  describe('User Interactions', () => {
    it('handles user input correctly', () => {
      // Test user interactions
    });
  });

  describe('Error Handling', () => {
    it('displays error states appropriately', () => {
      // Test error scenarios
    });
  });
});
```

### 3. Accessibility Testing

- Test keyboard navigation
- Verify ARIA attributes
- Check screen reader compatibility
- Validate semantic HTML structure

### 4. Mock Strategy

- Mock external dependencies (APIs, third-party libraries)
- Use realistic mock data
- Test both success and error scenarios
- Maintain mock consistency across tests

## Coverage Targets

### Minimum Coverage Requirements

- **Statements**: 80%
- **Branches**: 75%
- **Functions**: 80%
- **Lines**: 80%

### Priority Areas

1. **Critical user workflows**: 95%+ coverage
2. **Data handling and validation**: 90%+ coverage
3. **Error handling**: 85%+ coverage
4. **UI components**: 80%+ coverage

## Continuous Integration

### Pre-commit Hooks

Tests are automatically run before commits to ensure code quality:

```bash
# Install pre-commit hooks
npm run prepare

# Manual pre-commit check
npm run pre-commit
```

### CI/CD Pipeline

Tests are integrated into the CI/CD pipeline:

1. **Lint and format check**
2. **Unit tests execution**
3. **Integration tests execution**
4. **API tests execution**
5. **Coverage validation**
6. **Test report generation**

## Troubleshooting

### Common Issues

1. **Mock not working**: Ensure mocks are properly configured in `setupTests.js`
2. **Async test failures**: Use `waitFor` for asynchronous operations
3. **Component not rendering**: Check if all required providers are included
4. **API test failures**: Verify mock axios configuration

### Debug Mode

```bash
# Run tests in debug mode
npm test -- --verbose --detectOpenHandles

# Run specific test file in debug mode
npm test -- --testPathPattern="specific-test.test.js" --verbose
```

### Test Environment

Tests run in a Node.js environment with jsdom for DOM simulation. The environment is configured in:

- `jest.config.js`: Jest configuration
- `src/setupTests.js`: Test environment setup
- `src/tests/utils/`: Test utilities and helpers

## Contributing

### Adding New Tests

1. Follow the established directory structure
2. Use the provided test utilities and mocks
3. Include unit, integration, and API tests for new features
4. Maintain coverage targets
5. Update documentation as needed

### Test Review Checklist

- [ ] Tests follow naming conventions
- [ ] All test categories are covered (unit, integration, API)
- [ ] Mocks are properly configured
- [ ] Accessibility is tested
- [ ] Error scenarios are covered
- [ ] Coverage targets are met
- [ ] Tests are deterministic and reliable

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Accessibility Testing](https://web.dev/accessibility-testing/)
