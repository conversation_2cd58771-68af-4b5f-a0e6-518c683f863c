// ** React Imports
import { useContext, useEffect, useState } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import useMediaQuery from '@mui/material/useMediaQuery'

// ** Redux Imports
import { useDispatch, useSelector } from 'react-redux'

// ** Hooks
import { useSettings } from 'src/@core/hooks/useSettings'

// ** FullCalendar & App Components Imports

import CalendarWrapper from 'src/@core/styles/libs/fullcalendar'


// ** Actions
import {
  addEvent,
  fetchEvents,
  fetchEventsAll,
  deleteEvent,
  updateEvent,
  handleSelectEvent,
  fetchEventById,
  fetchEventsAdmin,
  fetchEventsAdminAll
} from 'src/store/calendar'
import SidebarLeft from 'src/views/calendar/SidebarLeft'
import Calendar from 'src/views/calendar/Calendar'

import AddEventRightBar from 'src/views/calendar/AddEventRightBar'
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { AuthContext } from 'src/context/AuthContext'
import authConfig from "src/configs/auth";
import { getUrl } from 'src/helpers/utils'

// ** CalendarColors
const calendarsColor = {
  Personal: 'error',
  Business: 'primary',
  Family: 'warning',
  Holiday: 'success',
  ETC: 'info'
}

const AppCalendar = () => {
  // ** States
  const [calendarApi, setCalendarApi] = useState(null)
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(false)
  const [addEventSidebarOpen, setAddEventSidebarOpen] = useState(false)

  const { canMenuPage, rbacRoles,canMenuPageSection } = useRBAC();

  const { user } = useContext(AuthContext)

  const router = useRouter()

  // ** Hooks
  const { settings } = useSettings()
  const dispatch = useDispatch()
  const store = useSelector(state => state.calendar)

  const { fromDate,toDate,employee } = router.query;

  // ** Vars
  const leftSidebarWidth = 300
  const addEventSidebarWidth = 400
  const { skin, direction } = settings
  const mdAbove = useMediaQuery(theme => theme.breakpoints.up('md'))

  useEffect(() => {
    if(fromDate && toDate){
      if (user?.roleId === authConfig?.superAdminRoleId || user?.roleId === authConfig?.htAdminRoleId ) {
        dispatch(fetchEventsAdminAll({ fromDate, toDate, employee }))
      } else {
        dispatch(fetchEventsAll({ fromDate, toDate, employee }))
      }
    }
    else{
    if (user?.roleId === authConfig?.superAdminRoleId || user?.roleId === authConfig?.htAdminRoleId ) {
      dispatch(fetchEventsAdmin())
    } else {
      dispatch(fetchEvents())
    }}
      
  }, [dispatch])

  

  
  const handleLeftSidebarToggle = () => setLeftSidebarOpen(!leftSidebarOpen)
  const handleAddEventSidebarToggle = () => setAddEventSidebarOpen(!addEventSidebarOpen)

  const canAccessAppointments = (requiredPermission) =>{
    if(user?.organisationCategory === "EMPLOYEE"){
      return canMenuPageSection(MENUS.LEFT, PAGES.TOOLS, PAGES.APPOINTMENTS , requiredPermission);
    }
    else{
      return canMenuPage(MENUS.LEFT, PAGES.APPOINTMENTS, requiredPermission);
    }
  }

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessAppointments(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);


  if(canAccessAppointments(PERMISSIONS.READ)){
  return (
    <CalendarWrapper
      className='app-calendar'
      sx={{
        paddingBottom:'0px !important',
        boxShadow: skin === 'bordered' ? 0 : 6,
        ...(skin === 'bordered' && { border: theme => `1px solid ${theme.palette.divider}` })
      }}
    >
      <SidebarLeft
        store={store}
        mdAbove={mdAbove}
        dispatch={dispatch}
        calendarApi={calendarApi}
        calendarsColor={calendarsColor}
        leftSidebarOpen={leftSidebarOpen}
        leftSidebarWidth={leftSidebarWidth}
        handleSelectEvent={handleSelectEvent}
        handleLeftSidebarToggle={handleLeftSidebarToggle}
       handleAddEventSidebarToggle={handleAddEventSidebarToggle}
      />
      <Box
        sx={{
          padding:{xs:'10px 10px 0px 10px', sm:'1rem 1rem 0 1rem'},
          pb: '0 !important',
          flexGrow: 1,
          borderRadius: 1,
          boxShadow: 'none',
          backgroundColor: 'background.paper',
          ...(mdAbove ? { borderTopLeftRadius: 0, borderBottomLeftRadius: 0 } : {})
        }}
      >
        <Calendar
          store={store}
          dispatch={dispatch}
          direction={direction}
          updateEvent={updateEvent}
          calendarApi={calendarApi}
          calendarsColor={calendarsColor}
          setCalendarApi={setCalendarApi}
          handleSelectEvent={handleSelectEvent}
          handleLeftSidebarToggle={handleLeftSidebarToggle}
          handleAddEventSidebarToggle={handleAddEventSidebarToggle}
         
        />
      </Box>
      <AddEventRightBar
        store={store}
        dispatch={dispatch}
        addEvent={addEvent}
        updateEvent={updateEvent}
        deleteEvent={deleteEvent}
        calendarApi={calendarApi}
        drawerWidth={addEventSidebarWidth}
        handleSelectEvent={handleSelectEvent}
        addEventSidebarOpen={addEventSidebarOpen}
        handleAddEventSidebarToggle={handleAddEventSidebarToggle}
        fetchEventById={fetchEventById}
      />
    </CalendarWrapper>
  );}
  else{
    return null;
  }
}

export default AppCalendar
