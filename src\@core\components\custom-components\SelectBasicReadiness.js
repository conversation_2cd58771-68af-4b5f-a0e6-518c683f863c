import Box from '@mui/material/Box'
import MenuItem from '@mui/material/MenuItem'
import InputLabel from '@mui/material/InputLabel'
import FormControl from '@mui/material/FormControl'
import Select from '@mui/material/Select'
import FormHelperText from '@mui/material/FormHelperText'

const SelectBasicReadiness = (props) => {
  const { id, label, nameArray, onChange,defaultValue } = props;

  const handleChange = (event) => {
    if (onChange) {
      onChange(event.target.value);
    }
  };

  return (
    <Box sx={{ '& > *': { mt: 6, mr: 6 } }}>
      <FormControl sx={{ maxWidth: '100%', minWidth: '100%' }}>
        <InputLabel id={id + '-label'}>{label}</InputLabel>
        <Select
          label={label}
          defaultValue={defaultValue}
          id={id}
          labelId={id + '-label'}
          onChange={handleChange}
        >
          {nameArray.map((name) => (
            <MenuItem key={name} value={name}>
              {name}
            </MenuItem>
          ))}
        </Select>
        <FormHelperText>Select an option</FormHelperText>
      </FormControl>
    </Box>
  );
};

export default SelectBasicReadiness;
