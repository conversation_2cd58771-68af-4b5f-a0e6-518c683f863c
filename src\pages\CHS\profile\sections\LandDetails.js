// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section1 from 'src/pages/SP/broker/sections/Section1'
import { useTheme } from '@emotion/react'

// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import PageHeader from 'src/@core/components/page-header'
import Section2 from './Section2'
import MUITableCell from "src/pages/SP/MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const LandDetails = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
    const theme = useTheme()

    const [state2, setState2] = useState(true)

    const handleState2 = () => {
      setState2(!state2)
    }
    
    // Pre-Populating code Start
    // const [landDetails, setLandDetails] = useState({
    //     grossPlotArea:"",
    //     builtUpAreaResidential:"",
    //     builtUpAreaCommercial:"",
    //     noOfResidence:"",
    //     noOfCommercial:""
    //   });

    return (
        <>
        {/* {can('society_landDetails_READ') && */}
       <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'Land Details'}
                body={
                  <>
                    {state2 && (
                      
                          <TableContainer sx={{ padding:'4px 6px', cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer'  }}
                            className='tableBody'
                            //onClick={can('society_landDetails_UPDATE') ? handleState2 : null}>
                            onClick={ handleState2}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <PageHeader
                                    title={<Typography variant='body1'><strong>Total Plot Area:</strong>
                                    </Typography>}
                                    subtitle={<Typography variant='body2'></Typography>}
                                  />
                                </TableRow>
                                


                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Built-up area a Residential(Sq Mtrs):</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.builtUpAreaResidential}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field} >Built-up area Commercial(Sq Mtrs):</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.builtUpAreaCommercial}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <PageHeader
                                  title={<Typography variant='body1' sx={{ mt: 6 ,mb:1 }}><strong>No.of Residents/Commercial:</strong>
                                  </Typography>}
                                  subtitle={<Typography variant='body2'></Typography>}
                                />

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field} >No.of Residents:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.noOfResidence}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>No.of Commercial:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.noOfCommercial}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        

                    )}
                    {!state2 && <Section2 formData={data} onCancel={handleState2} />}
                  </>
                }
                expanded={expanded}
              /> 
        {/* }     */}
        </>
    );

}
export default LandDetails;