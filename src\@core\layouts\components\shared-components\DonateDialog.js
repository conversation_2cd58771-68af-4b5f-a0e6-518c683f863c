import { useContext, useEffect, useState } from "react";

import {
    <PERSON>,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    TextField
} from "@mui/material";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

const RazorpayKey = "rzp_test_zKgAOeC4Kqzl31";

const loadRazorpayScript = () =>
  new Promise((resolve) => {
    if (window.Razorpay) {
      resolve(true);
      return;
    }
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });

const DonateDialog = ({ open, onClose }) => {
  const [tenantId, setTenantId] = useState("");
  const [tenantsList, setTenantsList] = useState([]);
  const { user } = useContext(AuthContext);

  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm();
  useEffect(() => {
    // Fetch all tenants
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setTenantsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const openRazorpay = async (data) => {
    const res = await loadRazorpayScript();
    if (!res) {
      alert("Razorpay SDK failed to load. Are you online?");
      return;
    }
    const amountInPaise = Number(data.amount) * 100;
    const options = {
      key: RazorpayKey,
      amount: amountInPaise,
      currency: "INR",
      name: "Pure Heart Donation",
      description: `Donation from ${user?.name}`,
      prefill: {
        name: user?.name,
        email: user?.email,
      },
      handler: function (response) {
        const generatedReceipt = {
          ...data,
          donorName: user?.name,
          paymentModeLabel: "Online",
          receiptId: Math.floor(100000 + Math.random() * 900000),
          date: new Date().toLocaleString(),
          paymentId: response.razorpay_payment_id,
        };
      },
      modal: {
        ondismiss: function () {
          //   setLoading(false);
        },
      },
    };
    const paymentObject = new window.Razorpay(options);
    paymentObject.open();
  };

  const onSubmit = async (data) => {
    openRazorpay(data);
  };

  const handleClose = () => {
    setTenantId("")
    setValue("amount","")
    setValue("remarks","")
    onClose();
  };

  return (
    <Dialog maxWidth="xs" scroll="paper" open={open}>
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4.5)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start" },

          height: "50px",
        }}
        textAlign={"center"}
      >
        <Box
          sx={{
            fontSize: {
              xs: 14, // Smaller font size for mobile
              sm: 15, // Slightly larger font size
              md: 17, // Default font size for larger screens
              lg: 16,
            },
            fontWeight: 600, // Bold text if needed
          }}
        >
            Donate
        </Box>

        {/* {"Creating User as Service Provider"} */}
        <Box sx={{ position: "absolute", top: "9px", right: "10px" }}>
           <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                },
              }}
            >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          position: "relative",
          p: (theme) => `${theme.spacing(4, 4)} !important`,
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <FormControl fullWidth error={Boolean(errors.tenantName)}>
              <Controller
                name="tenantName"
                control={control}
                rules={{ required: "NGO Name is required" }}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="tenantName"
                    label="Select NGO"
                    nameArray={tenantsList}
                    value={tenantId}
                    onChange={(event) => {
                      field.onChange(event.target?.value);
                      setTenantId(event.target?.value);
                    }}
                  />
                )}
              />
              {errors.tenantName && (
                <FormHelperText sx={{ color: "error.main" }}>
                  {errors.tenantName.message}
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="amount"
                control={control}
                rules={{ required: "Amount is required" }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="number"
                    size="small"
                    label="Amount"
                    placeholder="Enter your Amount"
                    error={Boolean(errors.amount)}
                    helperText={errors.amount?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="remarks"
                control={control}
                rules={{ required: false }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    rows={4}
                    multiline
                    label="remarks"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.remarks)}
                    helperText={errors.remarks?.message}
                    aria-describedby="Section2_remarks"
                  />
                )}
              />
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(2.5)} !important`,
          height: "50px", // Set fixed height for footer
        }}
      >
        <Grid item xs={12}>
          <center>
            <Button
              id="cancelButton"
              size="medium"
              sx={{ mr: 3 }}
              variant="outlined"
              color="primary"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              id="saveButton"
              size="medium"
              type="button"
              variant="contained"
              onClick={handleSubmit(onSubmit)}
              sx={{
                mr: {
                  xs: 2.2,
                  sm: 2.2,
                  md: 2.2,
                  lg: 2.2,
                  xl: 2.2,
                },
              }}
            >
              Pay & Generate Receipt
            </Button>
          </center>
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

export default DonateDialog;
