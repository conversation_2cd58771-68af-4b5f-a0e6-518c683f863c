import React from 'react';

// Mock Next.js router
export const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  pathname: '/test',
  query: {},
  asPath: '/test',
  route: '/test',
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
};

// Mock axios
export const mockAxios = {
  get: jest.fn(() => Promise.resolve({ data: [] })),
  post: jest.fn(() => Promise.resolve({ data: { donationHeads: [], totalCount: 0 } })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  patch: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  create: jest.fn(() => mockAxios),
};

// Mock ApexCharts
export const MockChart = ({ options, series, type, height, ...props }) => {
  return React.createElement('div', {
    'data-testid': 'mock-chart',
    'data-type': type,
    'data-height': height,
    'data-series': JSON.stringify(series),
    'data-options': JSON.stringify(options),
    ...props
  }, `Mock Chart (${type})`);
};

// Mock Material-UI DataGrid
export const MockDataGrid = ({ rows, columns, ...props }) => {
  return React.createElement('div', {
    'data-testid': 'mock-data-grid',
    'data-rows': JSON.stringify(rows),
    'data-columns': JSON.stringify(columns),
    ...props
  }, 'Mock DataGrid');
};

// Mock Fallback Spinner
export const MockFallbackSpinner = () => {
  return React.createElement('div', {
    'data-testid': 'fallback-spinner'
  }, 'Loading...');
};

// Setup all mocks function
export const setupAllMocks = () => {
  // Mock Next.js router
  jest.mock('next/router', () => ({
    useRouter: () => mockRouter,
  }));

  // Mock Next.js dynamic imports
  jest.mock('next/dynamic', () => {
    return (fn) => {
      const Component = fn();
      return Component || MockChart;
    };
  });

  // Mock axios
  jest.mock('axios', () => mockAxios);

  // Mock react-apexcharts
  jest.mock('react-apexcharts', () => MockChart);

  // Mock Material-UI DataGrid
  jest.mock('@mui/x-data-grid', () => ({
    DataGrid: MockDataGrid,
  }));

  // Mock Fallback Spinner
  jest.mock('src/@core/components/spinner', () => MockFallbackSpinner);

  // Mock auth config
  jest.mock('src/configs/auth', () => ({
    donationHeadEndpoint: '/api/donation-heads',
    listValuesAll: '/api/list-values',
    storageTokenKeyName: 'accessToken',
  }));

  // Mock constants
  jest.mock('src/constants', () => ({
    MENUS: { DONATION_HEAD: 'donation-head' },
    PAGES: { DONATION_HEAD: 'donation-head' },
    PERMISSIONS: { FULL_ACCESS: 'FULL_ACCESS' },
  }));

  // Mock helper utils
  jest.mock('src/helpers/utils', () => ({
    getAuthorizationHeaders: jest.fn(() => ({ Authorization: 'Bearer test-token' })),
    getUrl: jest.fn((endpoint) => `http://localhost:8080${endpoint}`),
  }));

  return { mockAxios, mockRouter };
};
