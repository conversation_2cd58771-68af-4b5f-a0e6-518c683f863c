import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const ProductivityTab = () => {
  const state = {
    series: [
      {
        name: 'Service Providers',
        data: [12, 15, 20, 25, 30, 35, 40, 45],  // Task-related data (Service Providers)
      },
      {
        name: 'Service Requests',
        data: [10, 20, 18, 30, 25, 28, 35, 40],  // Notification-related data (Service Requests)
      },
      {
        name: 'Satisfaction Rating',
        data: [3.5, 4.0, 4.2, 4.5, 4.3, 4.6, 4.7, 4.8],  // Satisfaction rating over time
      },
    ],
    options: {
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,  // Stack the bars to show multiple series on the same axis
      },
      plotOptions: {
        bar: {
          horizontal: true,  // Make the bars horizontal
          dataLabels: {
            total: {
              enabled: true,  // Display total on bars
              offsetX: 0,
              style: {
                fontSize: '13px',
                fontWeight: 900,
              },
            },
          },
        },
      },
      stroke: {
        width: 1,
        colors: ['#fff'],  // White stroke to separate bars
      },
      title: {
        text: 'Productivity Metrics Overview',
        align: 'left',
      },
      xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],  // Categories (months)
        labels: {
          formatter: function (val) {
            return val;  // Just display the month names
          },
        },
      },
      yaxis: {
        title: {
          text: 'Number of Service Providers',
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val;  // Display data in the tooltip
          },
        },
        custom: function ({ seriesIndex, dataPointIndex, w }) {
          let info = '';
          if (seriesIndex === 0) {
            info = `Service Providers: ${w.globals.series[seriesIndex][dataPointIndex]}`;
          } else if (seriesIndex === 1) {
            info = `Service Requests: ${w.globals.series[seriesIndex][dataPointIndex]}`;
          } else if (seriesIndex === 2) {
            info = `Satisfaction Rating: ${w.globals.series[seriesIndex][dataPointIndex]} / 5`;
          }
          return `<div style="padding:10px;">${info}</div>`;
        },
      },
      fill: {
        opacity: 1,  // Fully filled bars
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left',
        offsetX: 40,
        itemMargin: { vertical: 10 },
      },
      annotations: {
        points: [
          {
            x: 'Jun',  // Example annotation (highlight peak for Service Providers)
            y: 45,
            seriesIndex: 0,
            label: {
              borderColor: '#fff',
              offsetY: 0,
              style: {
                color: '#fff',
                background: '#FF4560',
              },
              text: 'Peak in Service Providers',
            },
          },
        ],
      },
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="bar" height={350} />
      </div>
    </div>
  );
};

export default ProductivityTab;
