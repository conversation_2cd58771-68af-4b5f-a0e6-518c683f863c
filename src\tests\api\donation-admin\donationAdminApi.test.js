import axios from 'axios';
import { mockStatsData, mockChartData, mockDistributionData, mockApiResponse } from '../../utils/donationTestUtils';
import { setupAllMocks } from '../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// API service functions for donation admin dashboard
const donationAdminApi = {
  getStats: async () => {
    const response = await axios.get('/api/dashboard/stats');
    return response.data;
  },

  getNgoSignupData: async (period = 'yearly') => {
    const response = await axios.get(`/api/dashboard/ngo-signups?period=${period}`);
    return response.data;
  },

  getDonationTrends: async (period = 'yearly') => {
    const response = await axios.get(`/api/dashboard/donation-trends?period=${period}`);
    return response.data;
  },

  getDonationDistribution: async () => {
    const response = await axios.get('/api/dashboard/donation-distribution');
    return response.data;
  },

  getTopDonors: async (limit = 10) => {
    const response = await axios.get(`/api/dashboard/top-donors?limit=${limit}`);
    return response.data;
  },

  getRecentActivities: async (limit = 20) => {
    const response = await axios.get(`/api/dashboard/recent-activities?limit=${limit}`);
    return response.data;
  },

  exportDashboardData: async (format = 'csv') => {
    const response = await axios.get(`/api/dashboard/export?format=${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

describe('Donation Admin API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/dashboard/stats', () => {
    it('fetches dashboard statistics successfully', async () => {
      const mockStats = mockStatsData({
        totalDonations: 1000000,
        uniqueDonors: 150,
        last30DaysDonations: 25,
        averageDonation: 6666,
      });

      mockAxios.get.mockResolvedValue(mockApiResponse(mockStats));

      const result = await donationAdminApi.getStats();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/stats');
      expect(result).toEqual(mockStats);
    });

    it('handles missing stats data', async () => {
      const emptyStats = {
        totalDonations: 0,
        uniqueDonors: 0,
        last30DaysDonations: 0,
        averageDonation: 0,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(emptyStats));

      const result = await donationAdminApi.getStats();

      expect(result.totalDonations).toBe(0);
      expect(result.uniqueDonors).toBe(0);
    });

    it('handles API errors for stats', async () => {
      mockAxios.get.mockRejectedValue(new Error('Stats service unavailable'));

      await expect(donationAdminApi.getStats()).rejects.toThrow('Stats service unavailable');
    });
  });

  describe('GET /api/dashboard/ngo-signups', () => {
    it('fetches NGO signup data for yearly period', async () => {
      const mockData = {
        period: 'yearly',
        series: [{ name: 'NGO Signups', data: [15, 22, 18, 25, 30, 28, 35] }],
        categories: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getNgoSignupData('yearly');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/ngo-signups?period=yearly');
      expect(result).toEqual(mockData);
    });

    it('fetches NGO signup data for monthly period', async () => {
      const mockData = {
        period: 'monthly',
        series: [{ name: 'NGO Signups', data: [2, 1, 3, 2, 3, 1] }],
        categories: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getNgoSignupData('monthly');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/ngo-signups?period=monthly');
      expect(result.period).toBe('monthly');
    });

    it('defaults to yearly period when no period specified', async () => {
      const mockData = {
        period: 'yearly',
        series: [{ name: 'NGO Signups', data: [15, 22, 18, 25, 30, 28, 35] }],
        categories: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getNgoSignupData();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/ngo-signups?period=yearly');
    });

    it('handles invalid period parameter', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 400,
          data: {
            message: 'Invalid period parameter',
          },
        },
      });

      await expect(donationAdminApi.getNgoSignupData('invalid')).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });
  });

  describe('GET /api/dashboard/donation-trends', () => {
    it('fetches donation trends for different periods', async () => {
      const mockData = {
        period: 'weekly',
        series: [{ name: 'Total Donations', data: [50, 30, 80, 60, 90, 70, 100] }],
        categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getDonationTrends('weekly');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/donation-trends?period=weekly');
      expect(result.period).toBe('weekly');
      expect(result.series[0].data).toHaveLength(7);
    });

    it('handles empty donation trends data', async () => {
      const emptyData = {
        period: 'daily',
        series: [{ name: 'Total Donations', data: [] }],
        categories: [],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(emptyData));

      const result = await donationAdminApi.getDonationTrends('daily');

      expect(result.series[0].data).toHaveLength(0);
      expect(result.categories).toHaveLength(0);
    });
  });

  describe('GET /api/dashboard/donation-distribution', () => {
    it('fetches donation distribution data successfully', async () => {
      const mockData = mockDistributionData({
        series: [4400, 5500, 3500, 4300, 2400],
        labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
      });

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getDonationDistribution();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/donation-distribution');
      expect(result.series).toHaveLength(5);
      expect(result.labels).toHaveLength(5);
    });

    it('handles empty distribution data', async () => {
      const emptyData = {
        series: [],
        labels: [],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(emptyData));

      const result = await donationAdminApi.getDonationDistribution();

      expect(result.series).toHaveLength(0);
      expect(result.labels).toHaveLength(0);
    });

    it('calculates percentages correctly', async () => {
      const mockData = {
        series: [4400, 5500, 3500, 4300, 2400],
        labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
        percentages: [22.0, 27.5, 17.5, 21.5, 12.0],
        total: 20100,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getDonationDistribution();

      expect(result.percentages).toBeDefined();
      expect(result.total).toBe(20100);
    });
  });

  describe('GET /api/dashboard/top-donors', () => {
    it('fetches top donors with default limit', async () => {
      const mockData = {
        donors: [
          { id: '1', name: 'John Doe', totalDonated: 50000, donationCount: 25 },
          { id: '2', name: 'Jane Smith', totalDonated: 45000, donationCount: 30 },
          { id: '3', name: 'Bob Johnson', totalDonated: 40000, donationCount: 20 },
        ],
        limit: 10,
        total: 3,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getTopDonors();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/top-donors?limit=10');
      expect(result.donors).toHaveLength(3);
    });

    it('fetches top donors with custom limit', async () => {
      const mockData = {
        donors: [
          { id: '1', name: 'John Doe', totalDonated: 50000, donationCount: 25 },
        ],
        limit: 5,
        total: 1,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getTopDonors(5);

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/top-donors?limit=5');
      expect(result.limit).toBe(5);
    });
  });

  describe('GET /api/dashboard/recent-activities', () => {
    it('fetches recent activities successfully', async () => {
      const mockData = {
        activities: [
          {
            id: '1',
            type: 'donation',
            description: 'New donation received',
            amount: 1000,
            timestamp: '2024-01-01T10:00:00Z',
            donor: 'John Doe',
          },
          {
            id: '2',
            type: 'ngo_signup',
            description: 'New NGO registered',
            timestamp: '2024-01-01T09:30:00Z',
            ngo: 'Education Foundation',
          },
        ],
        limit: 20,
        total: 2,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationAdminApi.getRecentActivities();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/recent-activities?limit=20');
      expect(result.activities).toHaveLength(2);
    });

    it('handles empty activities list', async () => {
      const emptyData = {
        activities: [],
        limit: 20,
        total: 0,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(emptyData));

      const result = await donationAdminApi.getRecentActivities();

      expect(result.activities).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('GET /api/dashboard/export', () => {
    it('exports dashboard data as CSV', async () => {
      const mockBlob = new Blob(['csv,data'], { type: 'text/csv' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      const result = await donationAdminApi.exportDashboardData('csv');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/export?format=csv', {
        responseType: 'blob',
      });
      expect(result).toBeInstanceOf(Blob);
    });

    it('exports dashboard data as Excel', async () => {
      const mockBlob = new Blob(['excel,data'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      const result = await donationAdminApi.exportDashboardData('xlsx');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/export?format=xlsx', {
        responseType: 'blob',
      });
      expect(result).toBeInstanceOf(Blob);
    });

    it('defaults to CSV format', async () => {
      const mockBlob = new Blob(['csv,data'], { type: 'text/csv' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      await donationAdminApi.exportDashboardData();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/export?format=csv', {
        responseType: 'blob',
      });
    });
  });

  describe('Error Handling', () => {
    it('handles network timeouts', async () => {
      mockAxios.get.mockRejectedValue(new Error('timeout of 5000ms exceeded'));

      await expect(donationAdminApi.getStats()).rejects.toThrow('timeout of 5000ms exceeded');
    });

    it('handles server errors (500)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 500,
          data: {
            message: 'Internal server error',
          },
        },
      });

      await expect(donationAdminApi.getDonationTrends()).rejects.toMatchObject({
        response: {
          status: 500,
        },
      });
    });

    it('handles unauthorized access (401)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 401,
          data: {
            message: 'Unauthorized access',
          },
        },
      });

      await expect(donationAdminApi.getStats()).rejects.toMatchObject({
        response: {
          status: 401,
        },
      });
    });

    it('handles forbidden access (403)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 403,
          data: {
            message: 'Insufficient permissions for admin dashboard',
          },
        },
      });

      await expect(donationAdminApi.getNgoSignupData()).rejects.toMatchObject({
        response: {
          status: 403,
        },
      });
    });

    it('handles rate limiting (429)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 429,
          data: {
            message: 'Too many requests',
          },
        },
      });

      await expect(donationAdminApi.getDonationDistribution()).rejects.toMatchObject({
        response: {
          status: 429,
        },
      });
    });
  });

  describe('Request Configuration', () => {
    it('includes proper headers for API requests', async () => {
      mockAxios.get.mockResolvedValue(mockApiResponse({}));

      await donationAdminApi.getStats();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/stats');
    });

    it('handles blob responses for export functionality', async () => {
      const mockBlob = new Blob(['test'], { type: 'text/csv' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      const result = await donationAdminApi.exportDashboardData('csv');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/dashboard/export?format=csv', {
        responseType: 'blob',
      });
      expect(result).toBeInstanceOf(Blob);
    });
  });
});
