import React, { useContext, useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { Card } from "@mui/material";

// Dynamically import ApexChart to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ZoneWiseChart = () => {
  const { listValues,user } = useContext(AuthContext);
  const router = useRouter();

  const generateRandomColor = () => {
    return `#${Math.floor(Math.random() * 16777215)
      .toString(16)
      .padStart(6, "0")}`;
  };

  const [sampleData, setSampleData] = useState([]);

  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url =
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/admin/service-requisitions-count-by-zone";
    } else {
      url =
        getUrl(authConfig.statisticsEndpointGraphs) +
        "/service-requisitions-count-by-zone";
    }

    let headers;

    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        accept: authConfig?.STATISTICS_GET_REQ_BY_ZONE_COUNT_ADMIN_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        accept: authConfig?.STATISTICS_GET_REQ_BY_ZONE_COUNT_EMPLOYEE_V1,
      });
    }

    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  // Extract zones (using zoneId) and counts from sampleData
  const zones = sampleData?.map(
    (item) =>
      listValues?.find((listItem) => listItem.id === item.zoneId)?.name ||
      item.zoneId
  );
  const counts = sampleData?.map((item) => parseInt(item.count, 10));

  // Generate random colors for the chart
  const randomColors = zones?.map(() => generateRandomColor());

  // Handle bar click
  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;
    const clickedZoneId = sampleData[clickedIndex]?.zoneId;
    const query = { zone: clickedZoneId  };
    if (clickedZoneId) {
       if (user?.roleId !== authConfig?.superAdminRoleId) {
              query.assignZone = true;
            }
      router.push({
        pathname: "/service-requisitions",
        query,
      });
    }
  };

  const options = {
    chart: {
      id: "zone-wise-bar-chart",
      events: {
        dataPointSelection: handleBarClick, // Add click event handler
      },
    },
    xaxis: {
      categories: zones,
    },
    yaxis: {
      title: {
        text: "No. of SR's by Zone",
      },
    },
    title: {
      text: "Zone-Wise Service Requisition Data",
      align: "center",
    },
    plotOptions: {
      bar: {
        horizontal: true, // Horizontal layout for better scalability
        distributed: true, // Enable distributed mode for unique colors per bar
      },
    },
    dataLabels: {
      enabled: true, // Show data labels for better insight
      style: {
        colors: ["#000"],
      },
    },
    colors: randomColors, // Apply random colors to each bar
  };

  const series = [
    {
      name: "Total Requisitions",
      data: counts,
    },
  ];

  return (
    <Card style={{ height: "450px", overflowY: "auto" }} sx={{ p: 3 }}>
      <ApexChart options={options} series={series} type="bar" height={600} />
    </Card>
  );
};

export default ZoneWiseChart;
