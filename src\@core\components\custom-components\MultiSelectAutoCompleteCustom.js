import React, { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import CustomAutocomplete from "./CustomAutoComplete";
import CustomTextField from "./CustomTextField";
import Icon from "src/@core/components/icon";
import { Controller, useForm } from "react-hook-form";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField,
} from "@mui/material";
import { useAuth } from "src/hooks/useAuth";

const MultiSelectAutoCompleteCustom = (props) => {
  const { id, label, nameArray, value,  onChange, listNameId,setValuesUpdate } = props;

  const { control, reset } = useForm();

  const auth = useAuth();

  const [selectedValues, setSelectedValues] = useState(value || []);
  const [isDropdownOpen, setDropdownOpen] = useState(false);

  const [dialogOpen, setDialogOpen] = useState(false);
  const [newOption, setNewOption] = useState("");

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  useEffect(() => {
    setSelectedValues(value || []);
  }, [value]);

  const handleOptionSelect = (event, newValue) => {
    const addNewOption = newValue.find((option) => option.value === "add-new");
    if (addNewOption) {
      setDialogOpen(true);
      // setAddSection(true);
    } else {
      setSelectedValues(newValue);
      onChange({ target: { value: newValue } });
    }
    setDropdownOpen(false);
  };

  const handleRemoveSelectedOption = (optionToRemove) => {
    const newSelectedValues = selectedValues.filter(
      (option) => option !== optionToRemove
    );
    setSelectedValues(newSelectedValues);
    onChange({ target: { value: newSelectedValues } });
  };

  async function handleAddNew(data) {
      setValuesUpdate(false)
    const fields = {
      name: newOption.trim(),
      listNamesId: listNameId,
    };

    let message;

    try {
      const response = await auth.postService(fields);
      if (response) {
        // setSelectedValues([...selectedValues,  newOption.trim()]);
        message = `
    <div> 
      <h3>Value added Successfully.</h3>
    </div>
  `;

        setDialogMessage(message);
        setOpenDialogContent(true);
      }
      reset();
    } catch (error) {
      console.error("Service Data Creation failed:", error);
      message = `
      <div> 
        <h3> Failed to Add Section name. Please try again later.</h3>
      </div>
    `;
      setDialogMessage(message);
      setOpenDialogContent(true);   
    }
    setValuesUpdate(true)
    setNewOption("");
  }

  const handleDropdownOpen = async () => {
    setDropdownOpen(true);

     // Ensure labelId exists before invoking the function
     if (selectedValues?.length > 0) {
      const labelId = selectedValues[0]?.value; // Example: Assuming first value has fieldId
    }
  };

  const handleDropdownClose = () => {
    setDropdownOpen(false);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
    setDialogOpen(false);
  };

  return (
    <>
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <CustomAutocomplete
          multiple
          autoHighlight
          id={id}
          options={[
            { key: "Add New", value: "add-new" },
            ...nameArray?.filter(
              (option) =>
                !selectedValues?.some(
                  (selected) => selected?.key === option?.key
                )
            ),
          ]}
          getOptionLabel={(option) => option?.key || ""}
          value={selectedValues}
          onChange={(event, newValue) => handleOptionSelect(event, newValue)}
          onOpen={handleDropdownOpen}
          onClose={handleDropdownClose}
          open={isDropdownOpen}
          renderOption={(props, option) => (
            <Box
              component="li"
              {...props}
              onClick={(event) =>
                handleOptionSelect(event, [...selectedValues, option])
              }
            >
              {option.value === "add-new" ? (
                <Box component="li" {...props}>
                  <Icon icon="tabler:circle-plus" />
                  <span>Add New</span>
                </Box>
              ) : (
                option.key
              )}
            </Box>
          )}
          renderInput={(params) => (
            <CustomTextField
              {...params}
              size="small"
              placeholder={`  ${label}`}
              sx={{ borderRadius: 1 }}
              inputProps={{
                ...params.inputProps,
              }}
            />
          )}
          
        />
      </Box>
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} fullWidth>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.5)} !important`,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          Add New
          <IconButton
            size="small"
            onClick={() => setDialogOpen(false)}
            sx={{
              // p: "0.438rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(10, 8)} !important`,
          }}
        >
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <Controller
                name="name"
                control={control}
                rules={{ required: "This field is required" }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    id="name"
                    label="Name"
                    size="small"
                    fullWidth
                    autoFocus
                    margin="dense"
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px",
          }}
        >
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddNew} variant="contained" color="primary">
            Add
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default MultiSelectAutoCompleteCustom;
