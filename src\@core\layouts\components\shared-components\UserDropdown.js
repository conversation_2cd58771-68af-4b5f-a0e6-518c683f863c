// ** React Imports
import { useState, Fragment, useEffect, useContext } from "react";
import authConfig from "src/configs/auth";

// ** Next Import
import { useRouter } from "next/router";

// ** MUI Imports
import Box from "@mui/material/Box";
import Menu from "@mui/material/Menu";
import Badge from "@mui/material/Badge";
import axios from "axios";
import Avatar from "@mui/material/Avatar";
// ** JWT import
import jwt from "jsonwebtoken";

import {
  Dialog,
  DialogTitle,
  DialogActions,
  DialogContent,
  DialogContentText,
  FormControl,
  Button,
  TextField,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  useMediaQuery,
} from "@mui/material";

import Divider from "@mui/material/Divider";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import MenuItem from "@mui/material/MenuItem";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import Grid from "@mui/material/Grid";
import { Controller, useForm } from "react-hook-form";
import FormHelperText from "@mui/material/FormHelperText";
import { useTheme } from "@mui/material/styles";
import MUITableCell from "src/pages/SP/MUITableCell";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AuthContext } from "src/context/AuthContext";

// ** Icon Imports
import IconButton from "@mui/material/IconButton";
import Icon from "src/@core/components/icon";

import CloseIcon from "@mui/icons-material/Close";
// ** Context
import { useAuth } from "src/hooks/useAuth";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import React from "react";
import Notification from "./Notification";

import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import SettingsIcon from "@mui/icons-material/Settings";
import CustomAvatar from "src/@core/components/mui/avatar";
import { Tooltip } from "@mui/material";
import QuickLink from "src/quick-link/QuickLink";
import TopMenuNew from "src/top-menu/TopMenuNew";
import TopMenuCanva from "src/top-menu/TopMenuCanva";
import QuickLinks from "src/quick-link";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import NGOAddingDonationReceipt from "src/pages/ngoaddingdonationreceipt";
import DonateDialog from "./DonateDialog";
import { VolunteerActivism } from "@mui/icons-material";
// ** Styled Components
const BadgeContentSpan = styled("span")(({ theme }) => ({
  width: 8,
  height: 8,
  borderRadius: "50%",
  backgroundColor: theme.palette.success.main,
  boxShadow: `0 0 0 2px ${theme.palette.background.paper}`,
}));

const MenuItemStyled = styled(MenuItem)(({ theme }) => ({
  "&:hover .MuiBox-root, &:hover .MuiBox-root svg": {
    color: theme.palette.primary.main,
  },
}));

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const UserDropdown = (props) => {
  // ** Props
  const { settings } = props;

  const theme = useTheme();

  const { user } = useContext(AuthContext);

  // ** States
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchor, setAnchor] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [entityCategory, setEntityCategory] = useState("");
  const [email, setEmail] = useState("");
  const [entityType, setEntityType] = useState("");
  const [isMenuOpen, setIsMenuOpen] = useState(true);
  const [openAlertDialog, setOpenAlertDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");

  // ** Hooks
  const router = useRouter();
  const { logout, updateProfile } = useAuth();

  const [donateOpen, setDonateOpen] = useState(false);

  const validateMyProfile = yup.object().shape({
    firstName: yup
      .string()
      .required("First Name is required")
      .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
      .min(3, "First name must be atleast 3 characters")
      .max(30, "First Name must not exceed 30 characters"),

    lastName: yup
      .string()
      .required("Last Name is required")
      .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
      .min(3, "Last name must be atleast 3 characters")
      .max(30, "Last Name must not exceed 30 characters"),

    mobileNumber: yup
      .string()
      .nullable()
      .test(
        "isValidLength",
        "Enter a Valid 10-Digit Mobile Number",
        (value) => {
          if (value === null || value === undefined || value.trim() === "") {
            return true; // Allow null or empty value
          }
          if (value.startsWith("+91")) {
            return value.length >= 13;
          } else {
            return value.length >= 10;
          }
        }
      )
      .test(
        "isValidPhoneNumber",
        "Contact number should start with a digit in between 6 to 9",
        (value) => {
          if (value === null || value === undefined || value.trim() === "") {
            return true; // Allow empty value
          }
          const phoneNumberRegex = /^(?:\+91\s?)?[6-9]\d{9}$/;
          return phoneNumberRegex.test(value);
        }
      )
      .max(13, "Contact number must not exceed 13 characters"),
  });

  const {
    register,
    setError,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validateMyProfile),
    mode: "onChange",
  });

  // ** Vars
  const { direction } = settings;

  const handleDropdownOpen = (event) => {
    setIsMenuOpen(true);
    setAnchorEl(event.currentTarget);
  };

  const handleDropdownClose = (url) => {
    if (url) {
      router.push(url);
    }
    setAnchorEl(null);
  };

  async function submit(data) {
    const message = `
      <div>
          <h3>
           profile data is saved
          </h3>
         </div>
         `;
    setDialogMessage(message);
    setOpenAlertDialog(true);

    const trimmedData = Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === "string" ? value.trim() : value,
      ])
    );

    const response = await updateProfile(trimmedData, () => {
      console.error("Profile Details failed");
    });
    handleCloseDialog();
  }

  const styles = {
    px: 4,
    py: 1.75,
    width: "100%",
    display: "flex",
    alignItems: "center",
    color: "black",
    textDecoration: "none",
    "& svg": {
      mr: 2.5,
      color: "text.primary",
    },
  };

  const [openLogoutDialog, setOpenLogoutDialog] = useState(false);

  const handleLogoutConfirmation = () => {
    const message = `
    <div>
      <h3>Are you sure you want to logout?</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenLogoutDialog(true);
  };

  const handleLogoutCancel = () => {
    setOpenLogoutDialog(false);
  };

  const handleLogoutConfirmed = async () => {
    await axios({
      method: "post",
      url: getUrl(authConfig.logoutEndpoint),
      headers: getAuthorizationHeaders(),
    });

    setOpenLogoutDialog(false);
    handleDropdownClose("/logout");
    handleLogout();
  };

  const handleLogout = () => {
    logout();
    handleDropdownClose();
  };

  const handleEdit = () => {
    setOpenDialog(true);
    setIsMenuOpen(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleClose = () => {
    setOpenAlertDialog(false);
  };

  const [firstName, setFirstName] = useState(user?.firstName);
  const [lastName, setLastName] = useState(user?.lastName);
  const [mobileNumber, setMobileNumber] = useState(user?.mobileNumber);
  const [quickLinkOpen, setQuickLinkOpen] = useState(false);
  const [topMenuOpen, setTopMenuOpen] = React.useState(false);

  const quickLinkData = QuickLinks();

  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  const handleTopMenuChange = () => {
    setTopMenuOpen(!topMenuOpen);
  };

  useEffect(() => {
    const handleResize = () => {
      const newIsMobile = window.innerWidth < 768;
      setIsMobile(newIsMobile);

      if (newIsMobile && topMenuOpen) {
        setTopMenuOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [topMenuOpen]);

  const handleQuickLinkChange = () => {
    if (quickLinkData?.length > 0) {
      setQuickLinkOpen(!quickLinkOpen);
    }
  };

  const navigateToCHS = () => {
    router.push("/CHS/profile");
    setIsMenuOpen(false);
  };

  const navigateToSP = () => {
    router.replace("/SP/basic-profile");
    setIsMenuOpen(false);
  };

  const [secondsLeft, setSecondsLeft] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const token = localStorage.getItem("accessToken");
      if (token) {
        const decodedToken = jwt.decode(token);
        if (decodedToken && decodedToken.exp) {
          const expiry = decodedToken.exp * 1000; // Convert to milliseconds
          const currentTime = Date.now();
          const timeRemaining = expiry - currentTime;

          // If less than or equal to 5 mins and still valid
          if (timeRemaining > 0 && timeRemaining <= 300000) {
            setSecondsLeft(Math.floor(timeRemaining / 1000));
          } else {
            setSecondsLeft(null); // Optional: reset if it's not in 5-min range
          }
        }
      }
    }, 1000); // Check every second

    return () => clearInterval(interval); // Cleanup on unmount
  }, []);

  const formatTime = (totalSeconds) => {
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
      2,
      "0"
    )}`;
  };

  const handleCloseDonate = () => {
    setDonateOpen(false);
  };

  const handleDonateClick = () => {
    setDonateOpen(true);
  };

  const isSmallScreen = useMediaQuery("(max-width:425px)");

  return (
    <Fragment>
      {secondsLeft > 0 && (
        <Box
          sx={{
            position: "fixed",
            right: 210,
            backgroundColor: "#fff0f0",
            border: "1px solid red",
            borderRadius: "8px",
            padding: "4px 4px",
            zIndex: 2000,
          }}
        >
          <Typography variant="body2" sx={{ color: "red", fontWeight: "bold" }}>
            {isSmallScreen
              ? formatTime(secondsLeft)
              : `⏳Session expires in ${formatTime(secondsLeft)}`}
          </Typography>
        </Box>
      )}

      {user?.organisationCategory === "DONOR" && (
        <Tooltip title="Donate">
          <VolunteerActivism
            onClick={handleDonateClick}
            sx={{ fontSize: 40, color: "primary.main", mr: 2 }}
          />
        </Tooltip>
      )}

      {/* {user?.permissionsDTOList && user.permissionsDTOList.length > 1 ? ( */}
      {!(user?.organisationCategory === "DONOR") && (
        <Tooltip title="Settings">
          <CustomAvatar
            skin="light"
            variant="rounded"
            sx={{
              cursor: "pointer",
              zIndex: 1200,
              mr: { xs: 1, lg: 2 },
            }}
            onClick={handleTopMenuChange}
          >
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </CustomAvatar>
        </Tooltip>
      )}
      {/* ) : (
  <></>
)} */}

      {topMenuOpen && <TopMenuNew />}

      {/* {user?.permissionsDTOList && user.permissionsDTOList.length > 1 ? (
      <Tooltip title="Quick Links">
        <CustomAvatar
          skin="light"
          variant="rounded"
          sx={{
            cursor: "pointer",
            zIndex: 1200,
          }}
          onClick={handleQuickLinkChange}
        >
          <Icon>
            <AddCircleOutlineIcon />
          </Icon>
        </CustomAvatar>
      </Tooltip>
      ) : (
        <></>
      )} */}

      {quickLinkOpen && <QuickLink />}

      <DonateDialog open={donateOpen} onClose={handleCloseDonate} />

      <Dialog
        open={openAlertDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={handleClose}
              style={{ margin: "10px auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      {/* <Notification /> */}

      <Badge
        overlap="circular"
        onClick={handleDropdownOpen}
        sx={{ ml: 2, cursor: "pointer" }}
        badgeContent={<BadgeContentSpan />}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
      >
        <Avatar
          alt="Profile"
          onClick={handleDropdownOpen}
          sx={{
            width: 40,
            height: 40,
            border: "2px solid gray",
            borderRadius: "50%",
          }}
          src="/images/donation-reciept/logo-1.webp"
        />
      </Badge>

      <Menu
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl) && isMenuOpen}
        onClose={handleDropdownClose}
        sx={{ "& .MuiMenu-paper": { width: 250, mt: 3.5 } }}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: direction === "ltr" ? "right" : "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: direction === "ltr" ? "right" : "left",
        }}
      >
        <Box sx={{ py: 1.5, px: 2 }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Badge
              overlap="circular"
              badgeContent={<BadgeContentSpan />}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "right",
              }}
            >
              <Avatar
                alt="John Doe"
                src="/images/donation-reciept/logo-1.webp"
                sx={{
                  width: 34,
                  height: 32,
                  border: "2px solid gray",
                  borderRadius: "50%",
                  overflow: "hidden",
                }}
              ></Avatar>
            </Badge>
            <Box
              sx={{
                display: "flex",
                ml: 1.5,
                alignItems: "flex-start",
                flexDirection: "column",
              }}
            >
              <Typography sx={{ fontWeight: 500 }}>{user?.name}</Typography>
              <Typography fontSize={13.5}>
                {user?.organisationCategory}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Divider sx={{ my: (theme) => `${theme.spacing(1)} !important` }} />
        {/* <MenuItemStyled
          onClick={() => {
            if (user.organisationCategory === "TENANT") {
              handleEdit();
            } else if (user.organisationCategory === "SOCIETY") {
              navigateToCHS();
            } else if (user.organisationCategory === "SERVICE_PROVIDER") {
              navigateToSP();
            }
          }}
          sx={{ p: 1.5, "& svg": { mr: 2, fontSize: "1.375rem" } }}
        >
          <Icon icon="tabler:user-check" />
          Profile
        </MenuItemStyled> */}

        {/* <a href={authConfig.guestURL + "faq"}>
          <MenuItemStyled
            sx={{ p: 1.5, "& svg": { mr: 2, fontSize: "1.375rem" } }}
          >
            <Icon icon="tabler:help" />
            FAQ
          </MenuItemStyled>
        </a> */}
        <Divider sx={{ my: (theme) => `${theme.spacing(1)} !important` }} />
        <MenuItemStyled
          onClick={handleLogoutConfirmation}
          sx={{ p: 1.5, "& svg": { mr: 2, fontSize: "1.375rem" } }}
        >
          <Icon icon="tabler:logout" />
          Logout
        </MenuItemStyled>
        <Dialog
          open={openLogoutDialog}
          onClose={handleLogoutCancel}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "center",
                p: (theme) => `${theme.spacing(2.5)} !important`,
              }}
            >
              <Button
                // href="/logout"
                onClick={handleLogoutConfirmed}
                color="primary"
                sx={{ marginRight: 2 }}
              >
                Yes
              </Button>

              <Button
                onClick={handleLogoutCancel}
                color="primary"
                sx={{ marginLeft: 2 }}
              >
                No
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"xs"}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.5)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "flex-start",
              marginLeft: { xl: 5.5, lg: 5.5, md: 5.5, sm: 5.5, xs: 5.5 },
            }}
            textAlign={"center"}
            fontSize={"24px !important"}
            fontWeight={"bold"}
          >
            {"Edit Profile "}
            <Box
              sx={{
                position: "absolute",
                top: "4px",
                right: "14px",
                marginRight: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 },
              }}
            >
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>

          <DialogContent
            dividers={scroll === "paper"}
            sx={{
              position: "relative",
              pt: (theme) => `${theme.spacing(8)} !important`,
              pb: (theme) => `${theme.spacing(5)} !important`,
              px: (theme) => [`${theme.spacing(8)} !important`],
            }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="firstName"
                    control={control}
                    defaultValue={user?.firstName}
                    render={({ field }) => (
                      <NameTextField
                        {...field}
                        label="First Name"
                        placeholder="Enter First Name"
                        size="small"
                        error={Boolean(errors.firstName)}
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 50 }}
                        helperText={errors.firstName?.message}
                        aria-describedby="validation-basic-firstName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="lastName"
                    control={control}
                    defaultValue={user?.lastName}
                    render={({ field }) => (
                      <NameTextField
                        {...field}
                        id="lastName"
                        label="Last Name"
                        placeholder="Enter Last Name"
                        size="small"
                        error={Boolean(errors.lastName)}
                        InputLabelProps={{ shrink: true }}
                        inputProps={{ maxLength: 30 }}
                        helperText={errors.lastName?.message}
                        aria-describedby="validation-basic-lastName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="mobileNumber"
                    control={control}
                    defaultValue={user?.mobileNumber}
                    render={({ field }) => (
                      <MobileNumberValidation
                        {...field}
                        type="tel"
                        label="Mobile Number"
                        size="small"
                        placeholder="Contact Number"
                        error={Boolean(errors.mobileNumber)}
                        helperText={errors.mobileNumber?.message}
                        aria-describedby="validation-Mobile Number"
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <TableContainer
                sx={{ padding: "10px 10px" }}
                className="tableBody"
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Email:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {user?.email}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </DialogContent>
          <Divider
            sx={{
              mt: `${theme.spacing(2)} !important`,
              mb: `${theme.spacing(2.25)} !important`,
            }}
          />
          <DialogActions
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              p: (theme) => `${theme.spacing(2.5)} !important`,
              marginRight: { xl: 5, lg: 5, md: 5, sm: 5, xs: 5 },
            }}
          >
            <Button
              variant="outlined"
              color="primary"
              onClick={handleCloseDialog}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              type="submit"
              onClick={handleSubmit(submit)}
            >
              Update
            </Button>
          </DialogActions>
        </Dialog>
      </Menu>
    </Fragment>
  );
};

export default UserDropdown;
