import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
} from "@mui/material";
import {
  Close as CloseIcon,
  CropSquareOutlined,
  MinimizeSharp,
} from "@mui/icons-material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
function FileInput({ selectedFiles, setSelectedFiles }) {
  const [fileError, setFileError] = useState("");
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    const preventDefaultDragDrop = (event) => {
      event.preventDefault();
    };
    document.addEventListener("dragover", preventDefaultDragDrop);
    document.addEventListener("drop", preventDefaultDragDrop);
    return () => {
      document.removeEventListener("dragover", preventDefaultDragDrop);
      document.removeEventListener("drop", preventDefaultDragDrop);
    };
  }, []);

  const removeFile = (index) => {
    setSelectedFiles((prevFiles) => {
      const newFiles = [...prevFiles];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const isValidFileType = (file) => {
    const validTypes = [
      ".pdf",
      ".doc",
      ".docx",
      ".jpg",
      ".jpeg",
      ".gif",
      ".bmp",

      ".png",
    ];
    const fileType = "." + file.name.split(".").pop();
    return validTypes.includes(fileType);
  };

  const isValidFileSize = (file) => {
    const maxSize = 5 * 1024 * 1024; // 5 MB in bytes
    return file.size <= maxSize;
  };

  const handleFileChange = (event) => {
    const fileInput = event.target;
    const files = Array.from(fileInput.files);
    fileInput.value = "";
    files.forEach((file) => {
      if (isValidFileType(file) && isValidFileSize(file)) {
        setSelectedFiles((prevFiles) => [...prevFiles, file]);
        setFileError("");
      } else {
        setFileError(
          "Invalid file type or size. Please select a .png file below 5 MB."
        );
      }
    });
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    files.forEach((file) => {
      if (isValidFileType(file) && isValidFileSize(file)) {
        setSelectedFiles((prevFiles) => [...prevFiles, file]);
        setFileError("");
      } else {
        setFileError(
          "Invalid file type or size. Please select a .png file below 5 MB."
        );
      }
    });
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const openDialog = (index) => {
    setSelectedFileIndex(index);
  };

  const closeDialog = () => {
    setSelectedFileIndex(null);
  };

  const [expanded, setExpanded] = useState(false);

  const handleExpand = () => {
    setExpanded(!expanded);
  };
  const handleClick = () => {
    fileInputRef.current.click();
  };

  return (
    <Grid container>
      <Grid item xs={12}>
        <FormControl error={!!fileError}>
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              padding: "20px",
              border: "1px solid #ddd",
              borderRadius: "4px",
              backgroundColor: "#f2f7f2",
              cursor: "pointer",
            }}
            onClick={handleClick}
          >
            <input
              type="file"
              onChange={handleFileChange}
              style={{ display: "none" }}
              ref={fileInputRef}
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.gif,.bmp,.png"
            />
            <CloudUploadIcon style={{ fontSize: 50 }} />
            <p>Drop the files here (max 5 MB) (supported formats: PDF, DOC, DOCX,JPG, JPEG, GIF, BMP, PNG)</p>
            <Button
              style={{
                backgroundColor: "#eee",
                border: "none",
                padding: "5px 10px",
                cursor: "pointer",
              }}
            >
              Upload Files
            </Button>
          </div>

          {fileError && <FormHelperText>{fileError}</FormHelperText>}
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        {selectedFiles && selectedFiles.length > 0 && (
          <div>
            <p>Selected Files:</p>
            <ul>
              {selectedFiles.map((file, index) => (
                <li key={index}>
                  {file.name}&nbsp;&nbsp;&nbsp;
                  <Button onClick={() => removeFile(index)}>Remove</Button>
                  &nbsp;&nbsp;
                  <Button onClick={() => openDialog(index)}>View</Button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </Grid>
      <Dialog
        open={selectedFileIndex !== null}
        onClose={closeDialog}
        maxWidth={expanded ? "xl" : undefined}
        maxHeight={expanded ? "xl" : undefined}
      >
        <DialogTitle>
          View Image
          {expanded && (
            <>
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  margin: "8px",
                }}
              >
                <IconButton
                  edge="end"
                  color="inherit"
                  onClick={handleExpand}
                  aria-label="expand"
                  style={{ margin: "8px" }}
                >
                  <MinimizeSharp />
                </IconButton>
                <IconButton
                  edge="end"
                  color="inherit"
                  onClick={closeDialog}
                  aria-label="shrink"
                  style={{ margin: "8px" }}
                >
                  <CloseIcon />
                </IconButton>
              </div>
            </>
          )}
        </DialogTitle>
        <DialogContent>
          {selectedFileIndex !== null && (
            <>
              <img
                src={URL.createObjectURL(selectedFiles[selectedFileIndex])}
                alt={selectedFiles[selectedFileIndex].name}
                style={{ maxWidth: "100%" }}
              />
              {!expanded && (
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    margin: "8px",
                  }}
                >
                  <IconButton
                    edge="end"
                    color="inherit"
                    onClick={handleExpand}
                    aria-label="expand"
                    style={{ margin: "8px" }}
                  >
                    <CropSquareOutlined />
                  </IconButton>
                  <IconButton
                    edge="end"
                    color="inherit"
                    onClick={closeDialog}
                    aria-label="shrink"
                    style={{ margin: "8px" }}
                  >
                    <CloseIcon />
                  </IconButton>
                </div>
              )}
            </>
          )}
        </DialogContent>
      </Dialog>
    </Grid>
  );
}

export default FileInput;
