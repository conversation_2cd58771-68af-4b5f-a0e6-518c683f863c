import React, { useContext, useEffect } from "react";
import { useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  FormHelperText,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

import { Controller, useForm } from "react-hook-form";
import axios from "axios";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SnapshotInput from "./SnapshotFrom";
import SelectAutoComplete from "./SelectAutoComplete";
import { AuthContext } from "src/context/AuthContext";

const SnapshotUploadDialog = ({
  open,
  onClose,
  onSave,
  category,
  selectedFiles,
  setSelectedFiles,
  isShortForm,
  leadStatus,
  setLeadStatus,
  leadPriority,
  setLeadPriority,
  status,
  setStatus,
  assignedTo,
  setAssignedTo,
  loading,
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  
  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const handleClose = () => {
    setSelectedFiles([]);
    setLeadPriority("")
    setLeadStatus("")
    setStatus("")
    setAssignedTo("")
    onClose();
  };


  const [disableButton, setDisableButton] = useState(true);
  useEffect(() => {
    if (selectedFiles?.length > 0) {
      setDisableButton(false);
    } else {
      setDisableButton(true);
    }
  }, [selectedFiles]);

  const [leadStatusList, setLeadStatusList] = useState([]);
  const [leadPriorityList, setLeadPriorityList] = useState([]);
  const [statusList, setStatusList] = useState([]);

  const handleError = (error) => {
    console.error("leadsnapshot error:", error);
  };
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.statusListNamesId,
        (data) =>
          setStatusList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const [employeesData, setEmployeesData] = useState([]);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const handleAssignedToChange = (event) => {
    const value = event.target.value;
    setAssignedTo(value);
  };
  const handleStatusChange = (event) => {
    const value = event.target.value;
    setStatus(value);
  };

  const handleLeadStatusChange = (event) => {
    const value = event.target.value;
    setLeadStatus(value);
  };

  const handleLeadPriorityChange = (event) => {
    const value = event.target.value;
    setLeadPriority(value);
  };

  const handleSave = (data) => {
    if (onSave) {
      onSave({
        ...data,
        selectedFiles,
        leadSnapshotResponseDTO: {
          assignedTo: data.assignedTo,
          status: data.status,
          leadStatus: data.leadStatus,
          leadPriority: data.leadPriority,
        },
      });
    }
  };
  return (
    <Dialog
      fullWidth
      maxWidth="md"
      scroll="paper"
      open={open}
      onClose={(_event, reason) => {
        if (reason !== "backdropClick") {
          handleClose();
        }
      }}
    >
      <DialogTitle
        sx={{
          position: "relative",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 4)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: { xs: "start" },
          fontSize: { xs: 19, md: 20 },
          height:"50px", // height
        }}
        textAlign={"center"}
      >
        Add {category} Snapshots
        <Box
          sx={{
            position: "absolute",
            top: "9px",
            right: "9px",
          }}
        >
          <IconButton
            size="small"
            onClick={handleClose}
            sx={{
              p: "0.438rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <CloseIcon sx={{ fontSize: "1rem" }}/>
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          position: "relative",
          ":playing": (theme) => `${theme.spacing(4)} !important`,
          px: (theme) => [
            `${theme.spacing(6)} !important`,
            `${theme.spacing(10)} !important`,
          ],
          wordWrap: "break-word !important",
          whiteSpace: "pre-wrap !important",
        }}
      >
        <Grid container spacing={3}>
          {!isShortForm && (
            <>
            <Grid item xs={6}>
            <FormControl fullWidth>
              <Controller
                name="leadStatus"
                control={control}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="leadStatus"
                    label="Lead Status"
                    value={leadStatus}
                    nameArray={leadStatusList}
                    onChange={(e) => {
                      field.onChange(e);
                      handleLeadStatusChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={6}>
            <FormControl fullWidth>
              <Controller
                name="leadPriority"
                control={control}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="leadPriority"
                    label="Priority"
                    nameArray={leadPriorityList}
                    value={leadPriority}
                    onChange={(e) => {
                      field.onChange(e);
                      handleLeadPriorityChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={6}>
            <FormControl fullWidth>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="status"
                    label="Status"
                    nameArray={statusList}
                    value={status}
                    onChange={(e) => {
                      field.onChange(e);
                      handleStatusChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={6}>
            <FormControl fullWidth>
              <Controller
                name="assignedTo"
                control={control}
                render={({ field }) => (
                  <SelectAutoComplete
                    id="assignedTo"
                    label="Assigned To"
                    value={assignedTo}
                    nameArray={employeesData.map((emp) => ({
                      value: emp.id,
                      key: emp.name,
                    }))}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      handleAssignedToChange(e);
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>
            </>
          )}
          
          <Grid item xs={12} sm={7}>
            <SnapshotInput
              selectedFiles={selectedFiles}
              setSelectedFiles={setSelectedFiles}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.85)} !important`,
          height:"50px", // height
        }}
      >
        <Button variant="outlined" onClick={handleClose}>
          Close
        </Button>
        <Button variant="contained" onClick={handleSubmit(handleSave)} disabled={disableButton}>
          {loading ? <CircularProgress color="inherit" size={24} /> : "Save"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SnapshotUploadDialog;
