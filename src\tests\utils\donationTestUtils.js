import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// Create a test theme
const testTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Mock contexts
const mockAuthContext = {
  donationHeadDetails: null,
  user: {
    id: '1',
    name: 'Test User',
    role: 'admin',
    orgId: 'org-1',
    organisationCategory: 'SUPER_ADMIN'
  },
  isAuthenticated: true,
  postDonationHead: jest.fn(),
  patchDonationHead: jest.fn(),
  deleteDonationHead: jest.fn(),
};

const mockRBACContext = {
  canAccessActions: jest.fn(() => true),
  canAccessHeads: jest.fn(() => true),
  hasPermission: jest.fn(() => true),
  canMenuPage: jest.fn(() => true),
  canMenuPageSection: jest.fn(() => true),
  rbacRoles: ['ADMIN'],
};

// Mock router
const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  pathname: '/donation-head',
  query: {},
  asPath: '/donation-head',
  route: '/donation-head',
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
};

// Test wrapper component with all providers
const TestWrapper = ({ children, authContextValue = mockAuthContext, rbacContextValue = mockRBACContext }) => {
  return (
    <ThemeProvider theme={testTheme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

// Enhanced render function with providers
export const renderWithProviders = (component, options = {}) => {
  const { authContextValue, rbacContextValue, ...renderOptions } = options;

  const Wrapper = ({ children }) => (
    <TestWrapper
      authContextValue={authContextValue}
      rbacContextValue={rbacContextValue}
    >
      {children}
    </TestWrapper>
  );

  return render(component, {
    wrapper: Wrapper,
    ...renderOptions,
  });
};

// Simple render function for basic tests
export const renderComponent = (component) => {
  return renderWithProviders(component);
};

// Mock data generators
export const mockDonationHead = (overrides = {}) => ({
  id: '1',
  name: 'Test Donation Head',
  description: 'Test description',
  orgId: 'org-1',
  isActive: true,
  createdOn: '2024-01-01T00:00:00Z',
  updatedOn: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockTenant = (overrides = {}) => ({
  value: 'org-1',
  key: 'Test Organization',
  label: 'Test Organization',
  ...overrides,
});

export const mockApiResponse = (data, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
});

// Mock chart data
export const mockChartData = {
  series: [{ name: 'Test Data', data: [10, 20, 30, 40, 50] }],
  categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
};

// Mock donation heads list
export const mockDonationHeadsList = [
  mockDonationHead({ id: '1', name: 'Education Fund' }),
  mockDonationHead({ id: '2', name: 'Healthcare Support', isActive: false }),
  mockDonationHead({ id: '3', name: 'Food Aid Program' }),
];

// Mock tenants list
export const mockTenantsList = [
  mockTenant({ value: 'org-1', key: 'Organization 1' }),
  mockTenant({ value: 'org-2', key: 'Organization 2' }),
  mockTenant({ value: 'org-3', key: 'Organization 3' }),
];

// Export mock contexts for use in tests
export { mockAuthContext, mockRBACContext, mockRouter };
