import React, { useState } from 'react';
import Slider from '@mui/material/Slider';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';

// Utility function to format the budget display
const formatBudgetValue = (value) => {
  if (value >= 10000000) return `₹${(value / 10000000).toFixed(1)} Cr`; // Crores
  if (value >= 100000) return `₹${(value / 100000).toFixed(1)} L`; // Lakhs
  if (value >= 1000) return `₹${(value / 1000).toFixed(1)} K`; // Thousands
  return `₹${value}`; // Hundreds
};

// Styled Container with responsive styles
const StyledContainer = styled(Paper)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column', // Stack vertically by default
  alignItems: 'flex-start',
  padding: theme.spacing(2),
  width: '100%',
  maxWidth: '1300px',
  borderRadius: 4,
  border: `1px solid ${theme.palette.grey[400]}`,
  backgroundColor: '#ffffff',
  boxShadow: 'none',
  transition: 'border-color 0.3s ease, background-color 0.3s ease',
  '&:hover': {
    borderColor: theme.palette.text.primary,
  },
  '&:focus-within': {
    borderColor: theme.palette.primary.main,
  },
  [theme.breakpoints.up('sm')]: {
    flexDirection: 'row', // Arrange horizontally on small screens and up
    alignItems: 'center',
    height: "40px", // Apply height styling for tablet and above devices
  },
}));

const BudgetSlider = () => {
  const [budgetRange, setBudgetRange] = useState([20000, 80000]); // Initial range: 20K to 80K

  // Dynamically adjust the step size based on the current range
  const getStepSize = (value) => {
    if (value >= 10000000) return 500000; // 5 Lakhs step for Crores range
    if (value >= 100000) return 10000; // 10 Thousands step for Lakhs range
    if (value >= 20000) return 1000; // 1 Thousand step for 20K and above
    if (value >= 1000) return 500; // 500 step for Thousands range
    return 100; // Default step for lower ranges
  };

  const handleSliderChange = (event, newValue) => {
    setBudgetRange(newValue);
  };

  return (
    <StyledContainer>
      {/* Label for the Slider */}
      <Typography
        variant="body1"
        sx={{
          mr: { sm: 2 }, // Margin right on small screens and up
          mb: { xs: 1, sm: 0 }, // Margin bottom on extra small screens
          fontWeight: 'bold',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        Budget:
      </Typography>

      {/* Slider Component */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' }, // Stack vertically on extra small screens
          alignItems: 'center',
          width: '100%',
        }}
      >
        {/* Minimum Budget Value */}
        <Typography
          sx={{
            minWidth: { xs: 'auto', sm: '60px' },
            textAlign: { xs: 'center', sm: 'left' },
            pr: { sm: 2 }, // Padding right on small screens and up
            pb: { xs: 1, sm: 0 }, // Padding bottom on extra small screens
            display: 'flex',
            alignItems: 'center',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {formatBudgetValue(budgetRange[0])}
        </Typography>

        <Slider
          value={budgetRange}
          min={0} // Minimum value is 0 rupees
          max={10000000} // Maximum value is 1 crore rupees
          step={getStepSize(budgetRange[1])} // Dynamically adjust the step size
          onChange={handleSliderChange}
          valueLabelDisplay="auto"
          valueLabelFormat={formatBudgetValue}
          sx={{
            flexGrow: 1,
            mx: { xs: 0, sm: 2 }, // Horizontal margin on small screens and up
            width: '100%',
            '& .MuiSlider-thumb': {
              height: 20,
              width: 20,
              backgroundColor: '#fff',
              border: '2px solid gray',
              borderRadius: 4,
              boxShadow: 'none',
              '&:hover, &.Mui-focusVisible': {
                boxShadow: '0px 0px 0px 8px rgba(255, 105, 135, 0.16)',
              },
              '&:active': {
                boxShadow: '0px 0px 0px 14px rgba(255, 105, 135, 0.16)',
              },
            },
            '& .MuiSlider-rail': {
              height: 8,
              borderRadius: 4,
              backgroundColor: '#f0f0f0',
              opacity: 1,
              border: '1px solid gray',
            },
            '& .MuiSlider-track': {
              height: 8,
              borderRadius: 4,
              backgroundColor: 'green',
              border: '1px solid gray',
            },
          }}
        />

        {/* Maximum Budget Value */}
        <Typography
          sx={{
            minWidth: { xs: 'auto', sm: '80px' },
            textAlign: { xs: 'center', sm: 'right' },
            pl: { sm: 2 }, // Padding left on small screens and up
            pt: { xs: 1, sm: 0 }, // Padding top on extra small screens
            display: 'flex',
            alignItems: 'center',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {formatBudgetValue(budgetRange[1])}
        </Typography>
      </Box>
    </StyledContainer>
  );
};

export default BudgetSlider;
