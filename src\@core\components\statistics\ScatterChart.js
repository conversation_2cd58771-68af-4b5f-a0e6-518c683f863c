import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

// Function to generate day-wise time series data
const generateDayWiseTimeSeries = (baseval, count, yrange) => {
  const series = [];
  for (let i = 0; i < count; i++) {
    const x = baseval + i * 86400000; // one day in milliseconds
    const y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
    series.push([x, y]);
  }
  return series;
};

const ScatterChart = () => {
  const colors = ['#808080', '#FFD700', '#E5E4E2', '#8A2BE2', '#E0115F', '#B9F2FF']; // Custom colors

  const state = {
    series: [
      {
        name: 'Silver',
        data: generateDayWiseTimeSeries(new Date('01 Jun 2017 GMT').getTime(), 10, {min: 10, max: 50})
      },
      {
        name: 'Gold',
        data: generateDayWiseTimeSeries(new Date('11 Jun 2017 GMT').getTime(), 10, {min: 54, max: 90})
      },
      {
        name: 'Platinum',
        data: generateDayWiseTimeSeries(new Date('20 Jun 2017 GMT').getTime(), 8, {min: 30, max: 99})
      },
      {
        name: 'Titanium',
        data: generateDayWiseTimeSeries(new Date('28 Jul 2017 GMT').getTime(), 16, {min: 0, max: 90})
      },
      {
        name: 'Ruby',
        data: generateDayWiseTimeSeries(new Date('20 Jul 2017 GMT').getTime(), 10, {min: 0, max: 59})
      },
      {
        name: 'Diamond',
        data: generateDayWiseTimeSeries(new Date('29 Aug 2017 GMT').getTime(), 10, {min: 5, max: 60})
      }
    ],
    options: {
      chart: {
        height: 350,
        type: 'scatter',
        zoom: {
          type: 'xy'
        }
      },
      colors: colors,
      dataLabels: {
        enabled: false
      },
      grid: {
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      xaxis: {
        type: 'datetime',
        labels: {
          style: {
            colors: '#000000 !important', // Set X-axis labels to black
            fontSize: '12px'
          }
        }
      },
      yaxis: {},
      legend: {
        show: true,
        labels: {
          colors: colors,
          useSeriesColors: true
        },
        markers: {
          width: 12,
          height: 12,
          radius: 12,
          fillColors: colors
        }
      },
      markers: {
        size: 10,
        fillOpacity: 0.8,
        strokeColors: '#333',
        strokeWidth: 2
      }
    }
  };

  return (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="scatter" height={350} />
      </div>
    </div>
  );
};

export default ScatterChart;
