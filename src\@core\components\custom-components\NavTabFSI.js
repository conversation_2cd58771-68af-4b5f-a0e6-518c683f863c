// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Tab from '@mui/material/Tab'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'
import TabContext from '@mui/lab/TabContext'
import Typography from '@mui/material/Typography'

const NavTabFSI = props => {
  // ** State
  const [value, setValue] = useState('1')

  // ** Props
  const { tabContent1, tabContent2, tabContent3,tabContent4,tabContent5,tabContent6 } = props

  const handleChange = (event, newValue) => {
    setValue(newValue)
  }

  return (

    <TabContext value={value}>
    <TabList
      variant="scrollable"
      scrollButtons="auto"
      onChange={handleChange}
      aria-label="forced scroll tabs example"
    >
      <Tab
        value="1"
        component="a"
        label="Location"
        onClick={(e) => e.preventDefault()}
      />
      <Tab
        value="2"
        component="a"
        label="Zone"
        onClick={(e) => e.preventDefault()}
      />
      <Tab
        value="3"
        component="a"
        label="Type"
        onClick={(e) => e.preventDefault()}
      />
      <Tab
        value="4"
        component="a"
        label="City"
        onClick={(e) => e.preventDefault()}
      />
      <Tab
        value="5"
        component="a"
        label="Ward"
        onClick={(e) => e.preventDefault()}
      />
      <Tab
        value="6"
        component="a"
        label="FSI Rules"
        onClick={(e) => e.preventDefault()}
      />

    </TabList>
    <TabPanel value="1" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
      <Typography>{tabContent1}</Typography>
    </TabPanel>
    <TabPanel value="2" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
      <Typography>{tabContent2}</Typography>
    </TabPanel>
    <TabPanel value="3" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
      <Typography>{tabContent3}</Typography>
    </TabPanel>
    <TabPanel value="4" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
      <Typography>{tabContent4}</Typography>
    </TabPanel>
    <TabPanel value="5" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
      <Typography>{tabContent5}</Typography>
    </TabPanel>
    <TabPanel value="6" sx={{ pb: 1, pt: 3, px: { xs: "0", md: "0.5rem" } }}>
      <Typography>{tabContent6}</Typography>
    </TabPanel>
  </TabContext>

  );
}

export default NavTabFSI
