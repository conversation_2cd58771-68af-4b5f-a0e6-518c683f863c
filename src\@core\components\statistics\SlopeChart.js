import dynamic from 'next/dynamic';
import React from 'react';

// Import dynamically to handle server-side rendering issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const SlopeChart = () => {
  const state = {
    series: [
      {
        name: 'Pre-Development',
        data: [
          { x: 'Initial Evaluation', y: 300 },
          { x: 'Planning', y: 450 },
          { x: 'Approval', y: 400 },
        ],
      },
      {
        name: 'Construction',
        data: [
          { x: 'Initial Evaluation', y: 200 },
          { x: 'Planning', y: 600 },
          { x: 'Approval', y: 550 },
        ],
      },
      {
        name: 'Post-Development',
        data: [
          { x: 'Initial Evaluation', y: 400 },
          { x: 'Planning', y: 500 },
          { x: 'Approval', y: 700 },
        ],
      },
    ],
    options: {
      chart: {
        height: 350,
        width: '100%',
        type: 'line',
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true,
          },
          autoSelected: 'zoom',
        },
      },
      plotOptions: {
        line: {
          curve: 'smooth',
        },
      },
      tooltip: {
        x: {
          show: true,
          style: {
            fontSize: '10px',
          },
        },
      },
      dataLabels: {
        enabled: true,
        style: {
          fontSize: '10px',
        },
        formatter: (val, opts) => `${opts.w.config.series[opts.seriesIndex].name}: ${val}`,
      },
      yaxis: {
        title: {
          text: 'Values',
          style: {
            fontSize: '12px',
          },
        },
        labels: {
          style: {
            fontSize: '10px',
          },
        },
      },
      xaxis: {
        title: {
          text: 'Development Stages',
          style: {
            fontSize: '12px',
          },
        },
        categories: ['Initial Evaluation', 'Planning', 'Approval'],
        labels: {
          style: {
            fontSize: '10px',
          },
        },
      },
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        labels: {
          style: {
            fontSize: '10px',
          },
        },
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: 300
          },
          legend: {
            position: 'bottom'
          }
        }
      }],
    },
  };

  return (
    <div style={{ maxWidth: '100%' }}>
      <ApexChart
        options={state.options}
        series={state.series}
        type="line"
        height={350}
      />
    </div>
  );
};

export default SlopeChart;
