// ** React Imports
import { forwardRef, useContext, useState } from "react";

// ** MUI Imports
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import FormControl from "@mui/material/FormControl";
import { Box } from "@mui/system";

// ** Third Party Imports
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { Button } from "@mui/material";
import { useAuth } from "src/hooks/useAuth";
import { yupResolver } from "@hookform/resolvers/yup";
import SocietyValidationsSection1 from "./SocietyValidationsSection1";
import { AuthContext } from "src/context/AuthContext";



// ** Icon Imports

const defaultValues = {
  dob: null,
  email: "",
  radio: "",
  select: "",
  lastName: "",
  password: "",
  textarea: "",
  firstName: "",
  checkbox: false,
};

const CustomInput = forwardRef(({ ...props }, ref) => {
  return <TextField inputRef={ref} {...props} sx={{ width: "100%" }} />;
});

const Section4 = ({ onCancel, formData }) => {
  //Hooks
  const auth = useAuth();

  const {user} = useContext(AuthContext)

  const fields=["requirements_ExtraArea","requirements_Rent","requirements_Corpus","notes","leadGivenTo"]

  const {
    register,
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(SocietyValidationsSection1(fields)),
    mode: "onChange",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  async function submit(data) {
    setIsSubmitting(true)
    try {
      const trimmedData = Object.fromEntries(
        Object.entries(data).map(([key, value]) => [
          key,
          typeof value === "string" ? value.trim() : value,
        ])
      );
      const hasWhiteSpace = Object.values(trimmedData).some(
        (value) => typeof value === "string" && value === ""
      );
      if (hasWhiteSpace) {
        toast.error("Fields cannot contain only white spaces");
        return;
      }
  
      const userUniqueId =
        formData && formData.userId !== undefined ? formData?.userId : user?.id;
      const response = await auth.updateEntity(trimmedData, userUniqueId)
      onCancel();
      onCancel();
    } catch {
      console.error("Submission failed:", errors);
      toast.error("An error occurred while submitting the form.");
    } finally {
      setIsSubmitting(false)
    }
    
  }

  return (
    <>
      <Box sx={{ pt: 3 }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="requirements_ExtraArea"
                control={control}
                defaultValue={formData?.requirements_ExtraArea}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Extra Area"
                    type="text"
                    size='small'
                    placeholder="Additional area"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.requirements_ExtraArea)}
                    helperText={errors.requirements_ExtraArea?.message}
                    aria-describedby="validation-basic-requirements_ExtraArea"
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      inputMode: 'numeric', // only allow numeric input
                      maxLength: 10, // adjust the max length as needed
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="requirements_Rent"
                control={control}
                defaultValue={formData?.requirements_Rent}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    label="Rent"
                    size='small'
                    placeholder="rental amount"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.requirements_Rent)}
                    helperText={errors.requirements_Rent?.message}
                    aria-describedby="validation-basic-requirements_Rent"
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      inputMode: 'numeric', // only allow numeric input
                      maxLength: 10, // adjust the max length as needed
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="requirements_Corpus"
                control={control}
                defaultValue={formData?.requirements_Corpus}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="text"
                    size='small'
                    label="Corpus"
                    placeholder="corpus amount"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.requirements_Corpus)}
                    helperText={errors.requirements_Corpus?.message}
                    aria-describedby="validation-basic-requirements_Corpus"
                    onKeyPress={(event) => {
                      if (!/\d/.test(event.key)) {
                        event.preventDefault();
                      }
                    }}
                    inputProps={{
                      inputMode: 'numeric', // only allow numeric input
                      maxLength: 10, // adjust the max length as needed
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <Controller
                name="notes"
                control={control}
                defaultValue={formData?.notes}
                render={({ field }) => (
                  <TextField
                    rows={4}
                    multiline
                    {...field}
                    label="Notes"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.notes)}
                    aria-describedby="validation-basic-notes"
                    helperText={errors.notes?.message}
                    inputProps={{
                      title:'Enter any additional comments or notes here'
                    }}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <Controller
                name="leadGivenTo"
                control={control}
                defaultValue={formData?.leadGivenTo}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Lead Given To"
                    size='small'
                    InputLabelProps={{ shrink: true }}
                    placeholder="Lead Given To"
                    error={Boolean(errors.leadGivenTo)}
                    helperText={errors.leadGivenTo?.message}
                    aria-describedby="validation-basic-leadGivenTo"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <center>
              <Button
                size="medium"
                sx={{ mr: 3 }}
                variant="outlined"
                color="primary"
                onClick={() => onCancel()}
              >
                Cancel
              </Button>
              <Button
                size="medium"
                type="button"
                variant="contained"
                disabled={isSubmitting}
                onClick={handleSubmit(submit)}
              >
                Save
              </Button>
            </center>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Section4;
