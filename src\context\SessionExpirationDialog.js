import React from "react";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Button,
  Box,
} from "@mui/material";
import { useTheme } from '@mui/material/styles'

const SessionExpirationDialog = ({ open, onClose,msg }) => {
  const theme = useTheme();
  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.default : theme.palette.background.paper,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.mode === 'dark' ? theme.palette.divider : theme.palette.grey[300]}`,
            borderColor: "green",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              sx={{
                color: theme.palette.mode === 'dark' ? theme.palette.text.primary : theme.palette.text.secondary,
                color:"primary"
              }}
            >
              {msg}
            </DialogContentText>
          </DialogContent>
          <DialogActions
            sx={{
              display: "flex",
              justifyContent: "center",
              color:"white !important"
            }}
          >
            <Button
              variant="outlined !important"
              onClick={onClose}
              sx={{
                borderRadius: 1,
                border: (theme) => `1px solid ${theme.palette.mode === 'dark' ? theme.palette.divider : theme.palette.grey[300]}`,
                borderColor: "green",
                backgroundColor:"green !important",
                textTransform: 'none', // This ensures the text remains in lowercase
              }}
              style={{ margin: "10px auto", width: 80 }}
            >
              okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default SessionExpirationDialog;
