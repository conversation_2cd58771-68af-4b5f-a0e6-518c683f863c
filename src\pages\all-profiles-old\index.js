// ** MUI Imports
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Dialog,
  CircularProgress,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  Menu,
  MenuItem,
  TextField,
  Typography,
  FormHelperText,
  Tooltip,
  FormControlLabel,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import React, { useCallback, useContext, useEffect, useState } from "react";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";

import CompanyDetailsArchitect from "../SP/architect/sections/CompanyDetailsArchitect";

import TeamArchitect from "../SP/architect/sections/TeamArchitect";
import CompanyDetailsBroker from "../SP/broker/sections/CompanyDetailsBroker";
import ServicesBroker from "../SP/broker/sections/ServicesBroker";
import TeamBroker from "../SP/broker/sections/TeamBroker";
import BasicProfileCA from "../SP/charteredAccountant/sections/BasicProfileCA";
import ServicesCA from "../SP/charteredAccountant/sections/ServicesCA";

import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import { useMediaQuery } from "@mui/material";

import { Controller, useForm } from "react-hook-form";

import ContactsReferences from "../CHS/profile/sections/ContactsReferences";
import FsiDetails from "../CHS/profile/sections/FsiDetails";
import LandDetails from "../CHS/profile/sections/LandDetails";
import OtherDetails from "../CHS/profile/sections/OtherDetails";
import Requirements from "../CHS/profile/sections/Requirements";
import SocietyDetails from "../CHS/profile/sections/SocietyDetails";

import CompanyDetailsStructural from "src/pages/SP/structural/sections/CompanyDetailsStructural";
import ProjectDetailsStructural from "src/pages/SP/structural/sections/ProjectDetailsSructural";
import TeamDetailsStructural from "src/pages/SP/structural/sections/TeamDetailsStructural";

import axios from "axios";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import CompanyDetailsPmc from "../SP/pmc/sections/CompanyDetailsPmc";
import ProjectDetailsPmc from "../SP/pmc/sections/ProjectDetailsPmc";
import TeamDetailsPmc from "../SP/pmc/sections/TeamDetailsPmc";

// ** Component
// ** Config
import authConfig from "src/configs/auth";

import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";

import * as FileSaver from "file-saver";
import * as XLSX from "xlsx";
import CloseExpandIcons from "../../@core/components/custom-components/CloseExpandIcons";
import IconButton from "@mui/material/IconButton";

import NewUserDropDown from "./NewUserDropDown";
import { yupResolver } from "@hookform/resolvers/yup";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import AddNewEntityDrawer from "./AddNewEntityDrawer";
import * as yup from "yup";
import SelectCategory from "src/@core/components/custom-components/SelectCategory";
import ProjectDetailsArchitect from "../SP/architect/sections/ProjectDetailsArchitect";
import OtherServices from "src/@core/components/custom-components/OtherServices";
import Remarks from "src/@core/components/custom-components/Remarks";

import SpecializationView from "../SP/architect/sections/SpecializationView";
import ServicesView from "src/@core/components/custom-components/ServicesView";
import StructuralServices from "../SP/structural/sections/StructuralServices";

import StructuralOfferedView from "src/pages/SP/structural/sections/StructuralOfferedView";
import CompanyDetails from "../SP/legal/Sections/CompanyDetails";
import ProjectDetails from "../SP/legal/Sections/ProjectDetails";
import SpecializationsView from "../SP/legal/Sections/SpecializationsView";
import LegalServicesView from "../SP/legal/Sections/LegalServicesView";
import Team from "../SP/legal/Sections/Team";

import ShortFormView from "./ShortFormView";
import { useRouter } from "next/router";

import { useRBAC } from "src/pages/permission/RBACContext";

import { useAuth } from "src/hooks/useAuth";

import IntroductionFieldsView from "../SP/architect/sections/IntroductionFieldsView";
import MembershipView from "../SP/architect/sections/MembershipView";
import ServicesOfferedView from "../SP/architect/sections/ServicesOfferedView";
import ArchitecturalDesignView from "../SP/architect/sections/ArchitecturalDesignView";
import ArchitectAwardsView from "../SP/architect/sections/ArchitectAwardsView";
import ArchitectProjectView from "../SP/architect/sections/ArchitectProjectView";
import AreaOfExperties from "../SP/architect/sections/AreaOfExperties";
import ServicesArchitectView from "../SP/architect/sections/ServicesArchitectView";
import TestimonialView from "../SP/architect/sections/TestimonialView";
import EducationalInsightsView from "../SP/architect/sections/EducationalInsightsView";
import CustomAutocomplete from "src/@core/components/custom-components/CustomAutoComplete";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import CustomTextField from "src/@core/components/custom-components/CustomTextField";
import Checkbox from "@mui/material/Checkbox";

const entityTypeOptions = [
  {
    value: "INDIVIDUAL",
    key: "Individual",
  },
  {
    value: "TYPE_ENTITY",
    key: "Entity",
  },
];

const entityCategoryOptions = [
  {
    value: "ARCHITECT",
    key: "Architect",
  },
  {
    value: "BROKER",
    key: "Broker",
  },
  {
    value: "CHARTERED_ACCOUNTANT",
    key: "Chartered Accountant",
  },
  {
    value: "SOCIETY",
    key: "Society",
  },
  {
    value: "PMC",
    key: "PMC",
  },
  {
    value: "STRUCTURAL_ENGINEER",
    key: "Structural Engineer",
  },
  {
    value: "LEGAL",
    key: "Legal",
  },
];

const ExcelDownMenu = ({ children }) => {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleExcelClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Tooltip title="Export to Excel">
        <IconButton onClick={handleExcelClick} size="medium">
          <Icon icon="vscode-icons:file-type-excel" fontSize="2.2rem" />
        </IconButton>
      </Tooltip>

      <Menu
        keepMounted
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            marginTop: "4px",
          },
        }}
      >
        {children}
      </Menu>
    </>
  );
};

const AllProfiles = ({ onCancel }) => {
  const { can, rbacRoles } = useRBAC();

  const auth = useAuth();

  const [expanded, setExpanded] = useState(true);
  const router = useRouter();

  const handleToggle = (value) => {
    setExpanded(value);
  };

  const isMobile = useMediaQuery((theme) => theme.breakpoints.down("sm"));

  const [addUserOpen, setAddUserOpen] = useState(false);
  const toggleAddUserDrawer = () => setAddUserOpen(!addUserOpen);

  //Hooks
  const {
    profileEntityData,
    profileUser,
    setProfileUser,
    addNewMember,
    shortFormData,
    setShortFormData,
    user,
  } = useContext(AuthContext);

  const [emailId, setEmailId] = useState("");

  const [pleaseVerifyEmailMessage, setPleaseVerifyEmailMessage] =
    useState(false);

  const [disableVerifyEmailButton, setDisableVerifyEmailButton] =
    useState(false);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [showOTPOptions, setShowOTPOptions] = useState(false);
  const [currentRow, setCurrentRow] = useState();
  const [selectedReminderRow, setSelectedReminderRow] = useState();

  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [otp, setOTP] = useState("");
  const [countdown, setCountdown] = useState(0);

  //Reminder
  const [openRemindDialog, setOpenRemindDialog] = useState(false);

  const handleRemindCloseDialog = () => {
    setOpenRemindDialog(false);
    setMessageOptional("");
    setCalendarValue("");
    setSelectedEmail("");
  };

  useEffect(() => {
    const getEmployeesData = async () => {
      // Fetch EMPLOYEE Data
      await axios({
        method: "get",
        url: getUrl(authConfig.getAllEmployeesList),
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          console.log("EMPLOYEES DATA Response", res.data);
          setEmployeeList(res.data);
        })
        .catch((err) => console.log("EMPLOYEES DATA error", err));
    };
    getEmployeesData();
  }, []);

  async function submit(data) {
    setLoadingSubmit(true);

    if (!isEmailVerified) {
      const message = `
          <div>
          <h3>
          Email Address need to be verified
          </h3>
          </div>
        `;
      setDialogMessage(message);
      setPleaseVerifyEmailMessage(true);
      setLoadingSubmit(false);
    } else {
      setPleaseVerifyEmailMessage(false);
      const response = await addNewMember(data, () => {});
      setLoadingSubmit(false);
      handleClose();
      fetchUsers(page, pageSize, searchKeyword);
    }
    setIsEmailVerified(false);
  }

  useEffect(() => {
    if (showOTPOptions) {
      setOTP("");
    }
  }, [showOTPOptions]);

  const columns = [
    {
      flex: 0.13,
      minWidth: 160,
      field: "firstName",
      headerName: "First Name",
    },
    {
      flex: 0.13,
      minWidth: 80,
      field: "lastName",
      headerName: "Last Name",
      hide: isMobile ? true : "",
    },
    {
      flex: 0.16,
      field: "email",
      minWidth: 200,
      headerName: "Email Id",
    },
    {
      flex: 0.16,
      field: "mobileNumber",
      minWidth: 110,
      headerName: "Mobile Number",
    },
    {
      flex: 0.13,
      minWidth: 140,
      field: "entityCategory",
      headerName: "Category",
    },
    {
      flex: 0.13,
      minWidth: 120,
      field: "isEmpanelled",
      headerName: "Is Empanelled",
    },
    {
      flex: 0.14,
      minWidth: 115,
      field: "createdBy",
      headerName: "Created By",
      hide: isMobile ? true : "",
    },
    {
      flex: 0.14,
      minWidth: 115,
      field: "createdOn",
      headerName: "Created On",
    },
    can("allProfiles_UPDATE") && {
      flex: 0.077,
      field: "edit",
      headerName: "Edit",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const onClick = () => {
          const row = params.row;
          setShortFormData({
            ...shortFormData,
            id: row.id,
            firstName: row.firstName,
            lastName: row.lastName,
            mobileNumber: row.mobileNumber,
            email: row.email,
            entityId: row.entityId,
            entityCategory: row.entityCategory,
            entityType: row.entityType,
          });
          console.log("Edit ROW", row, row.entityCategory);
          setEntityCategory(row.entityCategory);
          renderForm();
          setProfileShow(true);
          setProfileUser({
            ...profileUser,
            entityId: row.entityId,
            entityCategory: row.entityCategory,
            entityType: row.entityType,
          });
        };
        const onClickReminder = () => {
          setOpenRemindDialog(true);
          const row = params.row;
          setSelectedReminderRow(params?.row);
          console.log("row", row);
          console.log("params", params);
          setCurrentRow(row);
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Edit or View">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={onClick}
              >
                <Icon icon="iconamoon:edit" />
              </CustomAvatar>
            </Tooltip>
            <Tooltip title="Reminder">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 0, width: 34, height: 34, cursor: "pointer" }}
                onClick={onClickReminder}
              >
                <Icon icon="tabler:bell-filled" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    if (!!shortFormData) {
      console.log("-------------SHORT FORM DATA.", shortFormData);
    }
  }, [shortFormData]);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!can("allProfiles_READ")) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  const [selectedColumns, setSelectedColumns] = useState(columns);

  const [userList, setUserList] = useState([]);
  const [employeeList, setEmployeeList] = useState([]);
  const [entityType, setEntityType] = useState("");
  const [name, setName] = useState("");

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [entityCategory, setEntityCategory] = useState("");
  const [email, setEmail] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [rowCount, setRowCount] = useState(0);
  const validateShortForm = yup.object().shape({
    firstName: yup
      .string()
      .required("First Name is required")
      .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
      .min(3, "First name must be atleast 3 characters")
      .max(30, "First Name must not exceed 30 characters"),

    lastName: yup
      .string()
      .required("Last Name is required")
      .matches(/^([A-Za-z]+ ?)*[A-Za-z]+$/, "multiple spaces are not allowed")
      .max(30, "Last Name must not exceed 30 characters"),

    email: yup
      .string()
      .required("Email address is required")
      .email("Please enter a valid email address"),

    mobileNumber: yup
      .string()
      .required("Mobile number is required")
      .test("isValidLength", "Invalid length", (value) => {
        if (value.startsWith("+91")) {
          return value.length >= 13;
        } else {
          return value.length >= 10;
        }
      })
      .matches(
        /^(?:\+91\s?)?[6-9]\d{9}$/,
        "Contact number should start with a digit in between 6 to 9"
      )
      .max(13, "Contact number must not exceed 13 characters"),
    entityType: yup.string().required("Please Select Entity Type"),
  });

  const {
    register,
    control,
    handleSubmit,
    setValue,
    reset,
    getValues,
    clearErrors,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validateShortForm),
    defaultValues: {
      firstName: "",
      lastName: "",
      mobileNumber: "",
      email: "",
      entityType: "",
    },
    mode: "onChange",
  });

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    if (!externalUserTypeId) {
      console.log("## externalUserTypeId is null fetchUsers not executed.");
      return;
    }
    setLoading(true);

    const url = getUrl(authConfig.getAllUsersEndpointNewTable);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      userType: externalUserTypeId,
    };

    console.log(`Fetching users from ${url} with params`, data);

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.users || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log(`Fetching data for page: ${page}, pageSize: ${pageSize}`);
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  async function submitReminder() {
    // handleRemindCloseDialog();
    try {
      console.log(
        "Submitted Data in reminder",
        selectedEmail,
        value,
        messageOptional,
        selectedReminderRow?.id,
        selectedReminderRow?.entityId
      );
      const date = new Date(value);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const dueDate = `${year}-${month}-${day}`;
      console.log("Formatted dueDate:", dueDate);

      const jsonBackend = {
        userId: selectedEmail?.id,
        entityId: selectedReminderRow?.entityId,
        email: selectedEmail?.email,
        dueDate: dueDate,
        message: messageOptional,
      };

      console.log("JSON TO BACKEND:", jsonBackend);

      const response = await axios({
        method: "post",
        url: getUrl(authConfig.reminderEndpoint),
        headers: getAuthorizationHeaders(),
        data: jsonBackend,
      });
      if (response.data) {
        const message = `
            <div>
              <h3>
              Reminder Set Success!
              </h3>
            </div>
          `;
        setDialogMessage(message);
        setSubmitSuccess(true);
      }
      handleRemindCloseDialog();

      return response.data;
    } catch (error) {
      console.error("Error submitting reminder:", error);
      const message = `
          <div>
            <h3>
            Reminder Failed! Please Select Field(s).
            </h3>
          </div>
        `;
      setDialogMessage(message);
      setSubmitSuccess(true);
      handleRemindCloseDialog();
    }
  }

  const [value, setCalendarValue] = useState();
  const [messageOptional, setMessageOptional] = useState("");

  const onChange = useCallback(
    (value) => {
      setCalendarValue(value);
    },
    [setCalendarValue]
  );

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [selectedRows, setSelectedRows] = React.useState([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingEmail, setLoadingEmail] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const [empanelDialog, setEmpanelDialog] = useState(false);

  const [strategicDialog, setStrategicDialog] = useState(false);

  const [strategicDialogOpen, setStrategicDialogOpen] = useState(false);

  const [isListingEmpanelled, setIsListingEmpanelled] = useState(false);
  const [isMicrositeEmpanelled, setIsMicrositeEmpanelled] = useState(false);

  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };
  const handleActionsClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const fetchIpAddress = async () => {
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();

      return data.ip;
    } catch (error) {
      console.error("Error fetching IP address:", error);

      return null;
    }
  };

  const [externalUserTypeId, setExternalUserTypeId] = useState(null);

  useEffect(() => {
    const getUserTypes = async () => {
      // Fetch User TYPE Data
      await axios({
        method: "get",
        url: getUrl(authConfig.getAllUserTypes),
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          res?.data?.forEach((element) => {
            if (element?.userType == "EXTERNAL") {
              setExternalUserTypeId(element.id);
            }
          });
        })
        .catch((err) => console.log("USER_TYPE_DATA error", err));
    };
    getUserTypes();
  }, []);

  useEffect(() => {
    if (!!externalUserTypeId) {
      fetchUsers(page, pageSize, searchKeyword);
    }
  }, [externalUserTypeId]);

  async function sendOTP(data) {
    const ipAddress = await fetchIpAddress();
    setPleaseVerifyEmailMessage(false);
    setLoadingEmail(true);
    const email = data?.email;
    const name = email ? email.split("@")[0] : "";
    setDisableVerifyEmailButton(true);

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpSendToEmailEndpoint),
      data: { name: name, email: email, ipAddress: ipAddress },
    })
      .then((res) => {
        if (res.data.isVerified) {
          const message = `
        <div>
        <h3>
        Email already exists!!!
        </h3>
        </div>
        `;
          setLoadingEmail(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
        } else {
          setEmail(data?.email);
          const message = `
            <div>
            <h3>
            OTP has been sent to your Email for verification. Please check.
            </h3>
            </div>
          `;
          setLoadingEmail(false);
          setDialogMessage(message);
          setSubmitSuccess(true);
          setCountdown(30);
          setShowOTPOptions(true);
        }
      })
      .catch((err) => {
        console.log("Error sendOTP: ", err);
        const message = `
          <div>
          <h3>
          Error sending OTP to your Email. Please try again.
          </h3>
          </div>
        `;
        setLoadingEmail(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
      });
  }

  async function verifyOtp(data) {
    setLoading(true);

    await axios({
      method: "POST",
      url: getUrl(authConfig.otpVerifyEndpoint) + "?isMember=false",
      data: {
        otpCode: otp,
        email: email,
      },
    })
      .then((response) => {
        setIsEmailVerified(true);
        setOTP("");
        setShowOTPOptions(false);
        setDisableVerifyEmailButton(false);

        const message = `
          <div>
          <h3>
          Email has been verified successfully.
          </h3>
          </div>
        `;
        setLoading(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
        window.localStorage.setItem("verifiedEmail", data?.email);
      })
      .catch((error) => {
        console.error("Error verifying OTP:", error);

        const message = `
          <div>
          <h3>
          OTP doesn't match. Please try again.
          </h3>
          </div>

        `;
        setLoading(false);
        setDialogMessage(message);
        setSubmitSuccess(true);
      });
  }

  const handleMessageClose = () => {
    setSubmitSuccess(false);
    setIsListingEmpanelled(false);
    setIsMicrositeEmpanelled(false);
    setPleaseVerifyEmailMessage(false);
  };

  const handleStrategicDialogClose = () => {
    setStrategicDialogOpen(false);
  };

  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);

      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  const userListMap = userList.reduce((map, row) => {
    map[row.id] = row;
    return map;
  }, {});

  const exportToCSV = (csvData, fileName) => {
    const message = ` 
    <div>
      <h3>The data have been exported successfully</h3>
    </div>
    `;
    setDialogMessage(message);
    setSubmitSuccess(true);

    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
    const fileExtension = ".xlsx";

    const filteredData = csvData.map((row) =>
      Object.keys(row)
        .filter((key) => selectedColumns.find((col) => col.field === key))
        .reduce((obj, key) => {
          obj[key] = row[key];
          return obj;
        }, {})
    );
    const ws = XLSX.utils.json_to_sheet(filteredData);

    const wb = { Sheets: { data: ws }, SheetNames: ["data"] };
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const data = new Blob([excelBuffer], { type: fileType });
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hours = String(now.getHours()).padStart(2, "0");
    const minutes = String(now.getMinutes()).padStart(2, "0");
    const seconds = String(now.getSeconds()).padStart(2, "0");

    const formattedDate = `${year}-${month}-${day}-${hours}-${minutes}-${seconds}`;

    const newFileName = `${fileName}_${formattedDate}${fileExtension}`;

    FileSaver.saveAs(data, newFileName);
  };

  const handleColumnSelection = (columnField) => {
    setSelectedColumns((prevState) =>
      prevState.find((col) => col.field === columnField)
        ? prevState.filter((col) => col.field !== columnField)
        : [...prevState, columns.find((col) => col.field === columnField)]
    );
  };

  const ArchitectForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>
        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={expanded} />
            </Grid>

            <Grid item xs={12}>
              <IntroductionFieldsView
                data={profileEntityData}
                expanded={expanded}
              ></IntroductionFieldsView>
            </Grid>
            <Grid item xs={12}>
              <CompanyDetailsArchitect
                data={profileEntityData}
                expanded={expanded}
              />
            </Grid>
            <Grid item xs={12}>
              <TeamArchitect data={profileEntityData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <ProjectDetailsArchitect
                data={profileEntityData}
                expanded={expanded}
              ></ProjectDetailsArchitect>
            </Grid>
            <Grid item xs={12}>
              <MembershipView
                data={profileEntityData}
                expanded={expanded}
              ></MembershipView>
            </Grid>
            <Grid item xs={12}>
              <ServicesOfferedView
                data={profileEntityData}
                expanded={expanded}
              ></ServicesOfferedView>
            </Grid>
            <Grid item xs={12}>
              <ArchitecturalDesignView
                data={profileEntityData}
                expanded={expanded}
              ></ArchitecturalDesignView>
            </Grid>
            <Grid item xs={12}>
              <SpecializationView
                data={profileEntityData}
                expanded={expanded}
              ></SpecializationView>
            </Grid>
            <Grid item xs={12}>
              <ArchitectAwardsView
                data={profileEntityData}
                expanded={expanded}
              ></ArchitectAwardsView>
            </Grid>

            <Grid item xs={12}>
              <ArchitectProjectView
                data={profileEntityData}
                expanded={expanded}
              ></ArchitectProjectView>
            </Grid>

            <Grid item xs={12}>
              <ServicesView
                data={profileEntityData}
                expanded={expanded}
                readPermission={"architect_services_READ"}
                permission={"architect_services_UPDATE"}
              ></ServicesView>
            </Grid>
            <Grid item xs={12}>
              <AreaOfExperties
                data={profileEntityData}
                expanded={expanded}
              ></AreaOfExperties>
            </Grid>
            <Grid item xs={12}>
              <ServicesArchitectView
                data={profileEntityData}
                expanded={expanded}
              ></ServicesArchitectView>
            </Grid>

            <Grid item xs={12}>
              <TestimonialView
                data={profileEntityData}
                expanded={expanded}
              ></TestimonialView>
            </Grid>
            <Grid item xs={12}>
              <EducationalInsightsView
                data={profileEntityData}
                expanded={expanded}
              ></EducationalInsightsView>
            </Grid>
            <Grid item xs={12}>
              <OtherServices
                data={profileEntityData}
                expanded={expanded}
                readPermission={"architect_otherServices_READ"}
                permission={"architect_otherServices_UPDATE"}
              ></OtherServices>
            </Grid>
            <Grid item xs={12}>
              <Remarks
                data={profileEntityData}
                expanded={expanded}
                readPermission={"architect_remarks_READ"}
                permission={"architect_remarks_UPDATE"}
              ></Remarks>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const BrokerForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>
        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={true} />
            </Grid>
            <Grid item xs={12}>
              <CompanyDetailsBroker
                data={profileEntityData}
                expanded={expanded}
              />
            </Grid>
            <Grid item xs={12}>
              <TeamBroker data={profileEntityData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <ServicesBroker data={profileEntityData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <OtherServices
                data={profileEntityData}
                expanded={expanded}
                readPermission={"broker_otherServices_READ"}
                permission={"broker_otherServices_UPDATE"}
              ></OtherServices>
            </Grid>
            <Grid item xs={12}>
              <Remarks
                data={profileEntityData}
                expanded={expanded}
                readPermission={"broker_remarks_READ"}
                permission={"broker_remarks_UPDATE"}
              ></Remarks>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const CAForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>
        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <BasicProfileCA data={profileEntityData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <ServicesCA data={profileEntityData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <OtherServices
                data={profileEntityData}
                expanded={expanded}
                readPermission={"ca_otherServices_READ"}
                permission={"ca_otherServices_UPDATE"}
              ></OtherServices>
            </Grid>
            <Grid item xs={12}>
              <Remarks
                data={profileEntityData}
                expanded={expanded}
                readPermission={"ca_remarks_READ"}
                permission={"ca_remarks_UPDATE"}
              ></Remarks>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const SocietyForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>

        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <SocietyDetails
                data={profileEntityData}
                expanded={expanded}
              ></SocietyDetails>
            </Grid>
            <Grid item xs={12}>
              <LandDetails
                data={profileEntityData}
                expanded={expanded}
              ></LandDetails>
            </Grid>
            <Grid item xs={12}>
              <FsiDetails
                data={profileEntityData}
                expanded={expanded}
              ></FsiDetails>
            </Grid>
            <Grid item xs={12}>
              <Requirements
                data={profileEntityData}
                expanded={expanded}
              ></Requirements>
            </Grid>
            <Grid item xs={12}>
              <ContactsReferences
                data={profileEntityData}
                expanded={expanded}
              ></ContactsReferences>
            </Grid>
            <Grid item xs={12}>
              <OtherDetails
                data={profileEntityData}
                expanded={expanded}
              ></OtherDetails>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const StructuralForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>
        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <CompanyDetailsStructural
                data={profileEntityData}
                expanded={expanded}
              ></CompanyDetailsStructural>
            </Grid>
            <Grid item xs={12}>
              <TeamDetailsStructural
                data={profileEntityData}
                expanded={expanded}
              ></TeamDetailsStructural>
            </Grid>
            <Grid item xs={12}>
              <ProjectDetailsStructural
                data={profileEntityData}
                expanded={expanded}
              ></ProjectDetailsStructural>
            </Grid>
            <Grid item xs={12}>
              <StructuralOfferedView
                data={profileEntityData}
                expanded={expanded}
              ></StructuralOfferedView>
            </Grid>
            <Grid item xs={12}>
              <ServicesView
                data={profileEntityData}
                expanded={expanded}
                readPermission={"structuralEngineer_services_READ"}
                permission={"structuralEngineer_services_UPDATE"}
              ></ServicesView>
            </Grid>{" "}
            <Grid item xs={12}>
              <StructuralServices
                data={profileEntityData}
                expanded={expanded}
              ></StructuralServices>
            </Grid>
            <Grid item xs={12}>
              <OtherServices
                data={profileEntityData}
                expanded={expanded}
                readPermission={"structuralEngineer_otherServices_READ"}
                permission={"structuralEngineer_otherServices_UPDATE"}
              ></OtherServices>
            </Grid>
            <Grid item xs={12}>
              <Remarks
                data={profileEntityData}
                expanded={expanded}
                readPermission={"structuralEngineer_remarks_READ"}
                permission={"structuralEngineer_remarks_UPDATE"}
              ></Remarks>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const PMCForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>
        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <CompanyDetailsPmc
                data={profileEntityData}
                expanded={expanded}
              ></CompanyDetailsPmc>
            </Grid>

            <Grid item xs={12}>
              <TeamDetailsPmc
                data={profileEntityData}
                expanded={expanded}
              ></TeamDetailsPmc>
            </Grid>

            <Grid item xs={12}>
              <ProjectDetailsPmc
                data={profileEntityData}
                expanded={expanded}
              ></ProjectDetailsPmc>
            </Grid>
            <Grid item xs={12}>
              <ServicesView
                data={profileEntityData}
                expanded={expanded}
                read={"pmc_services_READ"}
                permission={"pmc_services_UPDATE"}
              ></ServicesView>
            </Grid>
            <Grid item xs={12}>
              <OtherServices
                data={profileEntityData}
                expanded={expanded}
                readPermission={"pmc_otherServices_READ"}
                permission={"pmc_otherServices_UPDATE"}
              ></OtherServices>
            </Grid>
            <Grid item xs={12}>
              <Remarks
                data={profileEntityData}
                expanded={expanded}
                readPermission={"pmc_remarks_READ"}
                permission={"pmc_remarks_UPDATE"}
              ></Remarks>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const LegalForm = () => {
    return (
      <>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
            cursor: pointer
          }
       `}
        </style>
        <DatePickerWrapper>
          <Grid
            container
            spacing={6}
            sx={{
              "& .MuiPaper-root.MuiAccordion-root": { margin: "0 !important" },
            }}
          >
            <Grid item xs={12}>
              <ShortFormView data={shortFormData} expanded={expanded} />
            </Grid>
            <Grid item xs={12}>
              <CompanyDetails
                data={profileEntityData}
                expanded={expanded}
              ></CompanyDetails>
            </Grid>
            <Grid item xs={12}>
              <Team data={profileEntityData} expanded={expanded}></Team>
            </Grid>
            <Grid item xs={12}>
              <ProjectDetails
                data={profileEntityData}
                expanded={expanded}
              ></ProjectDetails>
            </Grid>
            <Grid item xs={12}>
              <LegalServicesView
                data={profileEntityData}
                expanded={expanded}
              ></LegalServicesView>
            </Grid>
            <Grid item xs={12}>
              <SpecializationsView
                data={profileEntityData}
                expanded={expanded}
              ></SpecializationsView>
            </Grid>
            <Grid item xs={12}>
              <OtherServices
                data={profileEntityData}
                expanded={expanded}
                readPermission={"legal_otherServices_READ"}
                permission={"legal_otherServices_UPDATE"}
              ></OtherServices>
            </Grid>
          </Grid>
        </DatePickerWrapper>
      </>
    );
  };

  const totalGridHeight = pageSize * 52 + 80;

  const [selectedOption, setSelectedOption] = useState("");

  const [showForm, setShowForm] = useState(false);

  const [profileShow, setProfileShow] = useState(false);

  const [isFormOpen, setIsFormOpen] = useState(false);

  const handleOptionChange = (value) => {
    setEntityCategory(value);
    setShowForm(true);
  };

  const emailOptions = employeeList.map((employee) => ({
    email: employee?.email,
    id: employee?.id,
  }));

  const [selectedEmail, setSelectedEmail] = useState(null);

  const handleEmailChange = (event, newValue) => {
    setSelectedEmail(newValue);
  };

  const handleClose = () => {
    setPleaseVerifyEmailMessage(false);
    setDisableVerifyEmailButton(false);
    setShowForm(false);
    setShowOTPOptions(false);

    reset({
      firstName: "",
      lastName: "",
      mobileNumber: "",
      email: "",
      entityType: "",
    });
    setEntityType("");
  };

  const handleProfileClose = () => {
    setProfileShow(false);
    fetchUsers(page, pageSize, searchKeyword);
  };

  const renderForm = () => {
    switch (entityCategory) {
      case "ARCHITECT":
        return <ArchitectForm />;
      case "BROKER":
        return <BrokerForm />;
      case "CHARTERED_ACCOUNTANT":
        return <CAForm />;
      case "SOCIETY":
        return <SocietyForm />;
      case "PMC":
        return <PMCForm />;
      case "STRUCTURAL_ENGINEER":
        return <StructuralForm />;
      case "LEGAL":
        return <LegalForm />;
      default:
        return null;
    }
  };

  if (can("allProfiles_READ")) {
    const handleYes = async () => {
      setSubmitSuccess(false);
      let successFlag = true;
      let allAlreadyEmpanelled = true;
      const successIds = [];
      const failedIds = [];

      const updatePromises = selectedRows.map((row) => {
        if (!row.isListingEmpanelled || !row.isMicrositeEmpanelled) {
          allAlreadyEmpanelled = false;
          const fields = {
            userId: row.id,
            isListingEmpanelled: isListingEmpanelled,
            isMicrositeEmpanelled: isMicrositeEmpanelled,
          };

          return auth
            .updateIsEmpanelled(fields)
            .then((response) => {
              console.log(`API call successful for userId: ${row.id}`);
              successIds.push(row.id); // Add to success list
              return response;
            })
            .catch((error) => {
              console.error(`API call failed for userId: ${row.id}`, error);
              failedIds.push(row.id); // Add to failed list
              successFlag = false;
            });
        }
      });

      setEmpanelDialog(false);
      await Promise.all(updatePromises);

      let message = `<div><h3>`;
      if (allAlreadyEmpanelled) {
        message += `All Selected Rows are Already Empanelled!`;
      } else if (successFlag) {
        message += `Empanelled success for ${successIds.length} user(s).`;
        if (failedIds.length > 0) {
          message += ` Failed for ${failedIds.length} user(s).`;
        }
      } else {
        message += `Empanelment failed for ${failedIds.length} user(s).`;
      }
      message += `</h3></div>`;

      setDialogMessage(message);
      setSubmitSuccess(true);
      setSelectedRows([]);

      fetchUsers(page, pageSize, searchKeyword);
    };

    const handleNo = async () => {
      setSubmitSuccess(false);
      let successFlag = true;
      let allAlreadyUnEmpanelled = true;
      const successIds = [];
      const failedIds = [];

      const updatePromises = selectedRows.map((row) => {
        if (row.isListingEmpanelled || row.isMicrositeEmpanelled) {
          allAlreadyUnEmpanelled = false;
          const fields = {
            userId: row.id,
            isListingEmpanelled: false,
            isMicrositeEmpanelled: false,
          };

          return auth
            .updateIsEmpanelled(fields)
            .then((response) => {
              console.log(`API call successful for userId: ${row.id}`);
              successIds.push(row.id); // Add to success list
              return response;
            })
            .catch((error) => {
              console.error(`API call failed for userId: ${row.id}`, error);
              failedIds.push(row.id); // Add to failed list
              successFlag = false;
            });
        }
      });

      setEmpanelDialog(false);
      await Promise.all(updatePromises);

      let message = `<div><h3>`;
      if (allAlreadyUnEmpanelled) {
        message += `All Selected Rows are Already UnEmpanelled!`;
      } else if (successFlag) {
        message += `Unempanelled success for ${successIds.length} user(s).`;
        if (failedIds.length > 0) {
          message += ` Failed for ${failedIds.length} user(s).`;
        }
      } else {
        message += `Unempanelment failed for ${failedIds.length} user(s).`;
      }
      message += `</h3></div>`;

      setDialogMessage(message);
      setSubmitSuccess(true);
      setSelectedRows([]);

      fetchUsers(page, pageSize, searchKeyword);
    };

    const handleIsEmpanelled = async (selectedRows) => {
      const message = `
         <div>
           <h3>
Please select the page(s) to set the status. </h3>
 <p>The status will be applied to the selected row(s)</p>
          
         </div>
       `;
      setDialogMessage(message);
      setSubmitSuccess(true);
      setEmpanelDialog(true);
    };

    const handleIsStrategicPartner = async () => {
      const message = `
      <div>
        <h3>Confirm Action</h3>
        <p>Applying "Strategic Partner" status will impact all selected row(s).</p>
      </div>
    `;

      setDialogMessage(message);
      setStrategicDialog(true);
      setStrategicDialogOpen(true);
    };

    const handleAssignStrategic = async () => {
      setStrategicDialogOpen(false);
      let successFlag = true;
      let allAlreadyStrategicPartnered = true;
      const updatePromises = selectedRows.map((row) => {
        if (!row.isStrategicPartner) {
          allAlreadyStrategicPartnered = false;
          const fields = {
            userId: row.id,
            isStrategicPartner: true,
          };

          // Return the update promise
          return auth
            .updateIsStrategicPartner(fields)
            .then((response) => {
              console.log(`API call successful for userId: ${row.id}`);
              console.log("API call successful response", response);
              return response; // This will be used to check if at least one call was successful
            })
            .catch((error) => {
              successFlag = false;
              console.error("Employee Details failed", error);
            });
        }
      });

      setStrategicDialog(false);

      const responses = await Promise.all(updatePromises);

      if (allAlreadyStrategicPartnered) {
        const message = `<div> <h3>Action Not Required</h3><p>Selected entities are already strategic partners.</p></div>
        `;

        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else if (successFlag) {
        const message = `<div><h3>Update Successful</h3><p>Status updated to strategic partner.</p></div>

      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else {
        const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>      
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
      }

      if (responses.some((response) => response !== null)) {
        fetchUsers(page, pageSize, searchKeyword);
      }
    };

    const handleRemoveStrategic = async () => {
      setStrategicDialogOpen(false);
      let successFlag = true;
      let allAlreadyStrategicPartnered = true;
      const updatePromises = selectedRows.map((row) => {
        if (row.isStrategicPartner) {
          allAlreadyStrategicPartnered = false;
          const fields = {
            userId: row.id,
            isStrategicPartner: false,
          };

          // Return the update promise
          return auth
            .updateIsStrategicPartner(fields)
            .then((response) => {
              console.log(`API call successful for userId: ${row.id}`);
              console.log("API call successful response", response);
              return response; // This will be used to check if at least one call was successful
            })
            .catch((error) => {
              successFlag = false;
              console.error("Employee Details failed", error);
            });
        }
      });

      setStrategicDialog(false);

      const responses = await Promise.all(updatePromises);

      if (allAlreadyStrategicPartnered) {
        const message = `<div>
        <h3>Action Not Required</h3>
        <p>No changes were made as the selected entity or entities are not designated as strategic partners.</p>
      </div>`;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else if (successFlag) {
        const message = `<div>
        <h3>Update Successful</h3>
        <p>The strategic partner status has been successfully removed.</p>
      </div>
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
        setSelectedRows([]);
      } else {
        const message = `<div>
        <h3>Update Unsuccessful</h3>
        <p>The system encountered an issue while updating the status. Please try again.</p>
      </div>
      `;
        setDialogMessage(message);
        setStrategicDialogOpen(true);
      }

      if (responses.some((response) => response !== null)) {
        fetchUsers(page, pageSize, searchKeyword);
      }
    };

    const handleSelection = (selectionModel) => {
      // Assuming `userList` is an array of objects and each object has a unique `id` that corresponds to the `selectionModel`
      const selectedData = userList.filter((row) =>
        selectionModel.includes(row.id)
      );
      console.log(selectedData);
      setSelectedRows(selectedData);
    };

    return (
      <>
        {can("allProfiles_READ") && (
          <Grid>
            <Card>
              <Box
                sx={{
                  py: 3,
                  px: 6,
                  rowGap: 2,
                  columnGap: 4,
                  display: "flex",
                  flexWrap: "wrap",
                  alignItems: "center",
                  // justifyContent: "space-between",
                }}
              >
                <Typography variant="h6" sx={{ mb: { xs: 2, sm: 0 ,md:0} }}>
                  All Profiles
                </Typography>
                <Grid item xs={3}  sx={{ ml: { lg: 4, xs: 0,md:0,sm:0 } }}>
                  <Grid>
                    <AddNewEntityDrawer
                      open={addUserOpen}
                      toggle={toggleAddUserDrawer}
                      searchKeyword={searchKeyword}
                      setSearchKeyword={setSearchKeyword}
                      fetchUsers={fetchUsers}
                      page={page}
                      pageSize={pageSize}
                    />
                  </Grid>
                </Grid>
                <Grid sx={{ ml: { xs: 12,lg: 48,sm:12,md:12,lg:48 }, mr: 0 }}>
                  <ExcelDownMenu>
                    {columns.map((column) => (
                      <MenuItem key={column.field}>
                        {column.headerName === "Edit" ? null : (
                          <label>
                            <input
                              type="checkbox"
                              checked={
                                !!selectedColumns.find(
                                  (col) => col.field === column.field
                                )
                              }
                              onChange={() =>
                                handleColumnSelection(column.field)
                              }
                            />
                            {column.headerName}
                          </label>
                        )}
                      </MenuItem>
                    ))}
                    <Box sx={{ textAlign: "center", margin: 2 }}>
                      <Button
                        size="medium"
                        type="button"
                        variant="contained"
                        onClick={() => exportToCSV(userList, "users_data")}
                        disabled={selectedColumns.length === 0}
                      >
                        Download
                      </Button>
                    </Box>
                  </ExcelDownMenu>
                </Grid>
                <Box>
                  <Button
                    size="medium"
                    type="button"
                    variant="contained"
                    onClick={handleActionsClick}
                  >
                    Actions
                  </Button>
                  <Menu
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleCloseMenu}
                  >
                    <Tooltip
                      title="Please select the profiles to opt the options"
                      disableHoverListener={selectedRows.length !== 0}
                    >
                      <span>
                        <MenuItem
                          onClick={() => {
                            handleClose();
                            handleIsEmpanelled(selectedRows);
                          }}
                          disabled={selectedRows.length === 0}
                        >
                          Empanel/UnEmpanel
                        </MenuItem>
                      </span>
                    </Tooltip>
                    <Tooltip
                      title="Please select the profiles to enable the options"
                      disableHoverListener={selectedRows.length !== 0}
                    >
                      <span>
                        <MenuItem
                          onClick={() => {
                            handleClose();
                            handleIsStrategicPartner(selectedRows);
                          }}
                          disabled={selectedRows.length === 0}
                        >
                          Houzer Strategic Partner
                        </MenuItem>
                      </span>
                    </Tooltip>
                  </Menu>
                </Box>
                <Box
                  sx={{
                    rowGap: 2,
                    display: "flex",
                    flexWrap: "wrap",
                    alignItems: "center",
                    "& :not(:last-child)": { mr: 1},
                  }}
                >
                  {/* Add new profile Offcanvas-Drawer Component */}

                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    {can("allProfiles_CREATE") && (
                      <NewUserDropDown
                        register={register}
                        id="entityCategory"
                        nameArray={entityCategoryOptions}
                        setShowForm={setShowForm}
                        setValue={setValue}
                        setEntityCategory={setEntityCategory}
                        getValues={getValues}
                      />
                    )}
                  </Box>
                </Box>
                <Grid container spacing={6} className="match-height"></Grid>
              </Box>
              <Divider />
              <CardContent
                sx={{
                  p: (theme) => `${theme.spacing(4, 2)} !important`,
                }}
              >
                <div style={{ height: 380, width: "100%" }}>
                  <DataGrid
                    rows={userList}
                    columns={columns}
                    
                    checkboxSelection
                    pagination
                    pageSize={pageSize}
                    page={page - 1}
                    rowsPerPageOptions={rowsPerPageOptions}
                    rowCount={rowCount}
                    paginationMode="server"
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    onSelectionModelChange={handleSelection}
                    rowHeight={38}
                    headerHeight={38}
                  />
                </div>
              </CardContent>
            </Card>

            <Dialog
              fullWidth
              maxWidth="md"
              scroll="paper"
              open={showForm}
              //disableEscapeKeyDown
              onClose={(event, reason) => {
                if (reason !== "backdropClick") {
                  handleClose();
                }
              }}
            >
              {/* Add New User Short From Dialog box. */}
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.5)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
                textAlign={"center"}
                fontSize={"30px !important"}
                fontWeight={"bold"}
              >
                {entityCategory === "ARCHITECT"
                  ? "Add Architect"
                  : entityCategory === "BROKER"
                  ? "Add Broker "
                  : entityCategory === "CHARTERED_ACCOUNTANT"
                  ? "Add Chartered Accountant"
                  : entityCategory === "SOCIETY"
                  ? "Add Society "
                  : entityCategory === "PMC"
                  ? "Add PMC "
                  : entityCategory === "STRUCTURAL_ENGINEER"
                  ? "Add Structural Engineer "
                  : entityCategory === "LEGAL"
                  ? "Add Legal Advisor "
                  : null}
                <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
                  <IconButton
                    size="small"
                    onClick={handleClose}
                    sx={{
                      // p: "0.438rem",
                      borderRadius: 1,
                      color:"common.white", 
                      backgroundColor: "primary.main",
                      "&:hover": {
                        backgroundColor: 
                        '#66BB6A',
                         transition: 'background 0.5s ease, transform 0.5s ease',                       
                        },
                    }}
                  >
                    <Icon icon="tabler:x" fontSize="1rem" />
                  </IconButton>
                </Box>
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  p: (theme) => `${theme.spacing(10, 8)} !important`,
                }}
              >
                <Grid container spacing={3} alignItems={"center"}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="firstName"
                        control={control}
                        render={({ field }) => (
                          <NameTextField
                            id="firstName"
                            label="First Name"
                            placeholder="Enter First Name"
                            inputProps={{ maxLength: 30 }}
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.firstName)}
                            helperText={errors.firstName?.message}
                            {...field}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="lastName"
                        control={control}
                        render={({ field }) => (
                          <NameTextField
                            id="lastName"
                            label="Last Name"
                            placeholder="Enter Last Name"
                            inputProps={{ maxLength: 30 }}
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.lastName)}
                            helperText={errors.lastName?.message}
                            {...field}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <Controller
                        name="mobileNumber"
                        control={control}
                        render={({ field }) => (
                          <MobileNumberValidation
                            {...field}
                            type="tel"
                            label="Mobile Number"
                            error={Boolean(errors.mobileNumber)}
                            helperText={errors.mobileNumber?.message}
                            InputLabelProps={{ shrink: true }}
                            placeholder="+91 1234567890"
                            inputProps={{
                              maxLength: field.value.startsWith("+91")
                                ? 13
                                : 10,
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <SelectCategory
                      register={register}
                      id="entityType"
                      label="Entity Type"
                      name="entityType"
                      nameArray={entityTypeOptions}
                      value={entityType}
                      defaultValue={entityType}
                      onChange={(e) => setEntityType(e.target.value)}
                      clearErrors={clearErrors}
                    />
                    {errors.entityType && (
                      <FormHelperText
                        sx={{ color: "error.main" }}
                        id="validation-entityType"
                      >
                        {errors.entityType?.message}
                      </FormHelperText>
                    )}
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    {!isEmailVerified && (
                      <>
                        <FormControl fullWidth>
                          <Controller
                            name="email"
                            control={control}
                            render={({ field }) => (
                              <EmailTextField
                                {...field}
                                type="email"
                                label="Email address"
                                error={Boolean(errors.email)}
                                inputProps={{ maxLength: 50 }}
                                InputLabelProps={{ shrink: true }}
                                placeholder="Enter email address"
                                helperText={errors.email?.message}
                              />
                            )}
                          />
                        </FormControl>
                      </>
                    )}
                    {isEmailVerified && (
                      <>
                        <FormControl fullWidth>
                          <div
                            style={{ display: "flex", alignItems: "baseline" }}
                          >
                            <Typography
                              sx={{ marginBottom: 0, fontWeight: "bold" }}
                            >
                              Email:
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                marginRight: "4px",
                                marginLeft: "6px",
                              }}
                            >
                              {email}
                            </Typography>
                          </div>
                        </FormControl>

                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <CheckCircleOutlineIcon
                            sx={{
                              color: "green",
                              marginBottom: "-10px",
                              paddingBottom: 2,
                              marginLeft: "55px",
                            }}
                          />
                          <Typography
                            sx={{
                              marginLeft: "6px",
                              marginBottom: "-10px",
                              paddingBottom: 2,
                            }}
                          >
                            Verified
                          </Typography>
                        </Box>
                      </>
                    )}
                  </Grid>
                  <Grid item xs={12} sm={6} sx={{ alignSelf: "start" }}>
                    {!isEmailVerified && (
                      <>
                        <Button
                          display="flex"
                          justifyContent="center"
                          color="primary"
                          variant="contained"
                          onClick={handleSubmit(sendOTP)}
                          disabled={disableVerifyEmailButton}
                          sx={{
                            px: { lg: 3.25 },
                            py: "9px",
                            marginTop: { lg: "8px" },
                          }}
                        >
                          {loadingEmail ? (
                            <CircularProgress color="inherit" size={22} />
                          ) : (
                            "Verify Email"
                          )}
                        </Button>
                      </>
                    )}
                  </Grid>
                </Grid>
                <Grid container spacing={4} alignItems="center">
                  {showOTPOptions && (
                    <>
                      <Grid item xs={12} sm={3.5}>
                        <TextField
                          type="text"
                          inputProps={{
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                          }}
                          placeholder="Email OTP"
                          value={otp}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^\d*$/.test(value) && value.length <= 6) {
                              setOTP(value);
                            }
                          }}
                          InputLabelProps={{ shrink: true }}
                          error={Boolean(errors?.otp)}
                          helperText={errors?.otp?.message}
                          fullWidth
                          sx={{
                            "& .MuiInputBase-input.MuiOutlinedInput-input": {
                              padding: "10px",
                            },
                            borderRadius: "5px",
                            marginTop: 4,
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={5} sx={{ display: "flex" }}>
                        <Button
                          color="primary"
                          variant="contained"
                          disabled={!otp || Boolean(errors?.otp)}
                          onClick={verifyOtp}
                          sx={{
                            whiteSpace: "nowrap",
                            px: { lg: 5.55 },
                            py: { lg: "9.75px" },
                            marginTop: { lg: "14px" },
                            mr: { xs: 1.5, lg: 4 },
                            "&:disabled": { color: "primary.main" },
                          }}
                        >
                          {loading ? (
                            <CircularProgress color="inherit" size={22} />
                          ) : (
                            "VALIDATE OTP"
                          )}
                        </Button>
                        <Button
                          variant={countdown > 0 ? "outlined" : "contained"}
                          disabled={countdown > 0}
                          color="primary"
                          onClick={() => sendOTP({ email })}
                          sx={{
                            py: { lg: "9.75px" },
                            whiteSpace: "nowrap",
                            marginTop: { lg: "14px" },
                            minHeight: "10px",
                            "&:disabled": { backgroundColor: "white" },
                          }}
                        >
                          {loading ? (
                            <CircularProgress color="inherit" size={22} />
                          ) : (
                            "RESEND OTP"
                          )}
                        </Button>
                      </Grid>

                      {countdown > 0 && (
                        <Grid item xs={12} sm={3}>
                          <Typography
                            variant="body1"
                            sx={{
                              marginTop: { lg: "10px" },
                              marginLeft: { lg: "-10px" },
                              color: "primary.main",
                            }}
                          >
                            Resend OTP in: {countdown}s
                          </Typography>
                        </Grid>
                      )}
                    </>
                  )}
                </Grid>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "center",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                }}
              >
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="outlined"
                  color="primary"
                  onClick={handleClose}
                >
                  Cancel
                </Button>
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="contained"
                  color="primary"
                  onClick={handleSubmit(submit)}
                  disabled={loadingSubmit}
                >
                  {loadingSubmit ? (
                    <CircularProgress color="inherit" size={24} />
                  ) : (
                    "Submit"
                  )}
                </Button>
              </DialogActions>
            </Dialog>

            <Dialog
              open={submitSuccess || pleaseVerifyEmailMessage}
              aria-labelledby="alert-dialog-title"
              aria-describedby="alert-dialog-description"
              PaperProps={{
                sx: {
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                  backgroundColor: (theme) => theme.palette.primary.background,
                },
              }}
              onClose={(event, reason) => {
                if (reason === "backdropClick") {
                  handleMessageClose();
                }
              }}
            >
              <Box
                sx={{
                  width: "100%",
                  borderRadius: 1,
                  textAlign: "center",
                  border: (theme) => `1px solid ${theme.palette.divider}`,
                  borderColor: "primary.main",
                }}
              >
                <DialogContent>
                  <DialogContentText
                    id="alert-dialog-description"
                    color="primary.main"
                  >
                    <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
                  </DialogContentText>
                </DialogContent>
                <DialogActions
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  {empanelDialog ? (
                    <div>
                      <Box
                        sx={{
                          backgroundColor: "white",
                          padding: 2,
                          borderRadius: "8px",
                          mb: { xs: 2, lg: 4 },
                          paddingLeft: 6,
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={isListingEmpanelled}
                              onChange={(event) =>
                                setIsListingEmpanelled(event.target.checked)
                              }
                            />
                          }
                          label={
                            <span style={{ color: "black" }}>Listing Page</span>
                          }
                        />

                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={isMicrositeEmpanelled}
                              onChange={(event) =>
                                setIsMicrositeEmpanelled(event.target.checked)
                              }
                            />
                          }
                          label={
                            <span style={{ color: "black" }}>
                              Microsite Page
                            </span>
                          }
                        />
                      </Box>
                      <Button
                        variant="contained"
                        onClick={handleYes}
                        sx={{
                          margin: "10px",
                          backgroundColor: "primary.main",
                          "&:disabled": {
                            backgroundColor: "white",
                            color: "grey",
                          },
                        }}
                        disabled={
                          !isListingEmpanelled && !isMicrositeEmpanelled
                        }
                      >
                        Empanel
                      </Button>
                      <Button
                        variant="contained"
                        onClick={handleNo}
                        sx={{
                          margin: "10px",
                          backgroundColor: "primary.main",
                          "&:disabled": {
                            backgroundColor: "white",
                            color: "grey",
                          },
                        }}
                        disabled={
                          !isListingEmpanelled && !isMicrositeEmpanelled
                        }
                      >
                        UnEmpanel
                      </Button>
                    </div>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleMessageClose}
                      sx={{ margin: "auto", width: 100 }}
                    >
                      Okay
                    </Button>
                  )}
                </DialogActions>
              </Box>
            </Dialog>

            <Dialog
              open={strategicDialogOpen}
              aria-labelledby="alert-dialog-title"
              aria-describedby="alert-dialog-description"
              PaperProps={{
                sx: {
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                  backgroundColor: (theme) => theme.palette.primary.background,
                },
              }}
              //disableEscapeKeyDown
              onClose={(event, reason) => {
                if (reason === "backdropClick") {
                  handleStrategicDialogClose();
                }
              }}
            >
              <Box
                sx={{
                  width: "100%",
                  borderRadius: 1,
                  textAlign: "center",
                  border: (theme) => `1px solid ${theme.palette.divider} `,
                  borderColor: "primary.main",
                }}
              >
                <DialogContent>
                  <DialogContentText
                    id="alert-dialog-description"
                    color="primary.main"
                  >
                    <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
                  </DialogContentText>
                </DialogContent>
                <DialogActions
                  style={{ display: "flex", justifyContent: "center" }}
                >
                  {strategicDialog ? (
                    <div>
                      <Button
                        variant="contained"
                        onClick={handleAssignStrategic}
                        sx={{ margin: "10px" }}
                      >
                        Assign
                      </Button>
                      <Button
                        variant="contained"
                        onClick={handleRemoveStrategic}
                        sx={{ margin: "10px" }}
                      >
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleStrategicDialogClose}
                      sx={{ margin: "auto", width: 100 }}
                    >
                      Okay
                    </Button>
                  )}
                </DialogActions>
              </Box>
            </Dialog>
            {/* Profile Long Form */}
            <Dialog
              fullWidth
              maxWidth="xl"
              scroll="paper"
              open={profileShow}
              disableEscapeKeyDown
              onClose={(event, reason) => {
                if (reason !== "backdropClick") {
                  handleProfileClose;
                }
              }}
            >
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start", md: "center" },
                  fontSize: { xs: 20, md: 26 },
                }}
                textAlign={"center"}
                fontWeight={"bold"}
              >
                <>
                  {entityCategory === "ARCHITECT"
                    ? "Architect Profile"
                    : entityCategory === "BROKER"
                    ? "Broker Profile"
                    : entityCategory === "CHARTERED_ACCOUNTANT"
                    ? "Chartered Accountant Profile"
                    : entityCategory === "SOCIETY"
                    ? "Society Profile"
                    : entityCategory === "PMC"
                    ? "PMC Profile"
                    : entityCategory === "STRUCTURAL_ENGINEER"
                    ? "Structural Engineer Profile"
                    : null}
                  <Box sx={{ position: "absolute", top: "3px", right: "12px" }}>
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <CloseExpandIcons
                        expanded={expanded}
                        onToggle={handleToggle}
                      />
                      <IconButton
                        size="small"
                        onClick={handleProfileClose}
                        sx={{
                          // p: "0.438rem",
                          borderRadius: 1,
                          color:"common.white", 
                  backgroundColor: "primary.main",
                          "&:hover": {
                            backgroundColor: 
                        '#66BB6A',
                         transition: 'background 0.5s ease, transform 0.5s ease',                       
                        },
                        }}
                      >
                        <Icon icon="tabler:x" fontSize="1rem" />
                      </IconButton>
                    </div>
                  </Box>
                </>
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  ":playing": (theme) => `${theme.spacing(4)} !important`,
                  px: (theme) => [
                    `${theme.spacing(6)} !important`,
                    `${theme.spacing(10)} !important`,
                  ],
                }}
              >
                {renderForm()}
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "center",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.85)} !important`,
                }}
              >
                <Button onClick={handleProfileClose}>Close</Button>
              </DialogActions>
            </Dialog>

            <Dialog
              open={openRemindDialog}
              onClose={handleRemindCloseDialog}
              maxWidth={"lg"}
            >
              <DialogTitle
                sx={{
                  position: "relative",
                  borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(1.75, 4)} !important`,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: { xs: "start", md: "center" },
                  fontSize: { xs: 20, md: 26 },
                }}
                textAlign={"center"}
                fontWeight={"bold"}
              >
                New Reminder
              </DialogTitle>
              <DialogContent
                sx={{
                  position: "relative",
                  pt: (theme) => `${theme.spacing(14)} !important`,
                  pb: (theme) => `${theme.spacing(10)} !important`,
                  px: (theme) => [`${theme.spacing(8)} !important`],
                }}
              >
                <Grid container alignItems="center" justify="center">
                  <Grid item xs={12} sm={6} sx={{ mb: 5 }}>
                    <CustomAutocomplete
                      autoHighlight
                      id="autocomplete-email-select"
                      options={emailOptions}
                      getOptionLabel={(option) => option.email || ""}
                      value={selectedEmail}
                      onChange={handleEmailChange}
                      renderInput={(params) => (
                        <CustomTextField
                          {...params}
                          placeholder="Select Email"
                          label="Select Email"
                        />
                      )}
                    />
                  </Grid>

                  <Grid item xs={12} marginLeft={70}>
                    <Calendar value={value} onChange={onChange} />
                  </Grid>

                  <Grid item xs={12} sx={{ mt: 5 }}>
                    <FormControl fullWidth>
                      <Controller
                        name="messageOptional"
                        control={control}
                        //defaultValue={messageOptional}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            rows={4}
                            multiline
                            label="Message (optional)"
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.messageOptional)}
                            helperText={errors.messageOptional?.message}
                            onChange={(e) => {
                              field.onChange(e); // Inform React Hook Form of the change
                              setMessageOptional(e.target.value); // Update local state
                            }}
                            value={messageOptional} // Set value from local state
                            aria-describedby="validation-basic-textarea"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions
                sx={{
                  justifyContent: "center",
                  borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                  p: (theme) => `${theme.spacing(2.5)} !important`,
                }}
              >
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="outlined"
                  color="primary"
                  onClick={handleRemindCloseDialog}
                >
                  Cancel
                </Button>
                <Button
                  display="flex"
                  justifyContent="center"
                  variant="contained"
                  color="primary"
                  onClick={submitReminder}
                >
                  Save
                </Button>
              </DialogActions>
            </Dialog>
          </Grid>
        )}
      </>
    );
  } else {
    return null;
  }
};

export default AllProfiles;
