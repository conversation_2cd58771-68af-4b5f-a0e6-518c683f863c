const baseURL = process.env.NEXT_PUBLIC_API_URL;

const googleAuthUrl = process.env.NEXT_PUBLIC_GOOGLE_AUTH_URL;

const facebookAuthUrl = process.env.NEXT_PUBLIC_FACEBOOK_AUTH_URL;

const razorpayClientId = process.env.RZP_CLIENT_ID;

export default {
  baseURL: baseURL,
  googleAuthUrl: googleAuthUrl,
  facebookAuthUrl: facebookAuthUrl,
  razorpayClientId: razorpayClientId,
  authActionUrl: "auth/flow",
  guestURL: "https://pheart.in/",
  resourcesURL: "https://reducate.houzer.co.in/",
  readinessEndpoint: "auth/readiness",
  getReadinessProfileEndpoint: "users/readiness",
  loginEndpointNew: "auth/login",
  searchEndpoint: "entity",
  addNewUserEndpoint: "member/createMember",
  saveUserSpEndpoint: "users/all-profiles/create-short-form-sp",   //sp user form
  userPatch: "users/society/profile-data",
  userGet: "users/society-profile",
  readinessPatch: "users/updateReadiness",
  projectDataEndpoint: "entity",
  registerEndpointNew: "auth/signup",
  registerEndpointNewV2: "auth/signupV2",
  registerEndpointNewV3: "auth/signupV3",
  profileEndpointNew: "users/profile",
  profileUpdateEndpoint: "users",
  emailVerificationEndpoint: "auth/verify",
  getAllMembersEndpoint: "member/all",
  getAllRoleTypesEndpoint: "roleTypes/all",
  roleTypeEndpoint: "roleTypes",
  roleEndpoint: "role",
  roleUpdateEndpoint:"role/update",
  roleTypeUpdateEndpoint:"roleTypes/update",
  getAllEmployeesList: "employee/list",
  getAllRolesEndpoint: "role/all",
  usersEndpoint: "users",
  getAllUsersEndpointNewTable: "users/all-users",
  getAllProfilesEndpointNewTable: "users/all-profiles",
  getAllConversations: "conversation/getAllConversations",
  getAllConversationsCHS:"conversation/getAllConversations-chs",
  getAllTimelineConversations:'conversation/getAllConversationsByTimeLine',
  getAllTimelineConversationsCHS:'conversation/getAllConversationsByTimeLine-chs',
  passwordResetEndpoint: "auth/reset",
  settings: "settings",
  storageTokenKeyName: "accessToken",
  refreshTokenKeyName:"refreshToken",
  storageUserKeyName: "userData",
  onTokenExpiration: "logout", // logout | refreshToken
  triggerForgetPasswordEndpoint: "auth/forgotPassword",
  otpSendToEmailEndpoint: "auth/emailVerify",
  employeeCreateEndpoint: "employee",
  requisitionEndpoint:"service-requisition",
  requisitionGetAll:"service-requisition/all",
  requisitionsByUserId:"service-requisition/getServiceRequisitionByUserId",
  requisitionGet:"service-requisition/getServiceRequisitionById",
  requisitionUpdate:"service-requisition/updateServiceRequisitionByDTO",
  employeeUpdateEndpoint: "employee",
  employeeGetAllEndpoint: "employee/all",
  employeeDeleteEndpoint: "employee",
  employeeStatusUpdateEndpoint:"employee/update",
  unSubscribeEndpoint: "auth/unSubscribe",
  otpVerifyEndpoint: "auth/verifyOTPV2",
  otpVerifyEndpoint3: "auth/verifyOTPV3",
  notificationEndpoint: "notification",
  notificationV2Endpoint: "api/v2/notifications",
  selectDropdown: "selectDropdown",
  selectDropdownNew: "selectDropdown/individual-organisation-derived",
  reminderEndpoint: "reminder",
  documents: "documents",
  snapshots:"lead-snapshots",
  snapshotsGetAll:"lead-snapshots/allLeadSnapshots",
  snapshotsDelete:"lead-snapshots/delete",
  snapshotsActivate:"lead-snapshots/activate",
  snapshotsDeleteById:"lead-snapshots/deleteImage",
  packageTypeListNameId: "5c1b4c4e-8b9b-4b3d-9f77-8c44f63b254a",
  yearsOfExperienceListNameId:"918a41cd-e3ec-46c5-84da-0cf2727842bc",
  portalsRegisteredListNameId:"7b7244dc-1a64-4e98-930f-eb07ba9c308e",
  donorTypeListNameId:"b23ce94f-1c3c-40fd-bf76-66ad66ed8916",
  referralSourceListNameId:"f3a58d9d-caf1-45eb-90b5-205e1b0c701d",
  documentCategories: "documentCategories/all",
  documentSubCategories: "documentSubCategories/all",
  uploadProject: "documents/projects",
  getAllDocuments: "documents/allDocuments",
  getFileByLocation: "api/getFile",
  profileLogout: "logoutProfile",
  masterDataCreateEndpoint: "master-data",
  masterDataActivate:"master-data/activate",
  masterDataGetAllEndpoint: "master-data/all",
  servicesGetAll: "list-values/allValuesByNameId",
  locationZonesGetAll:"location-zone/all",
  locationZoneMapping:"location-zone",
  locationZoneActivate:"location-zone/activate",
  subServicesGetAll: "service-profile/sub-services",
  listValuesEndpoint: "list-values",
  listValueUpdate:"list-values/update",
  listValuesGetAll: "list-values/all",
  listNameEndpoint: "list-names",
  listNamesGetAll:"list-names/all",
  listNameStatisticsQuestion:"list-names/statistics-questions",
  uiComponentId:"612e5e29-dbe4-49cf-b693-f935c69219e1",
  fsiRuleEndpoint: "fsi-rules",
  fsiRulesActivateEndpoint:"fsi-rules/activate",
  fsiRulesGetAllEndpoint: "fsi-rules/all",
  fsiCalculatorEndpoint: "fsi-calculator",
  fsiCalculatorUpdateEndpoint:"fsi-calculator/update",
  fsiCalculatorSocietyGetALL: "fsi-calculator/all",
  fsiCalculatorGetALL: "fsi-calculator/all-records",
  assignRoleEndpoint: "users/assignRole",
  getAllUserTypes: "users/userType",
  getAllServiceProfiles: "service-profile",

  updateServiceProfiles:"service-profile/update",

  specificationActivate:"service-profile/activate",

  serviceProfileEndpoint: "user-services",
  statisticsEndpoint: "user-services/statistics",
  servicesDataEndpoint: "user-services/services",
  statisticsDataEndpoint: "user-services/statistics",
  projectsDataEndpoint: "user-services/projects",
  locationNames: "service-profile/locations",
  getAllSpecifications:"service-profile/all",
  getAllListValuesByListNameId: "list-values/listNameId",
  locationlistNameId: "c49dd276-79aa-4e4d-9292-5a1b3f6aaf48",
  workLocation: "9e36c6d5-3545-4b77-af6d-d1fb3c29585c",
  departmentId:"76ffa048-cf7e-4c45-a44d-bca55f998002",
  designationId: "67f1c22d-0a95-4db3-aae8-45294f112e99",
  designationSPId:"8e98a6de-9b9f-4c70-84fb-8821fd724657",
  departmentId:"76ffa048-cf7e-4c45-a44d-bca55f998002",
  allServicesListNameId: "88cf3b1d-8548-41d3-9910-c2f92dcf2815",
  uiComponentsId:"5b50c3e1-bf42-4c50-90a4-109bc42e3a38",
  allSectionsId:"9c53ef49-04db-4c0f-8f19-7f75f58a066d",
  allSubSectionsIds:"4b0c4d18-ac3d-422b-aea3-86fd7755bc99",
  societiesId: "2b5a9a3a-9440-4217-a5e5-784d1a42d53b",
  allBusinessLineId: "8aee6092-0c04-46fc-8b0a-d860eb2e36e2",
  basicProfileEndpoint: "users/basic-profile",
  allProfilesBasicProfileEndpoint: "users/all-profiles/createUser",
 
  allListNamesValues: "listNamesValues/all",
  micrositeListsEndpoint: "update-list-contextual",
  micrositeLevel2Endpoint: "update-level2-contextual",
  micrositeLevel1Endpoint: "update-level1-contextual",
  micrositeBasicProfileGetEndpoint: "public/microsite/individual-profile-microsite",
  leadStatusListNamesId: "550e8400-e29b-41d4-a716-446655440000",
  referenceListNameId:"54c553df-22de-4d1f-9839-1a5f7f8cbf03",
  groupDataListNameId: "4f4050bc-4f07-42a2-8f2c-56005d1af5bb",
  getAllConversationsByUserId: "conversation/getAllConversationsByUserId",
  getAllConversationsCHS:"conversation/getAllConversations-chs",
  conversationType: "2b5a9a3a-9440-4217-a5e5-784d1a42d123",
  outcomeConversation: "0a6e0dc5-92a4-4d7e-8f6b-ee95b8e07e1d",
  target: "9aeb2e4e-8902-4f52-a8f7-3b92c374c55f",
  shallRemind: "57c2f3a3-5b84-4d98-bc09-9e18d40aeb36",
  userCategorySociety: "SOCIETY",
  userCategoryServiceProvider: "SERVICE_PROVIDER",
  userCategoryEmployee: "EMPLOYEE",
  conversationEndpoint: "conversation",
  taskEndpoint: "task",
  getAllTasks: "task/all",
  leadPriorityListNamesId: "7819267b-59e0-4b02-80e8-43d0e6db9d80",
  priorityListNamesId: "f59db722-9be2-4f5a-b072-27cf4247f3e4",
  statusListNamesId: "c166e081-9b82-49ec-9a1a-0428e426e33b",
  allZonesListNameId:"3dcb4434-849f-4b5c-a881-70b31ebde9d2",
  allProfilesUpdateActiveStatus:"users/all-profiles/updateStatus",
  allProfilesUpdateInActiveStatus:"users/all-profiles/deleteUser",
  allUsersForContacts:"api/v1/individual/contact-group/all",
  watiEndPoint: "api/wati/sendTemplateMessage",
  eventEndpoint:'event',
  calendarUpdateEventEndpoint:'apps/calendar/update-event',
  calendarGetByCalendarEventEndpoint:'apps/calendar/event',
  calendarDeleteEventEndpoint:'apps/calendar/remove-event',
  watiMutliMessageEndPoint: "api/wati/TemplateMessages",
  contactGroupsEndpoint:"contact-group",
  contactGroupsGetAll:"contact-group/all",
  contactGroupsActivate:"contact-group/activate",
  createSubscriptionEndpoint:"subscription",
  patchSubscription: "subscription",
  createPaymentEndpoint:'payments',
  razorpayEndpoint:"payments/Razorpay",
  subscriptionEndpoint:"subscription",
  microSiteGet: 'public/microsite/individual-profile-microsite',
  getStatisticsData: "user-services/statistics",
  getProjectsImages: "public/microsite/projects/images",
  getAllDocumentsEndpointMicrosite: 'public/microsite/all',
  serviceProvidersByMonthAdminEndpoint:'/admin/service-providers-by-month',
  serviceProvidersByMonthHTEndpoint:'/service-providers-by-month-ht',
  CHSCategoryId:'9fb6e12c-8c82-4d03-a675-5d6a4e78a9c7',
  SPCategoryId:'b4ae14a7-9c73-41a4-bf7c-22d628c2e3e8',
  allReadinessReportsEndpoint:'auth/readiness/all',
  plumbingServiceId:"afbd1145-1a44-44c6-865f-61ff96cdd0b8",
  anyOtherDesignationId:"59852b6d-4f18-4eaf-bfa6-b5306f35a02a",
  rolesEndpoint:"admin/api/v1/roles",
  getMIMEType:'application/vnd-chidhagni-houzer.permissions.get.by.role.id.res-v1+json',
  getAllMIMEType:'application/vnd-chidhagni-houzer.roles.get.all.req-v1+json',
  getAllAcceptMIMEType:'application/vnd-chidhagni-houzer.roles.get.all.res-v1+json',
  postMIMEType:'application/vnd-chidhagni-houzer.roles.create.req-v1+json',
  postAcceptMIMEType:'application/vnd-chidhagni-houzer.roles.create.res-v1+json',
  patchMIMEType:'application/vnd-chidhagni-houzer.roles.update.req-v1+json',
  patchAcceptMIMEType:'application/vnd-chidhagni-houzer.roles.update.res-v1+json',
  getByParentRoleMIMEType:'application/vnd-chidhagni-houzer.permissions.get.by.parent.id.res-v1+json',

  individualEndpoint :"api/v1/individual",
  INDIVIDUAL_GET_ALL_REQ_V1:"application/vnd-chidhagni-houzer.individual.get.all.req-v1+json",
  INDIVIDUAL_GET_ALL_RES_V1:"application/vnd-chidhagni-houzer.individual.get.all.res-v1+json",
  INDIVIDUAL_GET_ALL_EMPLOYEE_REQ_V1:"application/vnd-chidhagni-houzer.individual.employee.get.all.req-v1+json",
  INDIVIDUAL_GET_ALL_EMPLOYEE_RES_V1:"application/vnd-chidhagni-houzer.individual.employee.get.all.res-v1+json",
  individualGetMIMEType:"application/vnd-chidhagni-houzer.individual.get.res-v1+json",
  individualPutMIMEType:"application/vnd-chidhagni-houzer.individual.update.req-v1+json",
  individualPutAcceptMIMEType:"application/vnd-chidhagni-houzer.individual.update.res-v1+json",
  individualListMIMEType:"application/vnd-chidhagni-houzer.individual.get.individual.by.orgid.res-v1+json",
  individualEmployeesMIMEType:"application/vnd-chidhagni-houzer.individual.get.employee.res-v1+json",
  INDIVIDUAL_GET_ROLE_HIERARCHY_MIMETYPE:"application/vnd-chidhagni-houzer.individual.get.roles.hierarchy.by.roleid.res-v1+json",
  INDIVIDUAL_GET_ALL_REQ_V1:"application/vnd-chidhagni-houzer.individual.get.all.req-v1+json",
  INDIVIDUAL_GET_ALL_RES_V1:"application/vnd-chidhagni-houzer.individual.get.all.res-v1+json",
  INDIVIDUAL_GET_ALL_EMPLOYEE_REQ_V1:"application/vnd-chidhagni-houzer.individual.employee.get.all.req-v1+json",
  INDIVIDUAL_GET_ALL_EMPLOYEE_RES_V1:"application/vnd-chidhagni-houzer.individual.employee.get.all.res-v1+json",

  individualPermissionsEndpoint :"api/v1/individual/permissions",

  organisationsEndpoint:"api/v1/organisation",
  organisationListMIMEType:"application/vnd-chidhagni-houzer.organisation.get.res-v1+json",

  ORGANISATION_GET_NAME_BY_CATEGORY_V1:"application/vnd-chidhagni-houzer.organisation.get.name.by.category-res-v1+json",
  ORGANISATION_GET_ORGANISATION_META_DATA_RES_V1:"application/vnd-chidhagni-houzer.organisation.get.organisation.metadata-res-v1+json",
  ORGANISATION_META_DATA_GET_BY_ID_V1:"application/vnd-chidhagni-houzer.organisation.get.by.organisation.id-res-v1+json",
  ORGANISATION_PATCH_META_DATA_BY_ID_REQ_V1:"application/vnd-chidhagni-houzer.organisation.patch.metadata.by.organisation.id-req-v1+json",
  ORGANISATION_PATCH_META_DATA_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.organisation.patch.metadata.by.organisation.id-res-v1+json",

  referenceTypeListNameId:"c68c8b30-8218-4a5c-805a-e20ff7441a82",
  referralNameId:"5c9d2496-8c7b-4e2e-a5e3-c9d18c5c8f1b",
  superAdminRoleId:"107dc277-e80a-4d4c-9f0a-48bcbad5bea3",
  serviceProviderRoleId:"3676706b-fc31-4b41-956c-3c4c758aa663",
  societyRoleId:"ad60ce4e-f528-4722-82aa-8757baafce7a",
  htAdminRoleId:"df753008-b821-4ec2-91fb-fc580f55a006",

  siteVisitStatusOpenId:"2f8dd1d6-b744-45a6-8b36-7db1fbe0367e",
  serviceRequisitionsEndpoint:"api/v1/service-requisitions",
  postSRMIMEType:"application/vnd-chidhagni-houzer.serivce.requisitions.create.req-v1+json",
  postSRAcceptMIMEType:"application/vnd-chidhagni-houzer.serivce.requisitions.create.res-v1+json",
  REQUISITION_GETALL_MIMETYPE:"application/vnd-chidhagni-houzer.service.requisitions.get.all.req-v1+json",
  REQUISITION_GETALL_ACCEPT_MIMETYPE:"application/vnd-chidhagni-houzer.service.requisitions.get.all.res-v1+json",
  REQUISITION_ADMIN_GETALL_MIMETYPE:"application/vnd-chidhagni-houzer.service.requisitions.get.all.admin.req-v1+json",
  REQUISITION_ADMIN_GETALL_ACCEPT_MIMETYPE:"application/vnd-chidhagni-houzer.service.requisitions.get.all.admin.res-v1+json",
  PATCH_SR_MIMETYPE:"application/vnd-chidhagni-houzer.serivce.requisitions.update.req-v1+json",
  PATCH_SR_ACCEPT_MIMETYPE:"application/vnd-chidhagni-houzer.serivce.requisitions.update.res-v1+json",

  getAllServiceProvidersFromOrganization:"api/v1/organisation/assign-broadcast/all",

  orgMimeReqType : "application/vnd-chidhagni-houzer.organisation.get.all.organisations.by.service.type.req-v1+json",
  orgMimeResType : "application/vnd-chidhagni-houzer.organisation.get.all.organisations.by.service.type.res-v1+json",

  postBroadCastSrToSpEndpoint:'api/v1/sr-quotations',

  SR_QUOTATIONS_CREATE_REQ_V1 :"application/vnd-chidhagni-houzer.sr.quotations.create.req-v1+json",
  SR_QUOTATIONS_CREATE_RES_V1 : "application/vnd-chidhagni-houzer.sr.quotations.create.res-v1+json",

  siteVisitsEndpoint:'api/v1/site-visits',
  openSlot:'2f8dd1d6-b744-45a6-8b36-7db1fbe0367e',

  SITE_VISITS_GET_ALL_ADMIN_RES_V1: "application/vnd-chidhagni-houzer.site.visits.get.all.admin.res-v1+json",
  SITE_VISITS_GET_ALL_ADMIN_REQ_V1: "application/vnd-chidhagni-houzer.site.visits.get.all.admin.req-v1+json",

  SITE_VISITS_GET_ALL_RES_V1: "application/vnd-chidhagni-houzer.site.visits.get.all.res-v1+json",
  SITE_VISITS_GET_ALL_REQ_V1: "application/vnd-chidhagni-houzer.site.visits.get.all.req-v1+json",

  SITE_VISIT_TIMINGS_GET_RES_V1: "application/vnd-chidhagni-houzer.site.visits.get.res-v1+json",

  SITE_VISIT_TIMINGS_PATCH_STATUS_RES_V1: "application/vnd-chidhagni-houzer.site.visits.patch.status.req-v1+json",
  SITE_VISIT_TIMINGS_RESCHEDULE_STATUS_RES_V1: "application/vnd-chidhagni-houzer.site.visits.patch.reschedule.status.res-v1+json",

 documentsEndpoint:'api/v1/resource',
 resourceDocumentId:"1dab1eea-7643-4445-9232-10032bd60d24",
 RESOURCES_CREATE_REQ_V1:"application/vnd-chidhagni-houzer.resources.create.req-v1+json",
 RESOURCES_CREATE_RES_V1: "application/vnd-chidhagni-houzer.resources.create.res-v1+json",
 RESOURCES_GET_RES_V1 :"application/vnd-chidhagni-houzer.resources.get.by.id.res-v1+json",


 documentEndpoint:"api/v1/document",
 DOCUMENT_REPO_GET_ALL_ADMIN_REQ_V1:"application/vnd-chidhagni-houzer.document.repo.get.all.admin.req-v1+json",
 DOCUMENT_REPO_GET_ALL_ADMIN_RES_V1:"application/vnd-chidhagni-houzer.document.repo.get.all.admin.res-v1+json",
 DOCUMENT_REPO_GET_ALL_REQ_V1:"application/vnd-chidhagni-houzer.document.repo.get.all.req-v1+json",
 DOCUMENT_REPO_GET_ALL_RES_V1:"application/vnd-chidhagni-houzer.document.repo.get.all.res-v1+json",

 DOCUMENTS_CREATE_RES_V1:"application/vnd-chidhagni-houzer.documents.create.res-v1+json",
 DOCUMENT_REPO_UPDATE_RES_V1:"application/vnd-chidhagni-houzer.document.repo.update.res-v1+json",

  SITE_VISIT_TIMINGS_RESCHEDULE_STATUS_REQ_V1: "application/vnd-chidhagni-houzer.site.visits.patch.reschedule.status.req-v1+json",

  SITE_VISIT_TIMINGS_UPDATE_REQ_V1:"application/vnd-chidhagni-houzer.site.visits.patch.req-v1+json",


  quotationsEndpoint:"api/v1/sr-quotations",
  SR_QUOTATIONS_PATCH_RES_V1:"application/vnd-chidhagni-houzer.sr.quotations.patch.res-v1+json",
  SR_QUOTATIONS_GET_BOQ_SERVICE_PROVIDER_ORG_RES:"application/vnd-chidhagni-houzer.sr.quotations.get.boq.sp.orgId.res-v1+json",
  SR_QUOTATIONS_PATCH_SUBMIT_BOQ_REQ_V1:"application/vnd-chidhagni-houzer.sr.quotations.patch.submit.final.boq.req-v1+json",
  SR_QUOTATIONS_PATCH_SUBMIT_BOQ_RES_V1:"application/vnd-chidhagni-houzer.sr.quotations.patch.submit.final.boq.res-v1+json",
  SR_QUOTATIONS_GET_BROAD_CASTED_SP_RES_V1:"application/vnd-chidhagni-houzer.sr.quotations.get.broad.casted.sp.res-v1+json",

  templateEndpoint:"api/v1/template",
  TEMPLATE_GET_ALL_DETAILS_REQ_V1:"application/vnd-chidhagni-houzer.template.all.details.req-v1+json",
  TEMPLATE_GET_ALL_DETAILS_RES_V1:"application/vnd-chidhagni-houzer.template.all.details.res-v1+json",
  TEMPLATE_CREATE_REQ_V1:"application/vnd-chidhagni-houzer.template.create.req-v1+json",
  TEMPLATE_CREATE_RES_V1:"application/vnd-chidhagni-houzer.template.create.res-v1+json",
  TEMPLATE_DELETE_RES_V1:"application/vnd-chidhagni-houzer.template.delete.res-v1+json",
  TEMPLATE_GET_DETAILS_RES_V1:"application/vnd-chidhagni-houzer.template.details.res-v1+json",
  TEMPLATE_UPDATE_FLAG_REQ_V1:"application/vnd-chidhagni-houzer.template.update.flag.req-v1+json",
  TEMPLATE_UPDATE_FLAG_RES_V1:"application/vnd-chidhagni-houzer.template.update.flag.res-v1+json",
  TEMPLATE_UPDATE_REQ_V1:"application/vnd-chidhagni-houzer.template.update.req-v1+json",
  TEMPLATE_UPDATE_RES_V1:"application/vnd-chidhagni-houzer.template.update.res-v1+json",
  TEMPLATE_GET_NAMES_RES_V1:"application/vnd-chidhagni-houzer.template.names.res-v1+json",

  siteVisitsActivateEndpoint: "api/v1/site-visits/activate",
  SERVICE_REQUISITIONS_ACTIVATE_RES_V1: "application/vnd-chidhagni-houzer.service.requisitions.site.visit.status.activate.res-v1+json",


  siteVisitDeactivateEndpoint: "api/v1/site-visits/de-activate",
  SERVICE_REQUISITIONS_DEACTIVATE_RES_V1:"application/vnd-chidhagni-houzer.service.requisitions.site.visit.status.de.activate.res-v1+json",

  multiSelectComponentId:"2f5d65b7-ea2d-4d2d-a2f2-6c3f7e6e4432",
  singleSelectComponentId:"3f0ed8a7-74d2-4b23-b071-2b0f44f10cf8",
  switchComponentId:"5f4768e9-fd43-42d4-a13f-95363df6889a",
  radioButtonComponentId:"4fb5b7b0-88c1-4cd3-b8e7-b4dfba9d69a0",
  textFieldComponentId:"612e5e29-dbe4-49cf-b693-f935c69219e1",
  numberTextFieldComponentId:"4f1f9a9c-c98d-4ef2-b10d-7fe2a326c0d7",
  textAreaComponentId:"4fecd285-0b4e-41eb-99e3-889b2e3cfdd2",



SR_QUOTATIONS_GET_ALL_ADMIN_RES_V1 : "application/vnd-chidhagni-houzer.sr.quotations.get.all.admin.res-v1+json",
SR_QUOTATIONS_GET_ALL_ADMIN_REQ_V1 : "application/vnd-chidhagni-houzer.sr.quotations.get.all.admin.req-v1+json",

SR_QUOTATIONS_GET_ALL_RES_V1 : "application/vnd-chidhagni-houzer.sr.quotations.get.all.res-v1+json",
SR_QUOTATIONS_GET_ALL_REQ_V1 : "application/vnd-chidhagni-houzer.sr.quotations.get.all.req-v1+json",

statisticsEndpointGraphs:"api/v1/statistics",

defaultEmployeeRoleEndpoint:"cafac5b3-77dd-4fcf-b890-6a2f63b9ee44",
SERVICE_REQUISITIONS_CONFIRMED_SP_UPDATE_REQ_V1:"application/vnd-chidhagni-houzer.serivce.requisitions.confirmed.sp.update.req-v1+json",
SERVICE_REQUISITIONS_CONFIRMED_SP_UPDATE_RES_V1 :"application/vnd-chidhagni-houzer.serivce.requisitions.confirmed.sp.update.res-v1+json",
STATISTICS_GET_ADMIN_REQ_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.create.res-v1+json",
STATISTICS_GET_REQ_V1:"application/vnd-chidhagni-houzer.statistics.get.create.res-v1+json",

workOrdersEndpoint:"api/v1/work-orders",
WORK_ORDERS_INSERT_RES_V1:"application/vnd-chidhagni-houzer.workorders.create.res-v1+json",
WORK_ORDERS_GET_ALL_BY_ADMIN_REQ_V1:"application/vnd-chidhagni-houzer.workorders.get.all.by.admin.req-v1+json",
WORK_ORDERS_GET_ALL_BY_ADMIN_RES_V1:"application/vnd-chidhagni-houzer.workorders.get.all.by.admin.res-v1+json",
WORK_ORDERS_GET_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.workorders.get.by.id.res-v1+json",
WORK_ORDERS_PUT_REQ_V1:"application/vnd-chidhagni-houzer.workorders.update.req-v1+json",
WORK_ORDERS_PUT_RES_V1:"application/vnd-chidhagni-houzer.workorders.update.res-v1+json",
WORK_ORDERS_PATCH_FILE_LOCATION_RES_V1:"application/vnd-chidhagni-houzer.workorders.update.file.location.res-v1+json",
WORK_ORDERS_GET_TEMPLATE_NAMES_RES_V1:"application/vnd-chidhagni-houzer.work.orders.get.template.names.res-v1+json",
WORK_ORDERS_GET_TEMPLATE_CONTENT_RES_V1:"application/vnd-chidhagni-houzer.work.orders.get.template.content.res-v1+json",
WORK_ORDERS_GET_ALL_FOR_SP_CHS_EMP_REQ_V1:"application/vnd-chidhagni-houzer.workorders.get.all.for.sp.chs.emp.req-v1+json",
WORK_ORDERS_GET_ALL_FOR_SP_CHS_EMP_RES_V1:"application/vnd-chidhagni-houzer.workorders.get.all.for.sp.chs.emp.res-v1+json",


invoicesEndpoint:"api/v1/invoice",
INVOICES_GET_ALL_BY_ADMIN_REQ_V1:"application/vnd-chidhagni-houzer.invoices.get.all.by.admin.req-v1+json",
INVOICES_GET_ALL_BY_ADMIN_RES_V1:"application/vnd-chidhagni-houzer.invoices.get.all.by.admin.res-v1+json",
INVOICES_GET_ALL_BY_SP_CHS_EMPLOYEE_REQ_V1:"application/vnd-chidhagni-houzer.invoices.get.all.by.sp.chs.employee.req-v1+json",
INVOICES_GET_ALL_BY_SP_CHS_EMPLOYEE_RES_V1:"application/vnd-chidhagni-houzer.invoices.get.all.by.sp.chs.employee.res-v1+json",
INVOICES_GET_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.invoices.get.by.id.res-v1+json",
INVOICE_CREATE_REQ_V1:"application/vnd-chidhagni-houzer.invoice.create.req-v1+json",
INVOICE_CREATE_RES_V1:"application/vnd-chidhagni-houzer.invoice.create.res-v1+json",
INVOICES_PUT_REQ_V1:"application/vnd-chidhagni-houzer.invoices.put.req-v1+json",
INVOICES_PUT_RES_V1:"application/vnd-chidhagni-houzer.invoices.put.res-v1+json",
invoiceStatus:"c698f54b-322d-4130-9474-9371edec0c9e",
STATISTICS_GET_REQ_SP_COUNT_ADMIN_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.sp.count.res-v1+json",
STATISTICS_GET_REQ_SP_COUNT_HT_V1 : "application/vnd-chidhagni-houzer.statistics.get.ht.sp.count.res-v1+json",
siteVisitsOpenId: "2f8dd1d6-b744-45a6-8b36-7db1fbe0367e",
payments: "payments",
SR_QUOTATION_DEACTIVATE_BY_ORG_ID_RES_V1:"application/vnd-chidhagni-houzer.sr.quotations.deactivate.by.organisation.id.res-v1+json",
SR_QUOTATION_ACTIVATE_BY_ORG_ID_RES_V1:"application/vnd-chidhagni-houzer.sr.quotations.activate.by.organisation.id.res-v1+json",

STATISTICS_GET_REQ_BY_ZONE_COUNT_ADMIN_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.sr.count.by.zone.res-v1+json",
STATISTICS_GET_REQ_BY_ZONE_COUNT_EMPLOYEE_V1:"application/vnd-chidhagni-houzer.statistics.get.employee.sr.count.by.zone.res-v1+json",
STATISTICS_GET_REQ_BY_SERVICE_TYPE_COUNT_ADMIN_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.sr.count.by.service.type.res-v1+json",
STATISTICS_GET_REQ_BY_SERVICE_TYPE_COUNT_EMPLOYEE_V1:"application/vnd-chidhagni-houzer.statistics.get.employee.sr.count.by.service.type.res-v1+json",
STATISTICS_GET_GROUP_BY_LEAD_STATUS_ADMIN_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.sp.count.group.by.lead.status.res-v1+json",
STATISTICS_GET_GROUP_BY_LEAD_STATUS_EMPLOYEE_V1:"application/vnd-chidhagni-houzer.statistics.get.employee.sp.count.group.by.lead.status.res-v1+json",
STATISTICS_GET_SERVICE_REQUISITIONS_QUARTERLY_REPORT_ADMIN_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.sr.quarterly.report.res-v1+json",
STATISTICS_GET_SERVICE_REQUISITIONS_QUARTERLY_REPORT_EMPLOYEE_V1:"application/vnd-chidhagni-houzer.statistics.get.employee.sr.quarterly.report.res-v1+json",
STATISTICS_GET_SERVICE_REQUISITIONS_FLOW_COUNT_ADMIN:"application/vnd-chidhagni-houzer.statistics.get.service.requisitions.flow.count.admin.res-v1+json",
STATISTICS_GET_SERVICE_REQUISITIONS_FLOW_COUNT:"application/vnd-chidhagni-houzer.statistics.get.service.requisitions.flow.count.res-v1+json",
STATISTICS_SERVICE_REQUISITIONS_COUNT_GROUP_BY_CREATED_BY_AND_ASSIGNED_TO_ME__DURATION_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.service.requisitions.count.group.by.created.by.and.assigned.to.me.by.duration.res-v1+json",
STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_EMPLOYEE_V1:"application/vnd-chidhagni-houzer.statistics.get.employee.individual.count.group.by.role.res-v1+json",
STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.individual.count.group.by.role.res-v1+json",
STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_AND_DURATION_V1:"application/vnd-chidhagni-houzer.statistics.get.admin.individual.count.group.by.role.and.duration.res-v1+json",
STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_AND_DURATION_EMPLOYEE_V1:"application/vnd-chidhagni-houzer.statistics.get.employee.individual.count.group.by.role.and.duration.res-v1+json",
ExpiredStatus:"6369edac-b2fb-4566-985d-b43cbcb6e0fb",
emailSaving:"auth/api/v1/leads-contact",
LEADS_CONTACT_INFO_REQUEST: "application/vnd-chidhagni-houzer.leads.contact.info.req-v1+json",
LEADS_CONTACT_INFO_RESPONSE:"application/vnd-chidhagni-houzer.leads.contact.info.res-v1+json",
invoicesOverdueId:"1b549d91-8696-40e2-9320-b9347cc98c2b",
getAllEmail:"auth/all",
ChsDesignation:"b3f2a7c1-8e6d-4cfa-bb5c-2e8f4a6c2e58",

chsCreateEndpoint:"api/v1/individual/create-short-form-chs",
INDIVIDUAL_CREATE_RES_V1:"application/vnd-chidhagni-houzer.individual.create.res-v1+json",
INDIVIDUAL_CREATE_REQ_V1:"application/vnd-chidhagni-houzer.individual.create.req-v1+json",

spCreateEndpoint:"api/v1/individual/create-short-form-sp",
INDIVIDUAL_CREATE_SP_REQ_V1:"application/vnd-chidhagni-houzer.individual.create.sp.req-v1+json",
INDIVIDUAL_CREATE_SP_RES_V1:"application/vnd-chidhagni-houzer.individual.create.sp.res-v1+json",

sendOTP:"auth/api/v1/send-otp",
OTP_SENT_RESPONSE:"application/vnd-chidhagni-houzer.sent.otp.res-v1+json",
OTP_SENT_REQUEST:"application/vnd-chidhagni-houzer.sent.otp.req-v1+json",
verifyOTP:"auth/api/v1/verify-otp",
OTP_VERIFY_REQUEST:"application/vnd-chidhagni-houzer.verify.otp.req-v1+json",
OTP_VERIFY_RESPONSE:"application/vnd-chidhagni-houzer.verify.otp.res-v1+json",
resendOTP:"auth/api/v1/resend-otp",
OTP_RE_SENT_REQUEST:"application/vnd-chidhagni-houzer.verify.otp.re.send.req-v1+json",
OTP_RE_SENT_RESPONSE:"application/vnd-chidhagni-houzer.verify.otp.re.send.res-v1+json",

signUpEndpoint:"auth/api/v1/individual-signup",
activationEndpoint:"auth/api/v1/activate",
createPasswordEndpoint:"auth/create-password",
loginEndpoint:"auth/api/v1/individual-login",
forgotPasswordEndpoint:"auth/forgot-password-individual",
resetPasswordEndpoint:"auth/reset-password",
logoutEndpoint:"logout-individual",
getReadinessDataEndpoint:"api/v1/organisation/api/v1",
ORGANISATION_CONTEXTUAL_DATA_GET_BY_ID_V1:"application/vnd-chidhagni-houzer.organisation.contextual.data.get.by.organisation.id-res-v1+json",

SP_ORGANISATION_META_DATA_GET_BY_ID_V1:"application/vnd-chidhagni-houzer.sp.organisation.get.by.organisation.id-res-v1+json",
ORGANISATION_PATCH_SP_META_DATA_BY_ID_REQ_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.metadata.by.organisation.id-req-v1+json",
ORGANISATION_PATCH_SP_META_DATA_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.metadata.by.organisation.id-res-v1+json",

STATISTICS_GET_SP_CHS_STATISTICS_REQUEST:"application/vnd-chidhagni-houzer.sp.chs.statistics.req-v1+json",  
STATISTICS_GET_SP_CHS_STATISTICS_RESPONSE:"application/vnd-chidhagni-houzer.sp.chs.statistics.res-v1+json",
STATISTICS_GET_SR_STATISTICS_REQUEST:"application/vnd-chidhagni-houzer.sr.statistics.req-v1+json",
STATISTICS_GET_SR_STATISTICS_RESPONSE:"application/vnd-chidhagni-houzer.sr.statistics.req-v1+json",
salesTeamMembers:"cfc0d7ce-3468-47c8-96a5-134f078118e4",
typeOfProjects:"5e36cf2c-8e67-45c5-ae58-d53bc67afd5a",
companyType:"5edbaff0-c37d-4394-ab7b-1c6bccc58239",
referralType:"d24579db-0191-4d8b-b10f-a77af6cd9e24",
referralName:"a0a4fe5e-5038-4abc-b451-aa4e920dbdfd",
addressTypeId:"4eba9bb1-18f9-4727-be0f-1a51713918de",
areaOfOperation:"83755f9e-dcd9-4b9a-8513-28a82cc6c006",
budgetCondition:"e9f16477-de0c-4222-b0d0-6456fc8b4dd5",
budgetUnits:"cec73b8c-3fe8-4206-afed-c70bcced25c8",

ORGANISATION_PATCH_SP_LEVEL1_CONTEXTUAL_DATA_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.sp.level1.contextual.by.organisation.id-res-v1+json",
ORGANISATION_PATCH_SP_LEVEL1_CONTEXTUAL_DATA_BY_ID_REQ_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.sp.level1.contextual.by.organisation.id-req-v1+json",
ORGANISATION_PATCH_SP_LEVEL2_CONTEXTUAL_DATA_BY_ID_REQ_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.sp.level2.contextual.by.organisation.id-req-v1+json",
ORGANISATION_PATCH_SP_LEVEL2_CONTEXTUAL_DATA_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.sp.level2.contextual.by.organisation.id-res-v1+json",
ORGANISATION_PATCH_SP_LISTS_CONTEXTUAL_DATA_BY_ID_REQ_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.sp.lists.contextual.by.organisation.id-req-v1+json",
ORGANISATION_PATCH_SP_LISTS_CONTEXTUAL_DATA_BY_ID_RES_V1:"application/vnd-chidhagni-houzer.sp.organisation.patch.sp.lists.contextual.by.organisation.id-res-v1+json",
INDIVIDUAL_GET_ALL_CONTACT_GROUP_REQ_V1:"application/vnd-chidhagni-houzer.individual.get.all.contact.group.req-v1+json",
INDIVIDUAL_GET_ALL_CONTACT_GROUP_RES_V1:"application/vnd-chidhagni-houzer.individual.get.all.contact.group.res-v1+json",

individualStatisticsUpdate:"user-services/individual-statistics",
organisationProjects:"api/v1/organisation/projects",
ChsDesignation:"b3f2a7c1-8e6d-4cfa-bb5c-2e8f4a6c2e58",

stateListNameId:"f17a2e1c-c287-4380-8bcb-54d5f23f6cd0",
donationListNameId:"f3a2a6e3-4eaf-4d26-8bc9-1c7c2a8c4db8",
paymentModeListNameId:"c2124508-c1fa-40c0-b2df-f6a5c69c3cc5",
paymentTypeListNameId:"f47a1c89-42a3-4621-bd5e-9b8ad65c9cf0",
listValuesAll:"list-values/all", 

donationHeadEndpoint:"donation-heads",
donationReceiptEndpoint:"donation-receipts",
rolesDropdownEndpoint:"admin/api/v1/roles",
individualVerificationAudit:"auth",
tenantMemberRoleId:"f5c03c6e-3e4f-45f2-b06f-207a59f0e312",
dashboardStatisticsEndpoint:"api/v1/statistics",
donorRoleId:"6f1c3911-94a7-4c86-9444-8f1a5ac72bfc",
tenantAdminRoleId:"c11e1f0a-cd55-41e6-92ec-8e7c41f72d67",

mobileOTPEndpoint:"api/v1/mobile-otp",
donorsEndpoint:"api/v1/donors",
donorImportEndpoint:"api/v1/donor-import",
donorGroupsEndpoint:"api/v1/donor-groups",
tagsListNameId:"b19e52fa-8a0f-4e84-9cc2-dcc99a464927",
};


