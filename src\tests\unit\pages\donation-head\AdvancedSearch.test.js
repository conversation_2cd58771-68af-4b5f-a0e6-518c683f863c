import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AdvancedSearch from '@/pages/donation-head/AdvancedSearch.js';
import { renderWithProviders, mockTenant } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
setupAllMocks();

// Mock the AdvancedSearch component
jest.mock('@/pages/donation-head/AdvancedSearch.js', () => {
  return function MockAdvancedSearch({
    open,
    toggle,
    selectedFilters = [],
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
    tenantsList = [],
  }) {
    const [formData, setFormData] = React.useState({
      donationHead: '',
      description: '',
      tenantName: '',
    });

    React.useEffect(() => {
      // Pre-fill form with selected filters
      const filterMap = new Map(selectedFilters.map(filter => [filter.key, filter.value]));
      setFormData({
        donationHead: filterMap.get('nameFilter') || '',
        description: filterMap.get('descriptionFilter') || '',
        tenantName: filterMap.get('orgIdFilter') || '',
      });
    }, [selectedFilters]);

    const handleInputChange = (field, value) => {
      setFormData(prev => ({ ...prev, [field]: value }));
    };

    const handleApply = () => {
      const filters = [];
      
      if (formData.tenantName) {
        filters.push({ key: 'orgIdFilter', label: 'NGO Name', value: formData.tenantName });
      }
      if (formData.donationHead) {
        filters.push({ key: 'nameFilter', label: 'Donation Head', value: formData.donationHead });
      }
      if (formData.description) {
        filters.push({ key: 'descriptionFilter', label: 'Description', value: formData.description });
      }

      onApplyFilters(filters);
      setSearchingState(true);
      toggle();
    };

    const handleCancel = () => {
      setFormData({ donationHead: '', description: '', tenantName: '' });
      setSearchingState(false);
      clearAllFilters();
      toggle();
    };

    if (!open) return null;

    return (
      <div data-testid="advanced-search-drawer" role="dialog" aria-label="Advanced Search">
        <div data-testid="drawer-backdrop" onClick={toggle} />
        
        <div data-testid="drawer-content">
          <div data-testid="drawer-header">
            <h2>Advanced Search</h2>
            <button data-testid="close-drawer-btn" onClick={toggle}>
              ×
            </button>
          </div>

          <div data-testid="search-form">
            <div data-testid="form-fields">
              <input
                data-testid="donation-head-filter"
                placeholder="Donation Head Name"
                value={formData.donationHead}
                onChange={(e) => handleInputChange('donationHead', e.target.value)}
              />

              <textarea
                data-testid="description-filter"
                placeholder="Description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />

              {tenantsList.length > 0 && (
                <select
                  data-testid="tenant-filter"
                  value={formData.tenantName}
                  onChange={(e) => handleInputChange('tenantName', e.target.value)}
                >
                  <option value="">Select NGO</option>
                  {tenantsList.map((tenant) => (
                    <option key={tenant.value} value={tenant.value}>
                      {tenant.key}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <div data-testid="filter-actions">
              <button
                data-testid="apply-filters-btn"
                onClick={handleApply}
              >
                Apply Filters
              </button>
              
              <button
                data-testid="cancel-filters-btn"
                onClick={handleCancel}
              >
                Cancel
              </button>
            </div>
          </div>

          <div data-testid="active-filters">
            {selectedFilters.length > 0 && (
              <div>
                <h3>Active Filters:</h3>
                {selectedFilters.map((filter, index) => (
                  <div key={index} data-testid={`active-filter-${index}`}>
                    <span>{filter.label}: {filter.value}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
});

describe('AdvancedSearch', () => {
  const mockProps = {
    open: true,
    toggle: jest.fn(),
    selectedFilters: [],
    setSearchingState: jest.fn(),
    clearAllFilters: jest.fn(),
    onApplyFilters: jest.fn(),
    tenantsList: [
      mockTenant({ value: 'org-1', key: 'Organization 1' }),
      mockTenant({ value: 'org-2', key: 'Organization 2' }),
    ],
  };

  const mockUser = {
    id: '1',
    name: 'Test User',
    organisationCategory: 'SUPER_ADMIN',
    orgId: 'org-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders advanced search drawer when open', () => {
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('advanced-search-drawer')).toBeInTheDocument();
      expect(screen.getByTestId('drawer-content')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      renderWithProviders(<AdvancedSearch {...mockProps} open={false} />, { user: mockUser });

      expect(screen.queryByTestId('advanced-search-drawer')).not.toBeInTheDocument();
    });

    it('renders all filter fields', () => {
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('donation-head-filter')).toBeInTheDocument();
      expect(screen.getByTestId('description-filter')).toBeInTheDocument();
      expect(screen.getByTestId('tenant-filter')).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('apply-filters-btn')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-filters-btn')).toBeInTheDocument();
    });

    it('shows tenant filter for super admin users', () => {
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('tenant-filter')).toBeInTheDocument();
    });
  });

  describe('Filter Interactions', () => {
    it('handles donation head filter input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      const input = screen.getByTestId('donation-head-filter');
      await user.type(input, 'Education');

      expect(input).toHaveValue('Education');
    });

    it('handles description filter input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      const textarea = screen.getByTestId('description-filter');
      await user.type(textarea, 'Test description');

      expect(textarea).toHaveValue('Test description');
    });

    it('handles tenant selection', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      const select = screen.getByTestId('tenant-filter');
      await user.selectOptions(select, 'org-1');

      expect(select).toHaveValue('org-1');
    });
  });

  describe('Filter Application', () => {
    it('applies filters with correct data structure', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      // Fill in filters
      await user.type(screen.getByTestId('donation-head-filter'), 'Education');
      await user.type(screen.getByTestId('description-filter'), 'Education fund');
      await user.selectOptions(screen.getByTestId('tenant-filter'), 'org-1');

      // Apply filters
      await user.click(screen.getByTestId('apply-filters-btn'));

      expect(mockProps.onApplyFilters).toHaveBeenCalledWith([
        { key: 'orgIdFilter', label: 'NGO Name', value: 'org-1' },
        { key: 'nameFilter', label: 'Donation Head', value: 'Education' },
        { key: 'descriptionFilter', label: 'Description', value: 'Education fund' },
      ]);
    });

    it('sets searching state when filters are applied', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      await user.type(screen.getByTestId('donation-head-filter'), 'Education');
      await user.click(screen.getByTestId('apply-filters-btn'));

      expect(mockProps.setSearchingState).toHaveBeenCalledWith(true);
    });

    it('closes drawer after applying filters', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      await user.type(screen.getByTestId('donation-head-filter'), 'Education');
      await user.click(screen.getByTestId('apply-filters-btn'));

      expect(mockProps.toggle).toHaveBeenCalled();
    });

    it('applies only non-empty filters', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      // Only fill donation head filter
      await user.type(screen.getByTestId('donation-head-filter'), 'Education');
      await user.click(screen.getByTestId('apply-filters-btn'));

      expect(mockProps.onApplyFilters).toHaveBeenCalledWith([
        { key: 'nameFilter', label: 'Donation Head', value: 'Education' },
      ]);
    });
  });

  describe('Filter Cancellation', () => {
    it('clears all filters when cancel is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      // Fill some filters
      await user.type(screen.getByTestId('donation-head-filter'), 'Education');
      await user.click(screen.getByTestId('cancel-filters-btn'));

      expect(mockProps.clearAllFilters).toHaveBeenCalled();
      expect(mockProps.setSearchingState).toHaveBeenCalledWith(false);
      expect(mockProps.toggle).toHaveBeenCalled();
    });

    it('resets form fields when cancel is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      // Fill and then cancel
      await user.type(screen.getByTestId('donation-head-filter'), 'Education');
      await user.click(screen.getByTestId('cancel-filters-btn'));

      // Form should be reset (this would be tested in the actual component)
      expect(mockProps.clearAllFilters).toHaveBeenCalled();
    });
  });

  describe('Selected Filters Display', () => {
    it('displays active filters when provided', () => {
      const propsWithFilters = {
        ...mockProps,
        selectedFilters: [
          { key: 'nameFilter', label: 'Donation Head', value: 'Education' },
          { key: 'descriptionFilter', label: 'Description', value: 'Education fund' },
        ],
      };

      renderWithProviders(<AdvancedSearch {...propsWithFilters} />, { user: mockUser });

      expect(screen.getByText('Active Filters:')).toBeInTheDocument();
      expect(screen.getByText('Donation Head: Education')).toBeInTheDocument();
      expect(screen.getByText('Description: Education fund')).toBeInTheDocument();
    });

    it('pre-fills form with selected filters', () => {
      const propsWithFilters = {
        ...mockProps,
        selectedFilters: [
          { key: 'nameFilter', label: 'Donation Head', value: 'Education' },
          { key: 'descriptionFilter', label: 'Description', value: 'Education fund' },
          { key: 'orgIdFilter', label: 'NGO Name', value: 'org-1' },
        ],
      };

      renderWithProviders(<AdvancedSearch {...propsWithFilters} />, { user: mockUser });

      expect(screen.getByTestId('donation-head-filter')).toHaveValue('Education');
      expect(screen.getByTestId('description-filter')).toHaveValue('Education fund');
      expect(screen.getByTestId('tenant-filter')).toHaveValue('org-1');
    });
  });

  describe('Drawer Actions', () => {
    it('closes drawer when close button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      await user.click(screen.getByTestId('close-drawer-btn'));

      expect(mockProps.toggle).toHaveBeenCalled();
    });

    it('closes drawer when backdrop is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      await user.click(screen.getByTestId('drawer-backdrop'));

      expect(mockProps.toggle).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      const drawer = screen.getByTestId('advanced-search-drawer');
      expect(drawer).toHaveAttribute('role', 'dialog');
      expect(drawer).toHaveAttribute('aria-label', 'Advanced Search');
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<AdvancedSearch {...mockProps} />, { user: mockUser });

      // Tab through form elements
      await user.tab();
      expect(screen.getByTestId('donation-head-filter')).toHaveFocus();

      await user.tab();
      expect(screen.getByTestId('description-filter')).toHaveFocus();
    });
  });
});
