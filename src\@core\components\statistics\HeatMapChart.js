import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

// Function to generate data
const generateData = (neighborhoods, yrange) => {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  return neighborhoods.map(neighborhood => {
    const data = [];
    for (let i = 0; i < 12; i++) { // Assuming 12 months
      const y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
      data.push({ x: monthNames[i], y });
    }
    return { name: neighborhood, data };
  });
};

const HeatMapChart = () => {
  const neighborhoods = [
    'Andheri', '<PERSON>ra', 'Ju<PERSON>', 'Colaba', 'Dadar', 
    'Goregaon', 'Ku<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>e'
  ];

  const state = {
    series: generateData(neighborhoods, { min: 0, max: 100 }), // Example range for redevelopment progress
    options: {
      chart: {
        height: 350,
        width: '100%',
        type: 'heatmap',
      },
      plotOptions: {
        heatmap: {
          shadeIntensity: 0.5,
          radius: 0,
          useFillColorAsStroke: true,
          colorScale: {
            ranges: [
              {
                from: 0,
                to: 25,
                name: 'Initial Stages',
                color: '#00A100',
              },
              {
                from: 26,
                to: 50,
                name: 'Planning',
                color: '#128FD9',
              },
              {
                from: 51,
                to: 75,
                name: 'Under Construction',
                color: '#FFB200',
              },
              {
                from: 76,
                to: 100,
                name: 'Near Completion',
                color: '#FF0000',
              },
            ],
          },
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: 1,
      },
      title: {
        text: 'Redevelopment Progress Across Mumbai Neighborhoods',
      },
      xaxis: {
        title: {
          text: 'Months of the Year',
        },
      },
      yaxis: {
        title: {
          text: 'Neighborhoods',
        },
      },
    },
  };

  return (
    <div style={{ width: '100%', maxWidth: '1000px', margin: '0 auto', display: 'flex', justifyContent: 'center' }}>
      <div id="chart" style={{ width: '100%' }}>
        <ApexChart options={state.options} series={state.series} type="heatmap" height={350} />
      </div>
    </div>
  );
};

export default HeatMapChart;
