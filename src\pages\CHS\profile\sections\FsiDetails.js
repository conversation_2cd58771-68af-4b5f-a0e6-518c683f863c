// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section1 from 'src/pages/SP/broker/sections/Section1'
import { useTheme } from '@emotion/react'

// ** Styled Component
import { Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material'
import styled from '@emotion/styled'
import PageHeader from 'src/@core/components/page-header'
import Section2 from './Section2'
import Section3 from './Section3'
import MUITableCell from "src/pages/SP/MUITableCell";
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};


const FsiDetails = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
    const theme = useTheme()

    const [state3, setState3] = useState(true)

    const handleState3 = () => {
      setState3(!state3)
    }
    
    
    // Pre-Populating code Start
    // const [fsiDetails, setFsiDetails] = useState({
    //     buildingAge:"",
    //     heightRestriction:"",
    //     fsiConsumedFsi:"",
    //     fsi_AvailableFsi:"",
    //     fsi_PermissibleFsi:"",
    //     scheme:"",
    //     dpRestrictions:"",
    //     litigationsOrEncroachment:""
    //   });

    return (
        <>
         {/* {can('society_fsiDetails_READ') && */}
          <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'FSI'}
                body={
                  <>
                    {state3 && (                     
                          <TableContainer sx={{ padding:'4px 6px', cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer'  }}
                            className='tableBody'
                           // onClick={can('society_fsiDetails_UPDATE') ? handleState3 : null}>
                            onClick={ handleState3}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Consumed FSI:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.fsiConsumedFsi}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Available FSI:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.fsi_AvailableFsi}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Permissable FSI :</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.fsi_PermissibleFsi}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Scheme:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                  <Typography className='data-field'>
                                    {data?.scheme === '_33_7B' ? '33(7B)' : data?.scheme}
                                 </Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>DP Restrictions:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.dpRestrictions}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>

                                  <MUITableCell>
                                    <Typography style={field}>Litigation/Encroachment:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.litigationsOrEncroachment}</Typography>
                                  </MUITableCell>
                                </TableRow>

                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Building Age:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.buildingAge}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Height Restriction:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.heightRestriction}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        

                    )}
                    {!state3 && <Section3 formData={data} onCancel={handleState3} />}
                  </>
                }
                expanded={expanded}
              />
              {/* } */}
        </>
    );

}
export default FsiDetails;