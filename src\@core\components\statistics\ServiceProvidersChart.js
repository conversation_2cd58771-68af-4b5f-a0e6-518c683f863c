import dynamic from "next/dynamic";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ServiceProvidersChart = () => {
  const { listValues } = useContext(AuthContext);
  const [sampleData, setSampleData] = useState([]);
  const [chartData, setChartData] = useState({
    series: [],
    options: {
      chart: {
        type: "bar",
        height: 400,
      },
      title: {
        text: "SP's Packages",
        align: "center",
        style: {
          fontSize: "18px",
          fontWeight: "bold",
        },
      },
      xaxis: {
        categories: [],
        title: {
          text: "Packages",
        },
      },
      yaxis: {
        title: {
          text: "Number of SP's",
        },
      },
      plotOptions: {
        bar: {
          distributed: true, // Enable individual bar colors
        },
      },
      colors: ["#FF5733", "#33FF57", "#3357FF", "#F1C40F", "#8E44AD", "#2ECC71"], // Colors for each bar
    },
  });

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.statisticsEndpointGraphs) + "/service-provider-count-group-by-package-id",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Statistics error", err));
  }, []);

  useEffect(() => {
    const categories = sampleData?.map(
      (item) =>
        listValues.find((listItem) => listItem.id === item.packageId)?.name || item.packageId
    );

    const counts = sampleData?.map((item) => item.count);

    setChartData((prev) => ({
      ...prev,
      series: [
        {
          name: "Number of SP's",
          data: counts || [],
        },
      ],
      options: {
        ...prev.options,
        xaxis: {
          ...prev.options.xaxis,
          categories: categories || [],
        },
      },
    }));
  }, [sampleData, listValues]);

  return (
    <div>
      <ApexChart
        options={chartData.options}
        series={chartData.series}
        type="bar"
        height={400}
      />
    </div>
  );
};

export default ServiceProvidersChart;
