import axios from 'axios';
import { mockStatsData, mockChartData, mockDistributionData, mockApiResponse } from '../../utils/donationTestUtils';
import { setupAllMocks } from '../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// API service functions for donation tenant dashboard
const donationTenantApi = {
  getTenantStats: async () => {
    const response = await axios.get('/api/tenant/dashboard/stats');
    return response.data;
  },

  getDonationTrends: async (period = 'yearly') => {
    const response = await axios.get(`/api/tenant/dashboard/trends?period=${period}`);
    return response.data;
  },

  getDonationDistribution: async () => {
    const response = await axios.get('/api/tenant/dashboard/distribution');
    return response.data;
  },

  getDonationHeads: async () => {
    const response = await axios.get('/api/tenant/donation-heads');
    return response.data;
  },

  getRecentActivities: async (limit = 10) => {
    const response = await axios.get(`/api/tenant/dashboard/activities?limit=${limit}`);
    return response.data;
  },

  getTopDonors: async (limit = 5) => {
    const response = await axios.get(`/api/tenant/dashboard/top-donors?limit=${limit}`);
    return response.data;
  },

  getDonationsByHead: async (headId) => {
    const response = await axios.get(`/api/tenant/donation-heads/${headId}/donations`);
    return response.data;
  },

  exportDashboardData: async (format = 'csv') => {
    const response = await axios.get(`/api/tenant/dashboard/export?format=${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  updateDonationHead: async (headId, data) => {
    const response = await axios.patch(`/api/tenant/donation-heads/${headId}`, data);
    return response.data;
  },

  createDonationReceipt: async (data) => {
    const response = await axios.post('/api/tenant/donation-receipts', data);
    return response.data;
  },
};

describe('Donation Tenant API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/tenant/dashboard/stats', () => {
    it('fetches tenant dashboard statistics successfully', async () => {
      const mockStats = {
        totalDonations: 125000,
        uniqueDonors: 45,
        last30DaysDonations: 8,
        averageDonation: 2777,
        donationHeadsCount: 5,
        topHead: 'Education',
        organizationName: 'Education Foundation',
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockStats));

      const result = await donationTenantApi.getTenantStats();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/stats');
      expect(result).toEqual(mockStats);
    });

    it('handles empty tenant stats data', async () => {
      const emptyStats = {
        totalDonations: 0,
        uniqueDonors: 0,
        last30DaysDonations: 0,
        averageDonation: 0,
        donationHeadsCount: 0,
        topHead: null,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(emptyStats));

      const result = await donationTenantApi.getTenantStats();

      expect(result.totalDonations).toBe(0);
      expect(result.topHead).toBeNull();
    });

    it('handles API errors for tenant stats', async () => {
      mockAxios.get.mockRejectedValue(new Error('Tenant stats service unavailable'));

      await expect(donationTenantApi.getTenantStats()).rejects.toThrow('Tenant stats service unavailable');
    });
  });

  describe('GET /api/tenant/dashboard/trends', () => {
    it('fetches donation trends for yearly period', async () => {
      const mockData = {
        period: 'yearly',
        series: [{ name: 'Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }],
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        total: 42000,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationTrends('yearly');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/trends?period=yearly');
      expect(result).toEqual(mockData);
    });

    it('fetches donation trends for different periods', async () => {
      const mockData = {
        period: 'weekly',
        series: [{ name: 'Donations', data: [50, 30, 80, 60, 90, 70, 100] }],
        categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        total: 480,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationTrends('weekly');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/trends?period=weekly');
      expect(result.period).toBe('weekly');
      expect(result.series[0].data).toHaveLength(7);
    });

    it('defaults to yearly period when no period specified', async () => {
      const mockData = {
        period: 'yearly',
        series: [{ name: 'Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }],
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationTrends();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/trends?period=yearly');
    });
  });

  describe('GET /api/tenant/dashboard/distribution', () => {
    it('fetches donation distribution data successfully', async () => {
      const mockData = {
        series: [3500, 2800, 2200, 1800, 1200],
        labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
        percentages: [30.4, 24.3, 19.1, 15.7, 10.4],
        total: 11500,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationDistribution();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/distribution');
      expect(result.series).toHaveLength(5);
      expect(result.labels).toHaveLength(5);
      expect(result.percentages).toHaveLength(5);
    });

    it('handles empty distribution data', async () => {
      const emptyData = {
        series: [],
        labels: [],
        percentages: [],
        total: 0,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(emptyData));

      const result = await donationTenantApi.getDonationDistribution();

      expect(result.series).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('GET /api/tenant/donation-heads', () => {
    it('fetches donation heads successfully', async () => {
      const mockData = [
        { id: '1', name: 'Education Fund', totalAmount: 3500, percentage: 30.4, isActive: true, donationCount: 25 },
        { id: '2', name: 'Healthcare Fund', totalAmount: 2800, percentage: 24.3, isActive: true, donationCount: 20 },
        { id: '3', name: 'Food Aid Fund', totalAmount: 2200, percentage: 19.1, isActive: false, donationCount: 15 },
      ];

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationHeads();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/donation-heads');
      expect(result).toHaveLength(3);
      expect(result[0].name).toBe('Education Fund');
    });

    it('handles empty donation heads list', async () => {
      mockAxios.get.mockResolvedValue(mockApiResponse([]));

      const result = await donationTenantApi.getDonationHeads();

      expect(result).toHaveLength(0);
    });
  });

  describe('GET /api/tenant/dashboard/activities', () => {
    it('fetches recent activities successfully', async () => {
      const mockData = [
        {
          id: '1',
          type: 'donation',
          description: 'New donation received',
          amount: 5000,
          timestamp: '2024-01-01T10:00:00Z',
          donor: 'John Doe',
          headName: 'Education Fund',
        },
        {
          id: '2',
          type: 'head_update',
          description: 'Donation head updated',
          timestamp: '2024-01-01T08:00:00Z',
          headName: 'Healthcare Fund',
        },
      ];

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getRecentActivities();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/activities?limit=10');
      expect(result).toHaveLength(2);
    });

    it('fetches activities with custom limit', async () => {
      const mockData = [
        {
          id: '1',
          type: 'donation',
          description: 'New donation received',
          amount: 5000,
          timestamp: '2024-01-01T10:00:00Z',
        },
      ];

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getRecentActivities(5);

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/activities?limit=5');
      expect(result).toHaveLength(1);
    });
  });

  describe('GET /api/tenant/dashboard/top-donors', () => {
    it('fetches top donors successfully', async () => {
      const mockData = [
        { id: '1', name: 'John Doe', totalDonated: 15000, donationCount: 8, lastDonation: '2024-01-01T10:00:00Z' },
        { id: '2', name: 'Jane Smith', totalDonated: 12000, donationCount: 6, lastDonation: '2023-12-28T15:30:00Z' },
        { id: '3', name: 'Bob Johnson', totalDonated: 10000, donationCount: 5, lastDonation: '2023-12-25T09:15:00Z' },
      ];

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getTopDonors();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/top-donors?limit=5');
      expect(result).toHaveLength(3);
    });

    it('fetches top donors with custom limit', async () => {
      const mockData = [
        { id: '1', name: 'John Doe', totalDonated: 15000, donationCount: 8 },
      ];

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getTopDonors(3);

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/top-donors?limit=3');
      expect(result).toHaveLength(1);
    });
  });

  describe('GET /api/tenant/donation-heads/:id/donations', () => {
    it('fetches donations for specific head successfully', async () => {
      const mockData = {
        donationHead: { id: '1', name: 'Education Fund' },
        donations: [
          { id: '1', amount: 1000, donor: 'John Doe', date: '2024-01-01T10:00:00Z' },
          { id: '2', amount: 2000, donor: 'Jane Smith', date: '2024-01-02T14:30:00Z' },
        ],
        total: 3000,
        count: 2,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationsByHead('1');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/donation-heads/1/donations');
      expect(result.donations).toHaveLength(2);
      expect(result.total).toBe(3000);
    });

    it('handles empty donations for head', async () => {
      const mockData = {
        donationHead: { id: '1', name: 'Education Fund' },
        donations: [],
        total: 0,
        count: 0,
      };

      mockAxios.get.mockResolvedValue(mockApiResponse(mockData));

      const result = await donationTenantApi.getDonationsByHead('1');

      expect(result.donations).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('GET /api/tenant/dashboard/export', () => {
    it('exports tenant dashboard data as CSV', async () => {
      const mockBlob = new Blob(['tenant,csv,data'], { type: 'text/csv' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      const result = await donationTenantApi.exportDashboardData('csv');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/export?format=csv', {
        responseType: 'blob',
      });
      expect(result).toBeInstanceOf(Blob);
    });

    it('exports tenant dashboard data as Excel', async () => {
      const mockBlob = new Blob(['tenant,excel,data'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      const result = await donationTenantApi.exportDashboardData('xlsx');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/export?format=xlsx', {
        responseType: 'blob',
      });
      expect(result).toBeInstanceOf(Blob);
    });
  });

  describe('PATCH /api/tenant/donation-heads/:id', () => {
    it('updates donation head successfully', async () => {
      const updateData = {
        name: 'Updated Education Fund',
        description: 'Updated description',
        isActive: false,
      };

      const mockResponse = {
        id: '1',
        ...updateData,
        updatedOn: '2024-01-02T10:00:00Z',
      };

      mockAxios.patch.mockResolvedValue(mockApiResponse(mockResponse));

      const result = await donationTenantApi.updateDonationHead('1', updateData);

      expect(mockAxios.patch).toHaveBeenCalledWith('/api/tenant/donation-heads/1', updateData);
      expect(result).toEqual(mockResponse);
    });

    it('handles validation errors during update', async () => {
      const invalidData = { name: '' };

      mockAxios.patch.mockRejectedValue({
        response: {
          status: 400,
          data: {
            message: 'Validation failed',
            errors: ['Name is required'],
          },
        },
      });

      await expect(donationTenantApi.updateDonationHead('1', invalidData)).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });
  });

  describe('POST /api/tenant/donation-receipts', () => {
    it('creates donation receipt successfully', async () => {
      const receiptData = {
        donorId: 'donor-1',
        donationHeadId: 'head-1',
        amount: 5000,
        paymentMode: 'online',
        paymentDetails: 'Credit card payment',
        receiptDate: '2024-01-01',
      };

      const mockResponse = {
        id: 'receipt-1',
        ...receiptData,
        receiptNumber: 'RCP-2024-001',
        createdOn: '2024-01-01T10:00:00Z',
      };

      mockAxios.post.mockResolvedValue(mockApiResponse(mockResponse));

      const result = await donationTenantApi.createDonationReceipt(receiptData);

      expect(mockAxios.post).toHaveBeenCalledWith('/api/tenant/donation-receipts', receiptData);
      expect(result).toEqual(mockResponse);
    });

    it('handles validation errors during receipt creation', async () => {
      const invalidData = { amount: -100 };

      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: {
            message: 'Invalid donation amount',
          },
        },
      });

      await expect(donationTenantApi.createDonationReceipt(invalidData)).rejects.toMatchObject({
        response: {
          status: 400,
        },
      });
    });
  });

  describe('Error Handling', () => {
    it('handles network timeouts', async () => {
      mockAxios.get.mockRejectedValue(new Error('timeout of 5000ms exceeded'));

      await expect(donationTenantApi.getTenantStats()).rejects.toThrow('timeout of 5000ms exceeded');
    });

    it('handles server errors (500)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 500,
          data: {
            message: 'Internal server error',
          },
        },
      });

      await expect(donationTenantApi.getDonationTrends()).rejects.toMatchObject({
        response: {
          status: 500,
        },
      });
    });

    it('handles unauthorized access (401)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 401,
          data: {
            message: 'Unauthorized access',
          },
        },
      });

      await expect(donationTenantApi.getTenantStats()).rejects.toMatchObject({
        response: {
          status: 401,
        },
      });
    });

    it('handles forbidden access (403)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 403,
          data: {
            message: 'Insufficient permissions for tenant dashboard',
          },
        },
      });

      await expect(donationTenantApi.getDonationDistribution()).rejects.toMatchObject({
        response: {
          status: 403,
        },
      });
    });

    it('handles not found errors (404)', async () => {
      mockAxios.get.mockRejectedValue({
        response: {
          status: 404,
          data: {
            message: 'Donation head not found',
          },
        },
      });

      await expect(donationTenantApi.getDonationsByHead('999')).rejects.toMatchObject({
        response: {
          status: 404,
        },
      });
    });
  });

  describe('Request Configuration', () => {
    it('includes proper headers for API requests', async () => {
      mockAxios.get.mockResolvedValue(mockApiResponse({}));

      await donationTenantApi.getTenantStats();

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/stats');
    });

    it('handles blob responses for export functionality', async () => {
      const mockBlob = new Blob(['test'], { type: 'text/csv' });
      mockAxios.get.mockResolvedValue({ data: mockBlob });

      const result = await donationTenantApi.exportDashboardData('csv');

      expect(mockAxios.get).toHaveBeenCalledWith('/api/tenant/dashboard/export?format=csv', {
        responseType: 'blob',
      });
      expect(result).toBeInstanceOf(Blob);
    });

    it('sends proper content-type for POST requests', async () => {
      mockAxios.post.mockResolvedValue(mockApiResponse({}));

      const data = { amount: 1000, donorId: 'donor-1' };
      await donationTenantApi.createDonationReceipt(data);

      expect(mockAxios.post).toHaveBeenCalledWith('/api/tenant/donation-receipts', data);
    });
  });
});
