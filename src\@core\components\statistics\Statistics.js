import React, { useContext, useEffect, useState } from "react";
import { FaBriefcase, FaHome, FaUsers } from "react-icons/fa";
import { Box, Typography, Grid, Paper, Card } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/bi";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";

const Statistics = () => {
  const [sampleData, setSampleData] = useState([]);
  const router = useRouter();

  const { user } = useContext(AuthContext);
  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs + "/admin/individuals-group-by-role"
      );
    } else {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs + "/individuals-group-by-role"
      );
    }

    let headers;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        accept: authConfig?.STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        accept:
          authConfig?.STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_EMPLOYEE_V1,
      });
    }
    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);
  const stats = [
    {
      actor: "Service Providers",
      icon: <FaBriefcase size={32} color="#60C260" />,
      value: sampleData?.spCount,
      route: "/SP",
    },
    {
      actor: "Societies",
      icon: <FaHome size={32} color="#108A00" />,
      value: sampleData?.chsCount,
      route: "/CHS",
    },
    {
      actor: "Houzer Team",
      icon: <FaUsers size={32} color="#0A5900" />,
      value: sampleData?.employeeCount,
      route: user?.roleId === authConfig.superAdminRoleId ? "/employees" : null,
    },
  ];

  const handleCardClick = (route) => {
    if (user?.roleId === authConfig?.superAdminRoleId) {
      router.push(route);
    } else {
      router.push({
        pathname: route,
        query: { assigned: true },
      });
    }
  };

  return (
    <Card sx={{ mb: 2, p: 2, borderRadius: 2, boxShadow: 2 }}>
      <Box sx={{ mb: 1 }}>
        {/* Header Section */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <BiLineChart
            size={20}
            color="#108A00"
            style={{ marginRight: "8px" }}
          />
          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
            Statistics
          </Typography>
        </Box>

        {/* Statistics Grid */}
        <Grid container spacing={3}>
          {stats?.map((stat, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Paper
                elevation={3}
                sx={{
                  p: 2,
                  textAlign: "center",
                  borderRadius: 2,
                  backgroundColor: "#f9f9f9",
                  cursor: "pointer",
                }}
                onClick={() => stat.route && handleCardClick(stat.route)}
              >
                {/* Icon */}
                <Box sx={{ mb: 1 }}>{stat.icon}</Box>

                {/* Actor Name */}
                <Typography
                  variant="body1"
                  sx={{ fontWeight: "bold", color: "#555", mb: 0.5 }}
                >
                  {stat.actor}
                </Typography>

                {/* Value */}
                <Typography
                  variant="h5"
                  sx={{ fontWeight: "bold", color: "#333", mb: 0.5 }}
                >
                  {stat.value}
                </Typography>

                {/* Label */}
                <Typography
                  variant="body2"
                  sx={{ color: "#888", textTransform: "uppercase" }}
                >
                  {stat.label}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Card>
  );
};

export default Statistics;
