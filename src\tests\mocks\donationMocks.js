import React from 'react';

// Mock Next.js router
export const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  pathname: '/test',
  query: {},
  asPath: '/test',
  route: '/test',
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
};

// Mock axios
export const mockAxios = {
  get: jest.fn(() => Promise.resolve({ data: [] })),
  post: jest.fn(() => Promise.resolve({ data: { donationHeads: [], totalCount: 0 } })),
  put: jest.fn(() => Promise.resolve({ data: {} })),
  patch: jest.fn(() => Promise.resolve({ data: {} })),
  delete: jest.fn(() => Promise.resolve({ data: {} })),
  create: jest.fn(() => mockAxios),
};

// Mock ApexCharts
export const MockChart = ({ options, series, type, height, ...props }) => {
  return React.createElement('div', {
    'data-testid': 'mock-chart',
    'data-type': type,
    'data-height': height,
    'data-series': JSON.stringify(series),
    'data-options': JSON.stringify(options),
    ...props
  }, `Mock Chart (${type})`);
};

// Mock Material-UI DataGrid
export const MockDataGrid = ({ rows, columns, ...props }) => {
  return React.createElement('div', {
    'data-testid': 'mock-data-grid',
    'data-rows': JSON.stringify(rows),
    'data-columns': JSON.stringify(columns),
    ...props
  }, 'Mock DataGrid');
};

// Mock Fallback Spinner
export const MockFallbackSpinner = () => {
  return React.createElement('div', {
    'data-testid': 'fallback-spinner'
  }, 'Loading...');
};

// Mock custom components
export const MockSelectAutoComplete = ({ label, value, onChange, nameArray, ...props }) => {
  return React.createElement('select', {
    'data-testid': 'mock-select-autocomplete',
    'aria-label': label,
    value: value,
    onChange: (e) => onChange && onChange({ target: { value: e.target.value } }),
    ...props
  }, [
    React.createElement('option', { key: 'empty', value: '' }, `Select ${label}`),
    ...(nameArray || []).map(item =>
      React.createElement('option', { key: item.value, value: item.value }, item.key)
    )
  ]);
};

export const MockNameTextField = ({ label, value, onChange, error, ...props }) => {
  return React.createElement('input', {
    'data-testid': 'mock-name-textfield',
    'aria-label': label,
    placeholder: label,
    value: value,
    onChange: (e) => onChange && onChange(e.target.value),
    className: error ? 'error' : '',
    ...props
  });
};

export const MockIcon = ({ icon, fontSize, ...props }) => {
  return React.createElement('span', {
    'data-testid': 'mock-icon',
    'data-icon': icon,
    'data-fontsize': fontSize,
    className: 'mock-icon',
    ...props
  }, `Icon(${icon})`);
};

export const MockCustomAvatar = ({ children, onClick, ...props }) => {
  return React.createElement('div', {
    'data-testid': 'mock-custom-avatar',
    onClick: onClick,
    className: 'mock-avatar',
    ...props
  }, children);
};

export const MockCustomChip = ({ label, color, ...props }) => {
  return React.createElement('span', {
    'data-testid': 'mock-custom-chip',
    'data-label': label,
    'data-color': color,
    className: 'mock-chip',
    ...props
  }, label);
};

// Setup all mocks function
export const setupAllMocks = () => {
  // Mock Next.js router
  jest.mock('next/router', () => ({
    useRouter: () => mockRouter,
  }));

  // Mock Next.js dynamic imports
  jest.mock('next/dynamic', () => {
    return (fn) => {
      const Component = fn();
      return Component || MockChart;
    };
  });

  // Mock axios
  jest.mock('axios', () => mockAxios);

  // Mock react-apexcharts
  jest.mock('react-apexcharts', () => MockChart);

  // Mock Material-UI DataGrid
  jest.mock('@mui/x-data-grid', () => ({
    DataGrid: MockDataGrid,
  }));

  // Mock Fallback Spinner
  jest.mock('src/@core/components/spinner', () => MockFallbackSpinner);

  // Mock custom components
  jest.mock('src/@core/components/custom-components/SelectAutoComplete', () => MockSelectAutoComplete);
  jest.mock('src/@core/components/custom-components/NameTextField', () => MockNameTextField);
  jest.mock('src/@core/components/icon', () => MockIcon);
  jest.mock('src/@core/components/mui/avatar', () => MockCustomAvatar);
  jest.mock('src/@core/components/mui/chip', () => MockCustomChip);

  // Mock auth config
  jest.mock('src/configs/auth', () => ({
    donationHeadEndpoint: '/api/donation-heads',
    listValuesAll: '/api/list-values',
    storageTokenKeyName: 'accessToken',
  }));

  // Mock constants
  jest.mock('src/constants', () => ({
    MENUS: { LEFT: 'left' },
    PAGES: { DONATION_HEAD: 'donation-head' },
    PERMISSIONS: { READ: 'READ', WRITE: 'WRITE', DELETE: 'DELETE', FULL_ACCESS: 'FULL_ACCESS' },
  }));

  // Mock helper utils
  jest.mock('src/helpers/utils', () => ({
    getAuthorizationHeaders: jest.fn(() => ({ Authorization: 'Bearer test-token' })),
    getUrl: jest.fn((endpoint) => `http://localhost:8080${endpoint}`),
  }));

  // Mock useAuth hook
  jest.mock('src/hooks/useAuth', () => ({
    useAuth: () => ({
      postDonationHead: jest.fn(),
      patchDonationHead: jest.fn(),
      deleteDonationHead: jest.fn(),
    }),
  }));

  return { mockAxios, mockRouter };
};
