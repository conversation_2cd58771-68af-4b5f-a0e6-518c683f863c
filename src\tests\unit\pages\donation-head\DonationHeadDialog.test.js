import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DonationHeadDialog from '@/pages/donation-head/DonationHeadDialog.js';
import { renderWithProviders, mockDonationHead, mockTenant } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the dialog component
jest.mock('@/pages/donation-head/DonationHeadDialog.js', () => {
  return function MockDonationHeadDialog({ 
    open, 
    onClose, 
    formData, 
    fetchUsers, 
    page, 
    pageSize, 
    searchKeyword, 
    tenantsList 
  }) {
    const [saveLoading, setSaveLoading] = React.useState(false);
    const [formValues, setFormValues] = React.useState({
      donationHead: formData?.name || '',
      description: formData?.description || '',
      tenantName: formData?.orgId || '',
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      setSaveLoading(true);
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 100));
        fetchUsers?.(page, pageSize, searchKeyword);
        onClose();
      } catch (error) {
        console.error('Save failed:', error);
      } finally {
        setSaveLoading(false);
      }
    };

    const handleInputChange = (field, value) => {
      setFormValues(prev => ({ ...prev, [field]: value }));
    };

    if (!open) return null;

    return (
      <div data-testid="donation-head-dialog" role="dialog" aria-labelledby="dialog-title">
        <div data-testid="dialog-backdrop" onClick={onClose} />
        
        <div data-testid="dialog-content">
          <h2 id="dialog-title" data-testid="dialog-title">
            {formData?.id ? 'Edit Donation Head' : 'Add Donation Head'}
          </h2>
          
          <form data-testid="donation-head-form" onSubmit={handleSubmit}>
            <div data-testid="form-fields">
              <input
                data-testid="donation-head-input"
                placeholder="Donation Head Name"
                value={formValues.donationHead}
                onChange={(e) => handleInputChange('donationHead', e.target.value)}
                required
              />
              
              <textarea
                data-testid="description-input"
                placeholder="Description"
                value={formValues.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
              
              {tenantsList && tenantsList.length > 0 && (
                <select
                  data-testid="tenant-select"
                  value={formValues.tenantName}
                  onChange={(e) => handleInputChange('tenantName', e.target.value)}
                >
                  <option value="">Select Organization</option>
                  {tenantsList.map((tenant) => (
                    <option key={tenant.value} value={tenant.value}>
                      {tenant.key}
                    </option>
                  ))}
                </select>
              )}
            </div>
            
            <div data-testid="dialog-actions">
              <button
                type="submit"
                data-testid="save-button"
                disabled={saveLoading}
              >
                {saveLoading ? 'Saving...' : 'Save'}
              </button>
              
              <button
                type="button"
                data-testid="cancel-button"
                onClick={onClose}
                disabled={saveLoading}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };
});

describe('DonationHeadDialog', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    formData: {},
    fetchUsers: jest.fn(),
    page: 1,
    pageSize: 10,
    searchKeyword: '',
    tenantsList: [
      mockTenant({ value: 'org-1', key: 'Organization 1' }),
      mockTenant({ value: 'org-2', key: 'Organization 2' }),
    ],
  };

  const mockUser = {
    id: '1',
    name: 'Test User',
    organisationCategory: 'TENANT',
    orgId: 'org-1',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders dialog when open is true', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByTestId('dialog-content')).toBeInTheDocument();
    });

    it('does not render dialog when open is false', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} open={false} />, { user: mockUser });

      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });

    it('displays correct title for new donation head', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      expect(screen.getByText('Add Donation Head')).toBeInTheDocument();
    });

    it('displays correct title for editing donation head', () => {
      const editProps = {
        ...mockProps,
        formData: mockDonationHead({ id: '1', name: 'Test Head' }),
      };

      renderWithProviders(<DonationHeadDialog {...editProps} />, { user: mockUser });

      expect(screen.getByText('Edit Donation Head')).toBeInTheDocument();
    });

    it('renders all form fields', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('donation-head-input')).toBeInTheDocument();
      expect(screen.getByTestId('description-input')).toBeInTheDocument();
      expect(screen.getByTestId('tenant-select')).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      expect(screen.getByTestId('save-button')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    it('handles donation head name input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const input = screen.getByTestId('donation-head-input');
      await user.type(input, 'New Donation Head');

      expect(input).toHaveValue('New Donation Head');
    });

    it('handles description input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const textarea = screen.getByTestId('description-input');
      await user.type(textarea, 'Test description');

      expect(textarea).toHaveValue('Test description');
    });

    it('handles tenant selection', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const select = screen.getByTestId('tenant-select');
      await user.selectOptions(select, 'org-1');

      expect(select).toHaveValue('org-1');
    });

    it('pre-fills form with existing data', () => {
      const editProps = {
        ...mockProps,
        formData: mockDonationHead({
          id: '1',
          name: 'Existing Head',
          description: 'Existing description',
          orgId: 'org-1',
        }),
      };

      renderWithProviders(<DonationHeadDialog {...editProps} />, { user: mockUser });

      expect(screen.getByTestId('donation-head-input')).toHaveValue('Existing Head');
      expect(screen.getByTestId('description-input')).toHaveValue('Existing description');
      expect(screen.getByTestId('tenant-select')).toHaveValue('org-1');
    });
  });

  describe('Form Submission', () => {
    it('handles form submission successfully', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      // Fill form
      await user.type(screen.getByTestId('donation-head-input'), 'Test Head');
      await user.type(screen.getByTestId('description-input'), 'Test description');

      // Submit form
      const saveButton = screen.getByTestId('save-button');
      await user.click(saveButton);

      // Should show loading state
      expect(screen.getByText('Saving...')).toBeInTheDocument();

      // Wait for submission to complete
      await waitFor(() => {
        expect(mockProps.onClose).toHaveBeenCalled();
      });
    });

    it('disables buttons during submission', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const saveButton = screen.getByTestId('save-button');
      const cancelButton = screen.getByTestId('cancel-button');

      await user.click(saveButton);

      expect(saveButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();
    });

    it('calls fetchUsers after successful submission', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      await user.click(screen.getByTestId('save-button'));

      await waitFor(() => {
        expect(mockProps.fetchUsers).toHaveBeenCalledWith(
          mockProps.page,
          mockProps.pageSize,
          mockProps.searchKeyword
        );
      });
    });
  });

  describe('Dialog Actions', () => {
    it('closes dialog when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const cancelButton = screen.getByTestId('cancel-button');
      await user.click(cancelButton);

      expect(mockProps.onClose).toHaveBeenCalled();
    });

    it('closes dialog when backdrop is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const backdrop = screen.getByTestId('dialog-backdrop');
      await user.click(backdrop);

      expect(mockProps.onClose).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const dialog = screen.getByTestId('donation-head-dialog');
      expect(dialog).toHaveAttribute('role', 'dialog');
      expect(dialog).toHaveAttribute('aria-labelledby', 'dialog-title');
    });

    it('focuses on first input when opened', () => {
      renderWithProviders(<DonationHeadDialog {...mockProps} />, { user: mockUser });

      const firstInput = screen.getByTestId('donation-head-input');
      expect(firstInput).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles submission errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      const user = userEvent.setup();
      
      // Mock a failing submission
      const failingProps = {
        ...mockProps,
        fetchUsers: jest.fn().mockRejectedValue(new Error('API Error')),
      };

      renderWithProviders(<DonationHeadDialog {...failingProps} />, { user: mockUser });

      await user.click(screen.getByTestId('save-button'));

      // Should handle error without crashing
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();

      consoleError.mockRestore();
    });
  });
});
