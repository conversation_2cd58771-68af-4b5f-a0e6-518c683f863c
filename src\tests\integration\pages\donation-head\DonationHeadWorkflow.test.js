import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import { renderWithProviders, mockDonationHead, mockTenant, mockApiResponse } from '../../../utils/donationTestUtils';
import { setupAllMocks } from '../../../mocks/donationMocks';

// Setup mocks
const mockAxios = setupAllMocks();

// Mock the complete donation head workflow
const DonationHeadWorkflow = () => {
  const [donationHeads, setDonationHeads] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [openDialog, setOpenDialog] = React.useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = React.useState(false);
  const [selectedItem, setSelectedItem] = React.useState(null);
  const [searchKeyword, setSearchKeyword] = React.useState('');
  const [selectedFilters, setSelectedFilters] = React.useState([]);
  const [openAdvancedSearch, setOpenAdvancedSearch] = React.useState(false);

  const tenantsList = [
    { value: 'org-1', key: 'Organization 1' },
    { value: 'org-2', key: 'Organization 2' },
  ];

  // Simulate API calls
  const fetchDonationHeads = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/donation-heads');
      setDonationHeads(response.data);
    } catch (error) {
      console.error('Failed to fetch donation heads:', error);
    } finally {
      setLoading(false);
    }
  };

  const createDonationHead = async (data) => {
    try {
      await axios.post('/api/donation-heads', data);
      await fetchDonationHeads();
      setOpenDialog(false);
    } catch (error) {
      console.error('Failed to create donation head:', error);
    }
  };

  const updateDonationHead = async (id, data) => {
    try {
      await axios.patch(`/api/donation-heads/${id}`, data);
      await fetchDonationHeads();
      setOpenDialog(false);
    } catch (error) {
      console.error('Failed to update donation head:', error);
    }
  };

  const toggleDonationHeadStatus = async (item) => {
    try {
      if (item.isActive) {
        await axios.delete(`/api/donation-heads/${item.id}`);
      } else {
        await axios.patch(`/api/donation-heads/${item.id}`);
      }
      await fetchDonationHeads();
      setOpenDeleteDialog(false);
    } catch (error) {
      console.error('Failed to toggle status:', error);
    }
  };

  React.useEffect(() => {
    fetchDonationHeads();
  }, []);

  const handleSearch = (keyword) => {
    setSearchKeyword(keyword);
    // Simulate search filtering
    if (keyword) {
      const filtered = donationHeads.filter(head => 
        head.name.toLowerCase().includes(keyword.toLowerCase())
      );
      setDonationHeads(filtered);
    } else {
      fetchDonationHeads();
    }
  };

  const handleAdvancedSearch = (filters) => {
    setSelectedFilters(filters);
    setOpenAdvancedSearch(false);
    // Simulate advanced filtering
    fetchDonationHeads();
  };

  return (
    <div data-testid="donation-head-workflow">
      {/* Header */}
      <div data-testid="page-header">
        <h1>Donation Head Management</h1>
      </div>

      {/* Search and Actions */}
      <div data-testid="search-and-actions">
        <input
          data-testid="search-input"
          placeholder="Search donation heads..."
          value={searchKeyword}
          onChange={(e) => handleSearch(e.target.value)}
        />
        
        <button
          data-testid="advanced-search-btn"
          onClick={() => setOpenAdvancedSearch(true)}
        >
          Advanced Search
        </button>
        
        <button
          data-testid="add-btn"
          onClick={() => {
            setSelectedItem(null);
            setOpenDialog(true);
          }}
        >
          Add Donation Head
        </button>
      </div>

      {/* Active Filters */}
      {selectedFilters.length > 0 && (
        <div data-testid="active-filters">
          <h3>Active Filters:</h3>
          {selectedFilters.map((filter, index) => (
            <span key={index} data-testid={`filter-${index}`}>
              {filter.label}: {filter.value}
            </span>
          ))}
          <button
            data-testid="clear-filters-btn"
            onClick={() => setSelectedFilters([])}
          >
            Clear All
          </button>
        </div>
      )}

      {/* Data Grid */}
      <div data-testid="data-grid">
        {loading ? (
          <div data-testid="loading">Loading...</div>
        ) : (
          <div data-testid="donation-heads-list">
            {donationHeads.map((head) => (
              <div key={head.id} data-testid={`donation-head-${head.id}`}>
                <span data-testid={`name-${head.id}`}>{head.name}</span>
                <span data-testid={`description-${head.id}`}>{head.description}</span>
                <span data-testid={`status-${head.id}`}>
                  {head.isActive ? 'Active' : 'Inactive'}
                </span>
                
                <button
                  data-testid={`edit-btn-${head.id}`}
                  onClick={() => {
                    setSelectedItem(head);
                    setOpenDialog(true);
                  }}
                >
                  Edit
                </button>
                
                <button
                  data-testid={`toggle-status-btn-${head.id}`}
                  onClick={() => {
                    setSelectedItem(head);
                    setOpenDeleteDialog(true);
                  }}
                >
                  {head.isActive ? 'Deactivate' : 'Activate'}
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create/Edit Dialog */}
      {openDialog && (
        <div data-testid="donation-head-dialog" role="dialog">
          <h2>{selectedItem ? 'Edit Donation Head' : 'Add Donation Head'}</h2>
          
          <form
            data-testid="donation-head-form"
            onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              const data = {
                name: formData.get('name'),
                description: formData.get('description'),
                orgId: formData.get('orgId'),
              };
              
              if (selectedItem) {
                updateDonationHead(selectedItem.id, data);
              } else {
                createDonationHead(data);
              }
            }}
          >
            <input
              name="name"
              data-testid="name-input"
              placeholder="Donation Head Name"
              defaultValue={selectedItem?.name || ''}
              required
            />
            
            <textarea
              name="description"
              data-testid="description-input"
              placeholder="Description"
              defaultValue={selectedItem?.description || ''}
            />
            
            <select
              name="orgId"
              data-testid="org-select"
              defaultValue={selectedItem?.orgId || ''}
            >
              <option value="">Select Organization</option>
              {tenantsList.map((tenant) => (
                <option key={tenant.value} value={tenant.value}>
                  {tenant.key}
                </option>
              ))}
            </select>
            
            <button type="submit" data-testid="save-btn">
              {selectedItem ? 'Update' : 'Create'}
            </button>
            
            <button
              type="button"
              data-testid="cancel-btn"
              onClick={() => setOpenDialog(false)}
            >
              Cancel
            </button>
          </form>
        </div>
      )}

      {/* Delete/Toggle Status Dialog */}
      {openDeleteDialog && selectedItem && (
        <div data-testid="delete-dialog" role="dialog">
          <h2>Confirm Action</h2>
          <p data-testid="delete-message">
            Are you sure you want to {selectedItem.isActive ? 'deactivate' : 'activate'} {selectedItem.name}?
          </p>
          
          <button
            data-testid="confirm-delete-btn"
            onClick={() => toggleDonationHeadStatus(selectedItem)}
          >
            {selectedItem.isActive ? 'Deactivate' : 'Activate'}
          </button>
          
          <button
            data-testid="cancel-delete-btn"
            onClick={() => setOpenDeleteDialog(false)}
          >
            Cancel
          </button>
        </div>
      )}

      {/* Advanced Search Dialog */}
      {openAdvancedSearch && (
        <div data-testid="advanced-search-dialog" role="dialog">
          <h2>Advanced Search</h2>
          
          <form
            data-testid="advanced-search-form"
            onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.target);
              const filters = [];
              
              const name = formData.get('name');
              const description = formData.get('description');
              const orgId = formData.get('orgId');
              
              if (name) filters.push({ key: 'nameFilter', label: 'Name', value: name });
              if (description) filters.push({ key: 'descriptionFilter', label: 'Description', value: description });
              if (orgId) filters.push({ key: 'orgIdFilter', label: 'Organization', value: orgId });
              
              handleAdvancedSearch(filters);
            }}
          >
            <input
              name="name"
              data-testid="filter-name-input"
              placeholder="Filter by name"
            />
            
            <input
              name="description"
              data-testid="filter-description-input"
              placeholder="Filter by description"
            />
            
            <select name="orgId" data-testid="filter-org-select">
              <option value="">All Organizations</option>
              {tenantsList.map((tenant) => (
                <option key={tenant.value} value={tenant.value}>
                  {tenant.key}
                </option>
              ))}
            </select>
            
            <button type="submit" data-testid="apply-filters-btn">
              Apply Filters
            </button>
            
            <button
              type="button"
              data-testid="cancel-advanced-search-btn"
              onClick={() => setOpenAdvancedSearch(false)}
            >
              Cancel
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

describe('Donation Head Workflow Integration', () => {
  const mockDonationHeads = [
    mockDonationHead({
      id: '1',
      name: 'Education Fund',
      description: 'Education related donations',
      orgId: 'org-1',
      isActive: true,
    }),
    mockDonationHead({
      id: '2',
      name: 'Healthcare Fund',
      description: 'Healthcare related donations',
      orgId: 'org-2',
      isActive: false,
    }),
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockAxios.get.mockResolvedValue(mockApiResponse(mockDonationHeads));
    mockAxios.post.mockResolvedValue(mockApiResponse({ success: true }));
    mockAxios.patch.mockResolvedValue(mockApiResponse({ success: true }));
    mockAxios.delete.mockResolvedValue(mockApiResponse({ success: true }));
  });

  describe('Initial Load and Display', () => {
    it('loads and displays donation heads on mount', async () => {
      renderWithProviders(<DonationHeadWorkflow />);

      expect(screen.getByTestId('loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
        expect(screen.getByTestId('donation-head-2')).toBeInTheDocument();
      });

      expect(mockAxios.get).toHaveBeenCalledWith('/api/donation-heads');
    });

    it('displays donation head information correctly', async () => {
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByText('Education Fund')).toBeInTheDocument();
        expect(screen.getByText('Healthcare Fund')).toBeInTheDocument();
        expect(screen.getByText('Education related donations')).toBeInTheDocument();
        expect(screen.getByText('Healthcare related donations')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    it('performs basic search correctly', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId('search-input');
      await user.type(searchInput, 'Education');

      expect(searchInput).toHaveValue('Education');
    });

    it('opens advanced search dialog', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      const advancedSearchBtn = screen.getByTestId('advanced-search-btn');
      await user.click(advancedSearchBtn);

      expect(screen.getByTestId('advanced-search-dialog')).toBeInTheDocument();
    });

    it('applies advanced search filters', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      // Open advanced search
      await user.click(screen.getByTestId('advanced-search-btn'));

      // Fill filters
      await user.type(screen.getByTestId('filter-name-input'), 'Education');
      await user.selectOptions(screen.getByTestId('filter-org-select'), 'org-1');

      // Apply filters
      await user.click(screen.getByTestId('apply-filters-btn'));

      // Check that filters are applied
      await waitFor(() => {
        expect(screen.getByTestId('active-filters')).toBeInTheDocument();
        expect(screen.getByText('Name: Education')).toBeInTheDocument();
        expect(screen.getByText('Organization: org-1')).toBeInTheDocument();
      });
    });
  });

  describe('Create Donation Head Workflow', () => {
    it('opens create dialog and creates new donation head', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      // Open create dialog
      await user.click(screen.getByTestId('add-btn'));
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByText('Add Donation Head')).toBeInTheDocument();

      // Fill form
      await user.type(screen.getByTestId('name-input'), 'New Fund');
      await user.type(screen.getByTestId('description-input'), 'New description');
      await user.selectOptions(screen.getByTestId('org-select'), 'org-1');

      // Submit form
      await user.click(screen.getByTestId('save-btn'));

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith('/api/donation-heads', {
          name: 'New Fund',
          description: 'New description',
          orgId: 'org-1',
        });
      });
    });

    it('cancels create dialog', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await user.click(screen.getByTestId('add-btn'));
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();

      await user.click(screen.getByTestId('cancel-btn'));
      expect(screen.queryByTestId('donation-head-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Edit Donation Head Workflow', () => {
    it('opens edit dialog with pre-filled data', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
      });

      // Open edit dialog
      await user.click(screen.getByTestId('edit-btn-1'));
      expect(screen.getByTestId('donation-head-dialog')).toBeInTheDocument();
      expect(screen.getByText('Edit Donation Head')).toBeInTheDocument();

      // Check pre-filled values
      expect(screen.getByTestId('name-input')).toHaveValue('Education Fund');
      expect(screen.getByTestId('description-input')).toHaveValue('Education related donations');
    });

    it('updates donation head successfully', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
      });

      // Open edit dialog
      await user.click(screen.getByTestId('edit-btn-1'));

      // Modify data
      const nameInput = screen.getByTestId('name-input');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Education Fund');

      // Submit
      await user.click(screen.getByTestId('save-btn'));

      await waitFor(() => {
        expect(mockAxios.patch).toHaveBeenCalledWith('/api/donation-heads/1', {
          name: 'Updated Education Fund',
          description: 'Education related donations',
          orgId: 'org-1',
        });
      });
    });
  });

  describe('Delete/Toggle Status Workflow', () => {
    it('opens delete dialog for active item', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('toggle-status-btn-1'));
      expect(screen.getByTestId('delete-dialog')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to deactivate Education Fund?')).toBeInTheDocument();
    });

    it('deactivates active donation head', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-1')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('toggle-status-btn-1'));
      await user.click(screen.getByTestId('confirm-delete-btn'));

      await waitFor(() => {
        expect(mockAxios.delete).toHaveBeenCalledWith('/api/donation-heads/1');
      });
    });

    it('activates inactive donation head', async () => {
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(screen.getByTestId('donation-head-2')).toBeInTheDocument();
      });

      await user.click(screen.getByTestId('toggle-status-btn-2'));
      await user.click(screen.getByTestId('confirm-delete-btn'));

      await waitFor(() => {
        expect(mockAxios.patch).toHaveBeenCalledWith('/api/donation-heads/2');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxios.get.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<DonationHeadWorkflow />);

      await waitFor(() => {
        expect(consoleError).toHaveBeenCalledWith('Failed to fetch donation heads:', expect.any(Error));
      });

      consoleError.mockRestore();
    });

    it('handles create errors gracefully', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockAxios.post.mockRejectedValue(new Error('Create Error'));
      
      const user = userEvent.setup();
      renderWithProviders(<DonationHeadWorkflow />);

      await user.click(screen.getByTestId('add-btn'));
      await user.type(screen.getByTestId('name-input'), 'New Fund');
      await user.click(screen.getByTestId('save-btn'));

      await waitFor(() => {
        expect(consoleError).toHaveBeenCalledWith('Failed to create donation head:', expect.any(Error));
      });

      consoleError.mockRestore();
    });
  });
});
