// ** MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useState } from 'react'

// ** Custom Components Imports
import AccordionBasic from 'src/@core//components/custom-components/AccordionBasic'

// ** Demo Components Imports
import Section6 from './Section6'
import { useTheme } from '@emotion/react'
import MUITableCell from "src/pages/SP/MUITableCell";
import { Table, TableBody, TableContainer, TableRow } from '@mui/material'
import { useRBAC } from "src/pages/permission/RBACContext";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const OtherDetails = ({data,expanded}) => {

  const { can } = useRBAC();

    // ** Hook
  const theme = useTheme()

  const [state6, setState6] = useState(true)

  const handleState6 = () => {
    setState6(!state6)
  }
    
    // Pre-Populating code Start
    // const [otherDetails, setOtherDetails] = useState({
    //     professionalDetails:"",
    //     redevelopment:"",
    //     documentsAvailable:""
    //   });

    return (
        <>
        {/* {can('society_otherDetails_READ') && */}
         <AccordionBasic
                id={'panel-header-2'}
                ariaControls={'panel-content-2'}
                heading={'Other Details'}
                body={
                  <>
                    {state6 && (
                     <TableContainer sx={{ padding:'4px 6px', cursor: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' width=\'24\' height=\'24\' fill=\'currentColor\'%3E%3Cpath d=\'M11.423 1A3.577 3.577 0 0 1 15 4.577c0 .27-.108.53-.3.722l-.528.529l-1.971 1.971l-5.059 5.059a3 3 0 0 1-1.533.82l-2.638.528a1 1 0 0 1-1.177-1.177l.528-2.638a3 3 0 0 1 .82-1.533l5.059-5.059l2.5-2.5c.191-.191.451-.299.722-.299m-2.31 4.009l-4.91 4.91a1.5 1.5 0 0 0-.41.766l-.38 1.903l1.902-.38a1.5 1.5 0 0 0 .767-.41l4.91-4.91a2.08 2.08 0 0 0-1.88-1.88m3.098.658a3.6 3.6 0 0 0-1.878-1.879l1.28-1.28c.995.09 1.788.884 1.878 1.88z\'/%3E%3C/svg%3E") 12 12, pointer'  }}
                            className='tableBody'
                            //onClick={can('society_otherDetails_UPDATE') ? handleState6 : null}>
                            onClick={ handleState6}>
                            <Table>
                              <TableBody sx={{ '& .MuiTableCell-root': { p: `${theme.spacing(1.35, 1.125)} !important` } }}>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Do you have following Documents?</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.documentsAvailable}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Professional Details:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.professionalDetails}</Typography>
                                  </MUITableCell>
                                </TableRow>
                                <TableRow>
                                  <MUITableCell>
                                    <Typography style={field}>Have You taken the steps towards redevelopment?
                                      If yes, Elaborate:</Typography>
                                  </MUITableCell>
                                  <MUITableCell>
                                    <Typography className='data-field' >{data?.redevelopment}</Typography>
                                  </MUITableCell>
                                </TableRow>

                              </TableBody>
                            </Table>
                          </TableContainer>
                        
                    )}
                    {!state6 && <Section6 formData={data} onCancel={handleState6} />}
                  </>
                }
                expanded={expanded}
              />
        {/* } */}
        </>
    );

}
export default OtherDetails;