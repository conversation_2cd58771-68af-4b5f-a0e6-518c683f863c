import React from 'react';
import { render } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';

// Mock theme for Material-UI components
const mockTheme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      background: '#f5f5f5',
    },
    secondary: {
      main: '#dc004e',
    },
    info: {
      main: '#0288d1',
    },
    error: {
      main: '#f44336',
    },
    text: {
      primary: '#000000',
      secondary: '#666666',
    },
    divider: '#e0e0e0',
  },
  spacing: (factor) => `${0.25 * factor}rem`,
});

// Mock AuthContext provider
export const MockAuthProvider = ({ children, user = null, listValues = [] }) => {
  const mockAuthValue = {
    user: user || {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      organisationCategory: 'TENANT',
      orgId: 'org-1',
    },
    listValues,
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
  };

  return (
    <div data-testid="mock-auth-provider">
      {React.cloneElement(children, { authContext: mockAuthValue })}
    </div>
  );
};

// Mock RBAC provider
export const MockRBACProvider = ({ children, permissions = [] }) => {
  const mockRBACValue = {
    canAccessActions: jest.fn(() => true),
    canAccessHeads: jest.fn(() => true),
    permissions,
  };

  return (
    <div data-testid="mock-rbac-provider">
      {React.cloneElement(children, { rbacContext: mockRBACValue })}
    </div>
  );
};

// Custom render function with providers
export const renderWithProviders = (
  ui,
  {
    user = null,
    listValues = [],
    permissions = [],
    ...renderOptions
  } = {}
) => {
  const Wrapper = ({ children }) => (
    <ThemeProvider theme={mockTheme}>
      <MockAuthProvider user={user} listValues={listValues}>
        <MockRBACProvider permissions={permissions}>
          {children}
        </MockRBACProvider>
      </MockAuthProvider>
    </ThemeProvider>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock data generators
export const mockDonationHead = (overrides = {}) => ({
  id: '1',
  name: 'Test Donation Head',
  description: 'Test description',
  orgId: 'org-1',
  isActive: true,
  createdOn: '2024-01-01T00:00:00Z',
  updatedOn: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockTenant = (overrides = {}) => ({
  value: 'org-1',
  key: 'Test Organization',
  label: 'Test Organization',
  ...overrides,
});

export const mockDonationReceipt = (overrides = {}) => ({
  id: '1',
  orgId: 'org-1',
  donorId: 'donor-1',
  donationTypeId: 'type-1',
  donationHeadId: 'head-1',
  receiptDate: '2024-01-01',
  metaData: {
    amount: 1000,
    paymentMode: 'online',
    paymentType: 'credit_card',
    paymentDetails: 'Test payment',
    reference: 'REF123',
    additionalNotes: 'Test notes',
  },
  ...overrides,
});

export const mockStatsData = (overrides = {}) => ({
  totalDonations: 512986,
  uniqueDonors: 20,
  last30DaysDonations: 0,
  averageDonation: 25649,
  donationHeadsCount: 5,
  currentPeriod: 100,
  topHead: 'Education',
  ...overrides,
});

// Mock API responses
export const mockApiResponse = (data, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
});

// Mock axios instance
export const createMockAxios = () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn(),
});

// Mock form data
export const mockFormData = {
  donationHead: 'Test Donation Head',
  description: 'Test description',
  tenantName: 'org-1',
  amount: '1000',
  donationDate: '2024-01-01',
  paymentDetails: 'Test payment',
  reference: 'REF123',
  additionalNotes: 'Test notes',
};

// Mock chart data
export const mockChartData = {
  series: [{ name: 'Test Series', data: [100, 200, 300, 400, 500] }],
  categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
};

// Mock distribution data
export const mockDistributionData = {
  series: [4400, 5500, 3500, 4300, 2400],
  labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'],
};

// Test helpers
export const waitForLoadingToFinish = async () => {
  await new Promise(resolve => setTimeout(resolve, 100));
};

export const mockConsoleError = () => {
  const originalError = console.error;
  console.error = jest.fn();
  return () => {
    console.error = originalError;
  };
};

// Mock Material-UI components for testing
export const mockMuiComponents = () => {
  jest.mock('@mui/material', () => ({
    ...jest.requireActual('@mui/material'),
    useTheme: () => mockTheme,
  }));

  jest.mock('@mui/x-data-grid', () => ({
    DataGrid: ({ rows, columns, ...props }) => (
      <div data-testid="data-grid" data-rows={JSON.stringify(rows)} data-columns={JSON.stringify(columns)} {...props}>
        Mock DataGrid
      </div>
    ),
  }));
};
