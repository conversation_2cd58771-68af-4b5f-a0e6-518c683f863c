// ** MUI Imports
import Typography from "@mui/material/Typography";
import { useState } from "react";

// ** Custom Components Imports
import AccordionBasic from "src/@core//components/custom-components/AccordionBasic";

// ** Demo Components Imports
import { useTheme } from "@emotion/react";


// ** Styled Component
import { Table, TableBody, TableContainer, TableRow } from "@mui/material";

import MUITableCell from "src/pages/SP/MUITableCell";
import ShortFormEdit from "./ShortFormEdit";


const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ShortFormView = ({ data, expanded }) => {
  // ** Hook
  const theme = useTheme();

  const [state, setState] = useState("view");

  const viewClick = () => {
    setState("edit");
  };

  const editClick = () => {
    setState("view");
  };

  return (
    <>
      <AccordionBasic
        id={"panel-header-1"}
        ariaControls={"panel-content-1"}
        heading={"Basic Details"}
        body={
          <>
            {state === "view" && (
              <TableContainer
                sx={{ padding: "4px 6px" }}
                className="tableBody"
                onClick={viewClick}
              >
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    {" "}
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>First Name</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.firstName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Last Name:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.lastName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Mobile Number:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.mobileNumber}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Email:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.email}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Entity Category:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.entityCategory}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell>
                        <Typography style={field}>Entity Type:</Typography>
                      </MUITableCell>
                      <MUITableCell>
                        <Typography className="data-field">
                          {data?.entityType}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
            
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            {state === "edit" && (
              <ShortFormEdit  onCancel={editClick} formData={data}  />
            )}
          </>
        }
        expanded={expanded}
      />
    </>
  );
};
export default ShortFormView;
