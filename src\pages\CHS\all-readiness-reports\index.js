import { DataGrid } from "@mui/x-data-grid";
import { useEffect, useState } from "react";

import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import {
  Card,
  CardContent,
  Divider,
  Grid,
  Link,
  Menu,
  MenuItem,
  Tooltip,
  Typography,
} from "@mui/material";
import Icon from "src/@core/components/icon";
import { useAuth } from "src/hooks/useAuth";

import axios from "axios";
import CustomAvatar from "src/@core/components/mui/avatar";

import { useForm } from "react-hook-form";

import { Box } from "@mui/system";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import SampleReadiness from "./ViewDialog";

const AllReadinessReports = () => {
  
  const [expanded, setExpanded] = useState(true);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  const [openViewDialog, setOpenViewDialog] = useState(false);

  const [openEditDialog, setOpenEditDialog] = useState(false);
  // Constants
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [menu, setMenu] = useState(null);
  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  // Define columns
  const columns = [
    {
      field: "societyName",
      minWidth: 135,
      headerName: "Society Name",
      flex: 0.19,
      valueGetter: (params) =>
        params.row?.readiness?.needAssessment?.societyName,
      renderCell: (params) => (
        <Tooltip title={params.row?.readiness?.readinessReport?.needAssessment?.societyName}>
          <span>{params.row?.readiness?.readinessReport?.needAssessment?.societyName}</span>
        </Tooltip>
      ),
    },
    {
      field: "name",
      minWidth: 90,
      headerName: "Name",
      flex: 0.19,
      valueGetter: (params) => {
        const { firstName, lastName } = params.row;
        return `${firstName} ${lastName ? lastName : ""}`.trim();
      },
      renderCell: (params) => {
        const { firstName, lastName } = params.row;
        const name = `${firstName} ${lastName ? lastName : ""}`.trim();
        return (
          <Tooltip title={name}>
            <span>{name}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "email",
      minWidth: 135,
      headerName: "Email",
      flex: 0.15,
      renderCell: (params) => {
        const email = params?.value;
        
        return email.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "mobileNumber",
      minWidth: 135,
      headerName: "Contact Number",
      flex: 0.15,
      renderCell: (params) => {
        const mobileNumber = params?.value;
    
        return (
          <Tooltip title={mobileNumber}>
            <Link
              href={`tel:${mobileNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              {mobileNumber}
            </Link>
          </Tooltip>
        );
      },
    },
    
    {
      flex: 0.077,
      field: "edit",
      headerName: "Actions",
      sortable: false,
      minWidth: 120,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
        };

        const onClickViewProfile = () => {
          setOpenViewDialog(true);
          handleCloseMenuItems();
        };
        const onClickEditProfile = () => {
          setOpenEditDialog(true);
          handleCloseMenuItems();
        };

        const onClickDeleteProfile = () => {
          // setOpenDeActivateDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          // setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{
                  mr: { xs: 2, lg: 4 },
                  width: 34,
                  height: 34,
                  cursor: "pointer",
                }}
                onClick={handleClickMenu}
              >
                <Icon icon="bi:three-dots-vertical" />
              </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>View</MenuItem>
              <MenuItem onClick={onClickEditProfile}>Edit</MenuItem>
              <MenuItem onClick={onClickDeleteProfile}>
                Activate/DeActivate
              </MenuItem>
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  // Use States
  const {
    register,
    handleSubmit,
    setError,
    control,
    reset,
    clearErrors,
    formState: { errors },
  } = useForm();

  const auth = useAuth();

  const [readinessList, setReadinessList] = useState([]);

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");

  const [openDialog, setOpenDialog] = useState(false);

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    const url = getUrl(authConfig.allReadinessReportsEndpoint);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setReadinessList(response.data?.readinessResponses || []);

        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const handleCloseViewDialog = () => {
    reset();
    setOpenViewDialog(false);
  };

  const handleCloseEditDialog = () => {
    reset();
    setOpenEditDialog(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessReadinessReports = (requiredPermission) =>
    canMenuPageSection(MENUS.LEFT, PAGES.REDEVELOPMENT, PAGES.CHS_READINESS_REPORTS , requiredPermission);
  

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessReadinessReports(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if(canAccessReadinessReports(PERMISSIONS.READ)) {
    return (
      <>
        <div>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={12}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                    <Typography variant="h6">
                      List of Readiness Reports
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider/>
          <CardContent>
            <div style={{ height: 380, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={readinessList || []}
                  columns={columns}
                  getRowId={(row) => row.userId}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={
                    readinessList?.length > 0 ? rowsPerPageOptions : []
                  }
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              )}
            </div>
          </CardContent>
        </div>
        <SampleReadiness
          open={openViewDialog}
          onClose={handleCloseViewDialog}
          title={"View"}
        />

        <SampleReadiness
          open={openEditDialog}
          onClose={handleCloseEditDialog}
          title={"Edit"}
        />
      </>
    );
  } else {
    return null;
  }
};

export default AllReadinessReports;
