import dynamic from 'next/dynamic';

const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const PieChart = () => {
  const state = {
    series: [65, 30, 5], // Example data values for Service Providers, Societies, and Employees
    options: {
      chart: {
        type: 'pie',
      },
      labels: ['Service Providers', 'Societies', 'Employees'], // Updated labels
      responsive: [
        {
          breakpoint: 1024, // Tablet breakpoint (can be adjusted as needed)
          options: {
            chart: {
              width: '90%', // Adjust width for tablet view
              height: 400,  // Increase height for tablet view
            },
          },
        },
        {
          breakpoint: 768, // Small tablet and large mobile view
          options: {
            chart: {
              width: '90%', // Adjust width for smaller tablets
              height: 350, 
            },
            legend: {
              position: 'bottom',
              fontSize: '12px', 
            }
          }
        },
        {
          breakpoint: 480, // Mobile breakpoint
          options: {
            chart: {
              width: '90%',  // Set width to 100% for mobile view
              height: 300,   // Reduce height for mobile view
              offsetX: 10, 
            },
            legend: {
              position: 'bottom',
              fontSize: '10px', 
            }
          }
        }
      ],
      legend: {
        position: 'right',
        offsetY: 0,
        itemMargin: {
          horizontal: 5,
          vertical: 5
        }
      },
      title: {
        text: 'Distribution of Actors in the Application',
        align: 'center',
      },
      colors: ['#FF4560', '#00E396', '#008FFB'],
      plotOptions: {
        pie: {
          expandOnClick: true
        }
      }
    },
  };

  return (
    <div style={{ overflow: 'hidden' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="pie" />
      </div>
    </div>
  );
};

export default PieChart;
